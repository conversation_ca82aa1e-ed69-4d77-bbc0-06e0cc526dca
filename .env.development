# ===========================================
# 开发环境配置
# ===========================================

# 应用基础配置
NODE_ENV=development
PORT=3000
NEXT_TELEMETRY_DISABLED=1

# MongoDB 配置（开发环境）
MONGODB_URI=mongodb://n28.i.erlitech.com:27017
MONGODB_DB_NAME=tractor_data
MONGODB_USERNAME=root
MONGODB_PASSWORD=1q2w3e4R
MONGODB_AUTH_SOURCE=admin
MONGODB_MAX_POOL_SIZE=5
MONGODB_MIN_POOL_SIZE=1
MONGODB_CONNECT_TIMEOUT=10000
MONGODB_SERVER_SELECTION_TIMEOUT=5000

# 构建配置（开发环境 - 快速构建）
BUILD_MODE=development
BUILD_STATIC=false
ANALYZE=false

# 日志配置（开发环境 - 详细日志）
LOG_LEVEL=debug
LOG_FILE_PATH=./logs
LOG_CONSOLE=true
LOG_FILE=true

# 缓存配置（开发环境 - 短缓存时间）
CACHE_REVALIDATE_TIME=60
CACHE_ENABLED=true

# 站点配置
SITE_URL=http://localhost:3000
SITE_NAME=TractorData (Dev)
DEFAULT_LOCALE=en
SUPPORTED_LOCALES=en,fr,zh,es,de,pt

# 安全配置（开发环境 - 宽松配置）
API_RATE_LIMIT=1000
CORS_ORIGIN=*
SECURITY_HEADERS_ENABLED=true
IP_SALT=dev-salt-key-2024

# 性能配置（开发环境）
IMAGES_UNOPTIMIZED=true
COMPRESS_ENABLED=false

# 开发工具配置
TURBOPACK_ENABLED=true
SKIP_TYPE_CHECK=true
SKIP_ESLINT=true

# PM2 配置（开发环境 - 少量实例）
PM2_INSTANCES=2
PM2_MAX_MEMORY=512M
PM2_APP_NAME=tractor-data-web-dev

# 备份配置
BACKUP_DIR=./backups
AUTO_BACKUP_ENABLED=false
BACKUP_RETENTION_DAYS=3
