// 容器专用的PM2配置
// 简化配置，避免pidusage相关问题

module.exports = {
  apps: [{
    name: 'tractor-data-web',
    script: 'start-server.js',
    instances: 1,
    exec_mode: 'fork',
    
    // 基础配置
    cwd: '/app',
    
    // 环境变量
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      NEXT_TELEMETRY_DISABLED: '1',
      HOSTNAME: '0.0.0.0'
    },
    
    // 性能优化 - 平衡的内存配置
    node_args: '--max-old-space-size=512',  // 恢复到合理的堆内存限制
    max_memory_restart: '700M',              // 合理的重启阈值
    
    // 日志配置 - 使用相对路径和更安全的配置
    log_file: '/app/logs/combined.log',
    out_file: '/app/logs/out.log',
    error_file: '/app/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    log_type: 'json',
    
    // 进程管理 - 简化配置
    autorestart: true,
    watch: false,
    max_restarts: 3,
    min_uptime: '10s',
    restart_delay: 2000,
    
    // 启动配置
    wait_ready: true,
    listen_timeout: 30000,
    kill_timeout: 5000,
    
    // 禁用可能导致问题的功能
    pmx: false,
    monitoring: false,
    disable_logs: false,
    
    // 容器优化
    ignore_watch: ['node_modules', 'logs', '.next'],

    // 错误处理
    combine_logs: true,
    
    // 时间配置
    time: true
  }]
};
