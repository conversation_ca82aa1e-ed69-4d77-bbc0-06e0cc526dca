#!/usr/bin/env node

/**
 * 启动服务器脚本
 * 适用于Next.js standalone模式和PM2管理
 */

const { createServer } = require('http');
const { parse } = require('url');
const path = require('path');

// 加载环境变量
try {
  require('dotenv').config();
} catch (error) {
  console.log('dotenv not available, using system environment variables');
}

// 检查并创建日志目录
const fs = require('fs');

const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  try {
    fs.mkdirSync(logsDir, { recursive: true, mode: 0o755 });
    console.log('Created logs directory');
  } catch (error) {
    console.log('Warning: Could not create logs directory:', error.message);
  }
}

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || '0.0.0.0';
const port = parseInt(process.env.PORT || '3000', 10);

console.log(`Starting server in ${dev ? 'development' : 'production'} mode`);
console.log(`Server will listen on ${hostname}:${port}`);

// 检查是否存在Next.js standalone server
const standaloneServerPath = path.join(__dirname, 'server.js');

if (fs.existsSync(standaloneServerPath) && !dev) {
  // 如果存在standalone server且在生产环境，直接使用它
  console.log('Using Next.js standalone server');
  require(standaloneServerPath);
} else {
  // 否则使用Next.js开发服务器或自定义服务器
  console.log('Using Next.js custom server');
  
  const next = require('next');
  const app = next({ dev, hostname, port });
  const handle = app.getRequestHandler();

  app.prepare().then(() => {
    createServer(async (req, res) => {
      try {
        // 解析URL
        const parsedUrl = parse(req.url, true);
        
        // 处理请求
        await handle(req, res, parsedUrl);
      } catch (err) {
        console.error('Error occurred handling', req.url, err);
        res.statusCode = 500;
        res.end('Internal server error');
      }
    })
    .listen(port, hostname, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port}`);
      
      // 通知PM2进程已准备就绪
      if (process.send) {
        process.send('ready');
      }
    });
  }).catch((err) => {
    console.error('Error starting server:', err);
    process.exit(1);
  });
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});
