- seo 友好是必须的，优先考虑
- 最小化使用 use client
- 需要适配移动端
- 每次完成任务后，不用帮忙重启开发服务器
- 尽量拆分合理的组件进行开发，属于页面的组件放在当前页面目录下
- 如果需要图片，图片使用网络公共图片 https://picsum.photos
- 如果需要图标，请自己绘制 svg, 并放文件夹内 public/icons 下
- 注意 clean code
- 良好的，美观的，现代化 UI 及交互
- next-intl 当前版本是4.0， 你写相关的代码时候请注意，https://next-intl.dev/blog/next-intl-4-0
对于服务器组件，next-intl提供了两种使用方式：
对于异步组件（async components）：使用getTranslations等异步API
对于非异步组件（non-async components）：可以直接使用useTranslations等hooks
特别重要的是非异步组件部分的说明：
   > If you import useTranslations, useFormatter, useLocale, useNow and useTimeZone from a shared component, next-intl will automatically provide an implementation that works best for the environment this component executes in (server or client).
   这意味着对于普通的非异步服务器组件，可以直接使用useTranslations而无需标记为'use client'。
非异步组件可以是"共享组件"，这意味着它们可以在服务器或客户端环境中运行，next-intl会自动提供适合当前环境的实现。
- 以上都是通用的规范，并不是每次修改都要遵循，根据实际情况来定
