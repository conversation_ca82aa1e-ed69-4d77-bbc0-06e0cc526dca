# 拖拉机展示网站 (Tractor Web)

这是一个使用 [Next.js](https://nextjs.org) 构建的拖拉机信息展示网站，为用户提供拖拉机品牌、型号、新闻和相关内容的综合平台。

## 项目特点

- **多语言支持**: 使用 next-intl 实现中文、英文和德文的国际化支持
- **响应式设计**: 完全适配移动端和桌面端的现代界面
- **拖拉机品牌目录**: 展示农用拖拉机和草坪拖拉机的主要品牌
- **产品详情**: 提供各种型号拖拉机的详细规格和信息
- **行业新闻**: 最新的拖拉机产业新闻和发展动态
- **SEO友好**: 针对搜索引擎优化的页面结构和内容

## 技术栈

- **框架**: Next.js 15.2.5 (App Router)
- **UI组件**: shadcn UI 组件库
- **样式**: Tailwind CSS
- **国际化**: next-intl v4.0
- **状态管理**: React Hooks
- **动画**: Framer Motion
- **数据库**: MongoDB
- **服务端API**: Next.js API Routes
- **进程管理**: PM2
- **部署方式**: 私有服务器部署

## 开始使用

首先，安装依赖:

```bash
npm install
```

然后，运行开发服务器:

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看网站。

## 项目结构

```
src/
├── app/
│   └── [locale]/         # 多语言路由
│       ├── brands/       # 品牌页面
│       ├── components/   # 通用组件
│       ├── farm-tractors/# 农用拖拉机页面
│       ├── lawn-garden-tractors/ # 草坪拖拉机页面
│       ├── news/         # 新闻页面
│       ├── search/       # 搜索功能
│       ├── tractors/     # 拖拉机页面
│       └── compare/      # 比较功能
├── components/           # 全局共享组件
├── i18n/                 # 国际化配置
├── lib/                  # 工具库
│   └── mongodb.ts        # MongoDB连接配置
├── services/             # API服务
│   ├── brandService.ts   # 品牌数据服务
│   └── tractorService.ts # 拖拉机数据服务
└── utils/                # 实用工具函数
```

## 数据查询

本项目使用MongoDB作为数据存储，主要数据集合包括：

- **brands**: 存储拖拉机品牌信息，包括品牌名称、介绍、官网等
- **tractors**: 存储拖拉机型号详细信息，包括规格、引擎、传动系统等数据
- **models**: 存储拖拉机型号的图片和附加信息

数据访问层采用服务模式设计：

- **brandService**: 提供品牌数据查询功能，包括获取所有品牌列表和单个品牌详情
- **tractorService**: 提供拖拉机数据查询功能，获取单个拖拉机型号的详细信息

示例查询：

```typescript
// 获取所有农用拖拉机品牌
const farmBrands = await getAllBrands('farm');

// 获取特定品牌详情
const brandDetails = await getBrandById('john-deere');

// 获取特定拖拉机型号详情
const tractorData = await getTractorData('john-deere', '8R-410');
```

## 开发规范

- SEO友好是必须的，优先考虑
- 最小化使用 `use client`
- 优先使用组件库 shadcn
- 需要适配移动端
- 尽量拆分合理的组件进行开发，属于页面的组件放在当前页面目录下
- 所有图片使用网络公共图片 https://picsum.photos
- 图标放置于 public/icons 目录下
- 遵循 clean code 原则
- 使用 next-intl 4.0 版本实现国际化

## 部署

本项目采用私有服务器部署，使用PM2进行进程管理：

### 环境配置

首先配置环境变量：

```bash
# 复制配置模板
cp .env.example .env.local

# 编辑配置文件，填入实际的数据库连接信息
nano .env.local
```

### 一键部署

```bash
# 完整部署流程（推荐）
npm run deploy

# 或者分步部署
npm run build
npm run sitemap
npm run seo:optimize
npm run pm2:start
```

### 常用管理命令

```bash
# 查看服务状态
npm run pm2:status

# 重启服务
npm run pm2:restart

# 停止服务
npm run pm2:stop

# 检查数据库连接
npm run check-db
```

详细的部署指南请参考 [DEPLOYMENT_GUIDE.md](docs/DEPLOYMENT_GUIDE.md)。

## 了解更多

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 功能和 API
- [Next.js 教程](https://nextjs.org/learn) - 交互式 Next.js 教程
- [项目部署指南](docs/DEPLOYMENT_GUIDE.md) - 详细的部署说明
- [环境配置指南](docs/ENVIRONMENT_CONFIG_GUIDE.md) - 环境变量配置说明
