import Link from "next/link";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface SimplePaginationControlsProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  previousText?: string;
  nextText?: string;
}

/**
 * 简单的分页控件组件，支持国际化
 * 
 * @param currentPage 当前页码
 * @param totalPages 总页数
 * @param baseUrl 基础URL，会自动附加页码参数
 * @param previousText 上一页文本，可选
 * @param nextText 下一页文本，可选
 */
export function SimplePaginationControls({
  currentPage,
  totalPages,
  baseUrl,
  previousText,
  nextText,
}: SimplePaginationControlsProps) {
  if (totalPages <= 1) return null;

  // 准备页码数组
  let pages: number[] = [];

  // 如果页数较少，全部显示
  if (totalPages <= 5) {
    pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    // 复杂情况：显示首页、尾页、当前页及前后页
    const firstPage = 1;
    const lastPage = totalPages;

    if (currentPage <= 3) {
      // 还在前面，显示1-4和最后
      pages = [1, 2, 3, 4, lastPage];
    } else if (currentPage >= totalPages - 2) {
      // 已经接近尾部，显示第一页和最后几页
      pages = [firstPage, totalPages - 3, totalPages - 2, totalPages - 1, lastPage];
    } else {
      // 在中间位置，显示第一页、当前页及前后一页、最后一页
      pages = [firstPage, currentPage - 1, currentPage, currentPage + 1, lastPage];
    }
  }

  // 生成分页链接
  const getPageUrl = (page: number) => {
    const separator = baseUrl.includes("?") ? "&" : "?";
    return `${baseUrl}${separator}page=${page}`;
  };

  // 检查页码连续性，决定是否显示省略号
  const shouldShowEllipsis = (current: number, next: number) => {
    return next - current > 1;
  };

  return (
    <Pagination className="mt-8">
      <PaginationContent>
        {/* 上一页按钮 */}
        <PaginationItem>
          <PaginationPrevious
            href={currentPage > 1 ? getPageUrl(currentPage - 1) : "#"}
            className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
            aria-label={previousText || "Previous"}
          >
            {previousText || "Previous"}
          </PaginationPrevious>
        </PaginationItem>

        {/* 页码列表 */}
        {pages.map((page, index) => {
          // 检查是否需要在此页码前添加省略号
          const showEllipsisBefore =
            index > 0 && shouldShowEllipsis(pages[index - 1], page);

          return (
            <div key={page} className="flex items-center">
              {showEllipsisBefore && (
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
              )}
              <PaginationItem>
                <PaginationLink
                  href={getPageUrl(page)}
                  isActive={page === currentPage}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            </div>
          );
        })}

        {/* 下一页按钮 */}
        <PaginationItem>
          <PaginationNext
            href={
              currentPage < totalPages ? getPageUrl(currentPage + 1) : "#"
            }
            className={
              currentPage >= totalPages ? "pointer-events-none opacity-50" : ""
            }
            aria-label={nextText || "Next"}
          >
            {nextText || "Next"}
          </PaginationNext>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
