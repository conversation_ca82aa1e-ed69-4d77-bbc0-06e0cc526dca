'use client';

import JsonLd from './JsonLd';

interface ArticleSchemaProps {
  title: string;
  description?: string;
  content?: string;
  author?: {
    name: string;
    url?: string;
  };
  publishedDate: string;
  modifiedDate?: string;
  image?: string;
  url?: string;
  category?: string;
  tags?: string[];
  baseUrl?: string;
}

/**
 * 文章结构化数据组件
 * 生成符合Google规范的Article结构化数据
 */
export default function ArticleSchema({
  title,
  description,
  content,
  author,
  publishedDate,
  modifiedDate,
  image,
  url,
  category = 'Agricultural News',
  tags = [],
  baseUrl = 'https://tractordata.site'
}: ArticleSchemaProps) {
  
  const articleSchema: any = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description || title,
    "datePublished": publishedDate,
    "dateModified": modifiedDate || publishedDate,
    "publisher": {
      "@type": "Organization",
      "name": "TractorData",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.svg`
      }
    }
  };

  // 添加作者信息
  if (author) {
    articleSchema.author = {
      "@type": "Person",
      "name": author.name,
      ...(author.url && { "url": author.url })
    };
  } else {
    // 默认作者为网站
    articleSchema.author = {
      "@type": "Organization",
      "name": "TractorData",
      "url": baseUrl
    };
  }

  // 添加文章URL
  if (url) {
    articleSchema.url = url.startsWith('http') ? url : `${baseUrl}${url}`;
    articleSchema.mainEntityOfPage = articleSchema.url;
  }

  // 添加图片
  if (image) {
    const imageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`;
    articleSchema.image = {
      "@type": "ImageObject",
      "url": imageUrl,
      "width": 1200,
      "height": 630
    };
  }

  // 添加文章正文（如果提供）
  if (content) {
    articleSchema.articleBody = content;
    
    // 计算字数（估算阅读时间）
    const wordCount = content.split(/\s+/).length;
    const readingTimeMinutes = Math.ceil(wordCount / 200); // 假设每分钟200字
    
    articleSchema.wordCount = wordCount;
    articleSchema.timeRequired = `PT${readingTimeMinutes}M`;
  }

  // 添加分类
  if (category) {
    articleSchema.articleSection = category;
  }

  // 添加标签
  if (tags.length > 0) {
    articleSchema.keywords = tags.join(', ');
  }

  // 添加语言
  articleSchema.inLanguage = 'en'; // 可以根据locale动态设置

  return <JsonLd data={articleSchema} />;
}

/**
 * 为新闻文章生成结构化数据的辅助函数
 */
export function generateNewsArticleSchema(newsData: any, locale: string = 'en') {
  if (!newsData) return null;

  return {
    title: newsData.title,
    description: newsData.summary || newsData.excerpt,
    content: newsData.content,
    author: newsData.author ? {
      name: newsData.author,
      url: newsData.author_url
    } : undefined,
    publishedDate: newsData.published_date || newsData.date,
    modifiedDate: newsData.updated_date || newsData.modified_date,
    image: newsData.featured_image || newsData.image,
    url: `/${locale === 'en' ? '' : locale + '/'}news/${newsData.slug}`,
    category: newsData.category || 'Agricultural News',
    tags: newsData.tags || []
  };
}

/**
 * FAQ结构化数据组件
 */
interface FAQSchemaProps {
  questions: Array<{
    question: string;
    answer: string;
  }>;
}

export function FAQSchema({ questions }: FAQSchemaProps) {
  if (!questions || questions.length === 0) {
    return null;
  }

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": questions.map(qa => ({
      "@type": "Question",
      "name": qa.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": qa.answer
      }
    }))
  };

  return <JsonLd data={faqSchema} />;
}

/**
 * How-to结构化数据组件
 */
interface HowToSchemaProps {
  name: string;
  description?: string;
  steps: Array<{
    name: string;
    text: string;
    image?: string;
  }>;
  totalTime?: string;
  estimatedCost?: string;
  supply?: string[];
  tool?: string[];
}

export function HowToSchema({
  name,
  description,
  steps,
  totalTime,
  estimatedCost,
  supply = [],
  tool = []
}: HowToSchemaProps) {
  const howToSchema: any = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": name,
    "description": description || name,
    "step": steps.map((step, index) => ({
      "@type": "HowToStep",
      "position": index + 1,
      "name": step.name,
      "text": step.text,
      ...(step.image && {
        "image": step.image
      })
    }))
  };

  if (totalTime) howToSchema.totalTime = totalTime;
  if (estimatedCost) howToSchema.estimatedCost = estimatedCost;
  if (supply.length > 0) howToSchema.supply = supply;
  if (tool.length > 0) howToSchema.tool = tool;

  return <JsonLd data={howToSchema} />;
}
