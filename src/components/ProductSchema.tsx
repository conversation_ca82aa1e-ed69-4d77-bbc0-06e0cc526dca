'use client';

import JsonLd from './JsonLd';

interface ProductSchemaProps {
  name: string;
  brand: string;
  model?: string;
  description?: string;
  image?: string;
  url?: string;
  manufacturer?: {
    name: string;
    url?: string;
  };
  specifications?: Record<string, any>;
  category?: string;
  baseUrl?: string;
}

/**
 * 拖拉机产品结构化数据组件
 * 生成符合Google规范的Product结构化数据
 */
export default function ProductSchema({
  name,
  brand,
  model,
  description,
  image,
  url,
  manufacturer,
  specifications = {},
  category = 'Tractor',
  baseUrl = 'https://tractordata.site'
}: ProductSchemaProps) {
  
  const productSchema: any = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": name,
    "brand": {
      "@type": "Brand",
      "name": brand
    },
    "category": category,
    "description": description || `${brand} ${model || name} tractor specifications and information`
  };

  // 添加型号信息
  if (model) {
    productSchema.model = model;
  }

  // 添加图片
  if (image) {
    productSchema.image = image.startsWith('http') ? image : `${baseUrl}${image}`;
  }

  // 添加URL
  if (url) {
    productSchema.url = url.startsWith('http') ? url : `${baseUrl}${url}`;
  }

  // 添加制造商信息
  if (manufacturer) {
    productSchema.manufacturer = {
      "@type": "Organization",
      "name": manufacturer.name,
      ...(manufacturer.url && { "url": manufacturer.url })
    };
  }

  // 添加技术规格
  if (Object.keys(specifications).length > 0) {
    const additionalProperties = [];
    
    for (const [key, value] of Object.entries(specifications)) {
      if (value !== null && value !== undefined && value !== '') {
        additionalProperties.push({
          "@type": "PropertyValue",
          "name": formatSpecificationName(key),
          "value": String(value)
        });
      }
    }
    
    if (additionalProperties.length > 0) {
      productSchema.additionalProperty = additionalProperties;
    }
  }

  // 添加产品类别的更多信息
  productSchema.productID = `${brand}-${model || name}`.toLowerCase().replace(/\s+/g, '-');
  
  // 添加关键词
  const keywords = [brand, model, 'tractor', 'farm equipment', 'agricultural machinery'];
  productSchema.keywords = keywords.filter(Boolean).join(', ');

  return <JsonLd data={productSchema} />;
}

/**
 * 格式化规格名称
 */
function formatSpecificationName(key: string): string {
  return key
    .replace(/([A-Z])/g, ' $1') // 在大写字母前添加空格
    .replace(/^./, str => str.toUpperCase()) // 首字母大写
    .replace(/_/g, ' ') // 替换下划线为空格
    .trim();
}

/**
 * 为拖拉机型号生成产品结构化数据的辅助函数
 */
export function generateTractorProductSchema(tractorData: any, locale: string = 'en') {
  if (!tractorData) return null;

  const specifications: Record<string, any> = {};
  
  // 提取常见规格
  if (tractorData.engine_power) specifications.enginePower = tractorData.engine_power;
  if (tractorData.engine_displacement) specifications.engineDisplacement = tractorData.engine_displacement;
  if (tractorData.fuel_type) specifications.fuelType = tractorData.fuel_type;
  if (tractorData.transmission) specifications.transmission = tractorData.transmission;
  if (tractorData.drive_type) specifications.driveType = tractorData.drive_type;
  if (tractorData.weight) specifications.weight = tractorData.weight;
  if (tractorData.length) specifications.length = tractorData.length;
  if (tractorData.width) specifications.width = tractorData.width;
  if (tractorData.height) specifications.height = tractorData.height;
  if (tractorData.wheelbase) specifications.wheelbase = tractorData.wheelbase;
  if (tractorData.ground_clearance) specifications.groundClearance = tractorData.ground_clearance;
  if (tractorData.fuel_capacity) specifications.fuelCapacity = tractorData.fuel_capacity;
  if (tractorData.hydraulic_capacity) specifications.hydraulicCapacity = tractorData.hydraulic_capacity;
  if (tractorData.pto_power) specifications.ptoPower = tractorData.pto_power;
  if (tractorData.max_speed) specifications.maxSpeed = tractorData.max_speed;

  return {
    name: `${tractorData.brand_name} ${tractorData.model_name}`,
    brand: tractorData.brand_name,
    model: tractorData.model_name,
    description: tractorData.description || `${tractorData.brand_name} ${tractorData.model_name} tractor specifications and detailed information`,
    image: tractorData.image_url,
    url: `/${locale === 'en' ? '' : locale + '/'}tractors/${tractorData.brand_key}/${tractorData.model_key}`,
    manufacturer: {
      name: tractorData.brand_name,
      url: `/${locale === 'en' ? '' : locale + '/'}brands/${tractorData.brand_key}`
    },
    specifications,
    category: 'Agricultural Tractor'
  };
}
