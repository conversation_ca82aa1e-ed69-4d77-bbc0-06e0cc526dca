'use client';

import { memo } from 'react';
import Script from 'next/script';

interface JsonLdProps {
  data: Record<string, any>;
}

/**
 * 通用JSON-LD结构化数据组件
 * 用于向页面添加结构化数据，提升SEO效果
 */
const JsonLd = memo(function JsonLd({ data }: JsonLdProps) {
  // 使用数据的哈希值作为稳定的ID，避免水合错误
  const stableId = `json-ld-${JSON.stringify(data).split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0).toString(36)}`;

  return (
    <Script
      id={stableId}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
      strategy="afterInteractive"
    />
  );
});

export default JsonLd;
