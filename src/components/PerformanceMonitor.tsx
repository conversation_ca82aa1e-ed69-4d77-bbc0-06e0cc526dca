'use client';

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  navigationTiming?: PerformanceNavigationTiming;
  paintTiming?: PerformancePaintTiming[];
  resourceTiming?: PerformanceResourceTiming[];
  memoryInfo?: any;
  connectionInfo?: any;
}

interface PerformanceMonitorProps {
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
  enableConsoleLogging?: boolean;
  enableResourceMonitoring?: boolean;
}

/**
 * 性能监控组件
 * 监控页面加载性能、资源加载时间等指标
 */
export default function PerformanceMonitor({
  onMetricsUpdate,
  enableConsoleLogging = false,
  enableResourceMonitoring = true
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});

  useEffect(() => {
    // 等待页面完全加载后收集性能指标
    const collectMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint') as PerformancePaintTiming[];
      const resources = enableResourceMonitoring 
        ? performance.getEntriesByType('resource') as PerformanceResourceTiming[]
        : [];

      // 获取内存信息（如果可用）
      const memoryInfo = (performance as any).memory || null;

      // 获取网络连接信息（如果可用）
      const connectionInfo = (navigator as any).connection || null;

      const newMetrics: PerformanceMetrics = {
        navigationTiming: navigation,
        paintTiming: paint,
        resourceTiming: resources,
        memoryInfo,
        connectionInfo
      };

      setMetrics(newMetrics);
      onMetricsUpdate?.(newMetrics);

      if (enableConsoleLogging) {
        logPerformanceMetrics(newMetrics);
      }
    };

    // 页面加载完成后收集指标
    if (document.readyState === 'complete') {
      setTimeout(collectMetrics, 100);
    } else {
      window.addEventListener('load', () => {
        setTimeout(collectMetrics, 100);
      });
    }

    // 监听资源加载
    if (enableResourceMonitoring) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        if (enableConsoleLogging) {
          entries.forEach(entry => {
            if (entry.duration > 1000) { // 只记录加载时间超过1秒的资源
              console.warn(`Slow resource: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
            }
          });
        }
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('Performance Observer not supported');
      }

      return () => {
        resourceObserver.disconnect();
      };
    }
  }, [onMetricsUpdate, enableConsoleLogging, enableResourceMonitoring]);

  // 记录性能指标到控制台
  const logPerformanceMetrics = (metrics: PerformanceMetrics) => {
    const { navigationTiming, paintTiming, memoryInfo, connectionInfo } = metrics;

    if (navigationTiming) {
      const loadTime = navigationTiming.loadEventEnd - navigationTiming.navigationStart;
      const domContentLoaded = navigationTiming.domContentLoadedEventEnd - navigationTiming.navigationStart;
      const firstByte = navigationTiming.responseStart - navigationTiming.navigationStart;

      console.group('🚀 Performance Metrics');
      console.log(`📊 Page Load Time: ${loadTime.toFixed(2)}ms`);
      console.log(`📄 DOM Content Loaded: ${domContentLoaded.toFixed(2)}ms`);
      console.log(`⚡ Time to First Byte: ${firstByte.toFixed(2)}ms`);
    }

    if (paintTiming && paintTiming.length > 0) {
      paintTiming.forEach(paint => {
        console.log(`🎨 ${paint.name}: ${paint.startTime.toFixed(2)}ms`);
      });
    }

    if (memoryInfo) {
      console.log(`💾 Memory Used: ${(memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
      console.log(`💾 Memory Limit: ${(memoryInfo.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`);
    }

    if (connectionInfo) {
      console.log(`🌐 Connection: ${connectionInfo.effectiveType || 'unknown'}`);
      console.log(`📶 Downlink: ${connectionInfo.downlink || 'unknown'}Mbps`);
    }

    console.groupEnd();
  };

  // 在开发环境中显示性能信息
  if (process.env.NODE_ENV === 'development' && enableConsoleLogging) {
    return (
      <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs max-w-xs z-50">
        <div className="font-bold mb-2">Performance Monitor</div>
        {metrics.navigationTiming && (
          <div>
            Load: {(metrics.navigationTiming.loadEventEnd - metrics.navigationTiming.navigationStart).toFixed(0)}ms
          </div>
        )}
        {metrics.memoryInfo && (
          <div>
            Memory: {(metrics.memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB
          </div>
        )}
        {metrics.connectionInfo && (
          <div>
            Network: {metrics.connectionInfo.effectiveType || 'unknown'}
          </div>
        )}
      </div>
    );
  }

  return null;
}

/**
 * 性能指标计算工具函数
 */
export const PerformanceUtils = {
  /**
   * 计算Core Web Vitals分数
   */
  calculateWebVitalsScore: (lcp: number, inp: number, cls: number) => {
    const lcpScore = lcp <= 2500 ? 'good' : lcp <= 4000 ? 'needs-improvement' : 'poor';
    const inpScore = inp <= 200 ? 'good' : inp <= 500 ? 'needs-improvement' : 'poor'; // INP阈值
    const clsScore = cls <= 0.1 ? 'good' : cls <= 0.25 ? 'needs-improvement' : 'poor';

    return { lcp: lcpScore, inp: inpScore, cls: clsScore };
  },

  /**
   * 获取页面加载性能等级
   */
  getPerformanceGrade: (loadTime: number) => {
    if (loadTime <= 1000) return 'A';
    if (loadTime <= 2000) return 'B';
    if (loadTime <= 3000) return 'C';
    if (loadTime <= 5000) return 'D';
    return 'F';
  },

  /**
   * 检测慢速资源
   */
  detectSlowResources: (resources: PerformanceResourceTiming[], threshold: number = 1000) => {
    return resources.filter(resource => resource.duration > threshold);
  },

  /**
   * 计算资源加载效率
   */
  calculateResourceEfficiency: (resources: PerformanceResourceTiming[]) => {
    const totalSize = resources.reduce((sum, resource) => {
      return sum + (resource.transferSize || 0);
    }, 0);
    
    const totalTime = resources.reduce((sum, resource) => {
      return sum + resource.duration;
    }, 0);
    
    return {
      totalSize: totalSize / 1024, // KB
      totalTime,
      efficiency: totalSize > 0 ? (totalSize / 1024) / (totalTime / 1000) : 0 // KB/s
    };
  }
};

/**
 * 性能预算检查器
 */
export class PerformanceBudget {
  private budgets: Record<string, number>;

  constructor(budgets: Record<string, number> = {}) {
    this.budgets = {
      loadTime: 3000,
      firstContentfulPaint: 1500,
      largestContentfulPaint: 2500,
      interactionToNextPaint: 200, // 替代firstInputDelay
      cumulativeLayoutShift: 0.1,
      totalBlockingTime: 300,
      ...budgets
    };
  }

  check(metrics: Record<string, number>): Record<string, boolean> {
    const results: Record<string, boolean> = {};
    
    for (const [metric, budget] of Object.entries(this.budgets)) {
      if (metrics[metric] !== undefined) {
        results[metric] = metrics[metric] <= budget;
      }
    }
    
    return results;
  }

  getViolations(metrics: Record<string, number>): string[] {
    const violations: string[] = [];
    const results = this.check(metrics);
    
    for (const [metric, passed] of Object.entries(results)) {
      if (!passed) {
        violations.push(`${metric}: ${metrics[metric]} exceeds budget of ${this.budgets[metric]}`);
      }
    }
    
    return violations;
  }
}
