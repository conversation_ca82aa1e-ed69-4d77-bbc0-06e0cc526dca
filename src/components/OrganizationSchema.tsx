'use client';

import JsonLd from './JsonLd';

interface OrganizationSchemaProps {
  name: string;
  description?: string;
  url?: string;
  logo?: string;
  foundingDate?: string;
  headquarters?: {
    country?: string;
    city?: string;
    address?: string;
  };
  products?: string[];
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };
  baseUrl?: string;
}

/**
 * 拖拉机品牌组织结构化数据组件
 * 生成符合Google规范的Organization结构化数据
 */
export default function OrganizationSchema({
  name,
  description,
  url,
  logo,
  foundingDate,
  headquarters,
  products = [],
  socialMedia = {},
  baseUrl = 'https://tractordata.site'
}: OrganizationSchemaProps) {
  
  const organizationSchema: any = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": name,
    "description": description || `${name} is a manufacturer of agricultural tractors and farm equipment.`,
    "url": url || `${baseUrl}/brands/${name.toLowerCase().replace(/\s+/g, '-')}`
  };

  // 添加logo
  if (logo) {
    organizationSchema.logo = logo.startsWith('http') ? logo : `${baseUrl}${logo}`;
    organizationSchema.image = organizationSchema.logo;
  }

  // 添加成立日期
  if (foundingDate) {
    organizationSchema.foundingDate = foundingDate;
  }

  // 添加总部信息
  if (headquarters && (headquarters.country || headquarters.city)) {
    const address: any = {
      "@type": "PostalAddress"
    };
    
    if (headquarters.country) address.addressCountry = headquarters.country;
    if (headquarters.city) address.addressLocality = headquarters.city;
    if (headquarters.address) address.streetAddress = headquarters.address;
    
    organizationSchema.address = address;
  }

  // 添加产品类别
  if (products.length > 0) {
    organizationSchema.makesOffer = products.map(product => ({
      "@type": "Offer",
      "itemOffered": {
        "@type": "Product",
        "name": product,
        "category": "Agricultural Equipment"
      }
    }));
  }

  // 添加社交媒体链接
  const sameAs = [];
  if (socialMedia.facebook) sameAs.push(socialMedia.facebook);
  if (socialMedia.twitter) sameAs.push(socialMedia.twitter);
  if (socialMedia.linkedin) sameAs.push(socialMedia.linkedin);
  if (socialMedia.youtube) sameAs.push(socialMedia.youtube);
  
  if (sameAs.length > 0) {
    organizationSchema.sameAs = sameAs;
  }

  // 添加行业分类
  organizationSchema.industry = "Agricultural Equipment Manufacturing";
  organizationSchema.naics = "333111"; // Farm Machinery and Equipment Manufacturing

  return <JsonLd data={organizationSchema} />;
}

/**
 * 为拖拉机品牌生成组织结构化数据的辅助函数
 */
export function generateBrandOrganizationSchema(brandData: any, locale: string = 'en') {
  if (!brandData) return null;

  // 从品牌数据中提取信息
  const products = [];
  if (brandData.product_types) {
    products.push(...brandData.product_types);
  } else {
    // 默认产品类型
    products.push('Tractors', 'Farm Equipment', 'Agricultural Machinery');
  }

  const headquarters: any = {};
  if (brandData.country) headquarters.country = brandData.country;
  if (brandData.city) headquarters.city = brandData.city;
  if (brandData.address) headquarters.address = brandData.address;

  const socialMedia: any = {};
  if (brandData.facebook_url) socialMedia.facebook = brandData.facebook_url;
  if (brandData.twitter_url) socialMedia.twitter = brandData.twitter_url;
  if (brandData.linkedin_url) socialMedia.linkedin = brandData.linkedin_url;
  if (brandData.youtube_url) socialMedia.youtube = brandData.youtube_url;

  return {
    name: brandData.name,
    description: brandData.description || `${brandData.name} is a leading manufacturer of agricultural tractors and farm equipment.`,
    url: `/${locale === 'en' ? '' : locale + '/'}brands/${brandData.brand_key}`,
    logo: brandData.logo,
    foundingDate: brandData.founded_year ? `${brandData.founded_year}-01-01` : undefined,
    headquarters: Object.keys(headquarters).length > 0 ? headquarters : undefined,
    products,
    socialMedia: Object.keys(socialMedia).length > 0 ? socialMedia : undefined
  };
}

/**
 * 网站组织结构化数据
 */
export function WebsiteOrganizationSchema({ locale = 'en' }: { locale?: string }) {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "TractorData",
    "description": "Comprehensive database of tractor specifications, comparisons, and agricultural equipment information.",
    "url": "https://tractordata.site",
    "logo": "https://tractordata.site/logo.svg",
    "foundingDate": "2024-01-01",
    "industry": "Agricultural Information Services",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "Global"
    },
    "sameAs": [
      // 可以添加社交媒体链接
    ],
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://tractordata.site/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return <JsonLd data={organizationSchema} />;
}
