'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import JsonLd from './JsonLd';

interface BreadcrumbItem {
  label: string;
  path: string;
  isLast?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  baseUrl?: string;
}

/**
 * 面包屑导航组件
 * 用于显示当前页面在网站层次结构中的位置，并添加结构化数据
 */
export default function Breadcrumbs({ items, baseUrl = 'https://tractordata.site' }: BreadcrumbsProps) {
  const t = useTranslations('Navigation');

  // 生成结构化数据
  const breadcrumbSchema = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    'itemListElement': items.map((item, index) => ({
      '@type': 'ListItem',
      'position': index + 1,
      'name': item.label,
      ...(item.isLast ? {} : { 'item': `${baseUrl}${item.path}` })
    }))
  };

  return (
    <>
      <nav aria-label="Breadcrumb" className="py-3 text-sm">
        <ol className="flex flex-wrap items-center space-x-2">
          {items.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && <span className="mx-2 text-gray-400">/</span>}
              {item.isLast ? (
                <span className="text-gray-600" aria-current="page">{item.label}</span>
              ) : (
                <Link
                  href={item.path}
                  className="text-tractor hover:text-tractor/80 transition-colors"
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>

      {/* 添加结构化数据 */}
      <JsonLd data={breadcrumbSchema} />
    </>
  );
}
