'use client';

import { useEffect } from 'react';
import { onCLS, onFCP, onLCP, onTTFB, onINP } from 'web-vitals';

interface WebVitalsProps {
  debug?: boolean;
}

/**
 * Core Web Vitals 监控组件
 * 收集并发送性能指标到Google Analytics
 */
export default function WebVitals({ debug = false }: WebVitalsProps) {
  useEffect(() => {
    // 发送指标到Google Analytics
    function sendToGoogleAnalytics(metric: any) {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          event_label: metric.id,
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          non_interaction: true,
        });
      }

      // 调试模式下输出到控制台
      if (debug) {
        console.log('Web Vitals:', metric);
      }
    }

    // 发送指标到自定义端点（可选）
    function sendToAnalytics(metric: any) {
      // 发送到Google Analytics
      sendToGoogleAnalytics(metric);

      // 可以添加发送到其他分析服务的逻辑
      if (process.env.NODE_ENV === 'production') {
        // 发送到自定义分析端点
        fetch('/api/analytics/web-vitals', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: metric.name,
            value: metric.value,
            id: metric.id,
            delta: metric.delta,
            rating: metric.rating,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
          }),
        }).catch(error => {
          if (debug) {
            console.error('Failed to send web vitals:', error);
          }
        });
      }
    }

    // 收集所有Core Web Vitals指标
    onCLS(sendToAnalytics);
    onFCP(sendToAnalytics);
    onLCP(sendToAnalytics);
    onTTFB(sendToAnalytics);
    onINP(sendToAnalytics); // 替代FID的新指标

    // 页面可见性变化时的处理
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        // 页面隐藏时发送最终指标
        onCLS(sendToAnalytics);
        onLCP(sendToAnalytics);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [debug]);

  return null; // 这是一个监控组件，不渲染任何内容
}

// 扩展Window接口以包含gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
