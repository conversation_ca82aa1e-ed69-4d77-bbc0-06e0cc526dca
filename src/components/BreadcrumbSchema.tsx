'use client';

import JsonLd from './JsonLd';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[];
  baseUrl?: string;
}

/**
 * 面包屑导航结构化数据组件
 * 生成符合Google规范的BreadcrumbList结构化数据
 */
export default function BreadcrumbSchema({ items, baseUrl = 'https://tractordata.site' }: BreadcrumbSchemaProps) {
  if (!items || items.length === 0) {
    return null;
  }

  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`
    }))
  };

  return <JsonLd data={breadcrumbSchema} />;
}

/**
 * 生成面包屑项目的辅助函数
 */
export function generateBreadcrumbItems(
  path: string, 
  locale: string = 'en',
  translations: any = {}
): BreadcrumbItem[] {
  const items: BreadcrumbItem[] = [];
  const pathSegments = path.split('/').filter(segment => segment && segment !== locale);
  
  // 添加首页
  items.push({
    name: translations.home || 'Home',
    url: locale === 'en' ? '/' : `/${locale}/`
  });

  let currentPath = locale === 'en' ? '' : `/${locale}`;
  
  for (let i = 0; i < pathSegments.length; i++) {
    const segment = pathSegments[i];
    currentPath += `/${segment}`;
    
    // 根据路径段生成名称
    let name = segment;
    
    // 常见路径的翻译
    const pathTranslations: Record<string, string> = {
      'brands': translations.brands || 'Brands',
      'tractors': translations.tractors || 'Tractors', 
      'news': translations.news || 'News',
      'compare': translations.compare || 'Compare',
      'shows': translations.shows || 'Shows',
      'about': translations.about || 'About',
      'contact': translations.contact || 'Contact'
    };
    
    if (pathTranslations[segment]) {
      name = pathTranslations[segment];
    } else {
      // 对于动态段（如品牌名、型号名），保持原样或进行格式化
      name = segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    
    items.push({
      name,
      url: currentPath
    });
  }

  return items;
}
