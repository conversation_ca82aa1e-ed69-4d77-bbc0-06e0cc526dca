'use client';

import { useLocale, useTranslations } from 'next-intl';
import { usePathname, useRouter } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { getLocalizedLanguageName } from '@/lib/multilingual-seo';
import { useState } from 'react';

interface LanguageSwitcherProps {
  className?: string;
  showFlags?: boolean;
}

/**
 * 语言切换器组件
 * 提供SEO友好的语言切换功能
 */
export default function LanguageSwitcher({ 
  className = '', 
  showFlags = true 
}: LanguageSwitcherProps) {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations('LanguageSwitcher');
  const [isOpen, setIsOpen] = useState(false);

  // 语言标志映射
  const flagEmojis: Record<string, string> = {
    'en': '🇺🇸',
    'fr': '🇫🇷',
    'zh': '🇨🇳',
    'es': '🇪🇸',
    'de': '🇩🇪',
    'pt': '🇧🇷'
  };

  // 获取当前路径（不包含语言前缀）
  const getPathWithoutLocale = () => {
    if (locale === routing.defaultLocale) {
      return pathname;
    }
    return pathname.replace(`/${locale}`, '') || '/';
  };

  // 生成目标语言的URL
  const getLocalizedUrl = (targetLocale: string) => {
    const pathWithoutLocale = getPathWithoutLocale();
    
    if (targetLocale === routing.defaultLocale) {
      return pathWithoutLocale;
    }
    return `/${targetLocale}${pathWithoutLocale}`;
  };

  // 处理语言切换
  const handleLanguageChange = (targetLocale: string) => {
    const newUrl = getLocalizedUrl(targetLocale);
    router.push(newUrl);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* 当前语言按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-md border border-gray-300 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
        aria-label={t('selectLanguage')}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        {showFlags && (
          <span className="text-lg" role="img" aria-label={getLocalizedLanguageName(locale, locale)}>
            {flagEmojis[locale]}
          </span>
        )}
        <span className="text-sm font-medium text-gray-700">
          {getLocalizedLanguageName(locale, locale)}
        </span>
        <svg
          className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 语言选项下拉菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50">
          <ul role="listbox" className="py-1">
            {routing.locales.map((targetLocale) => (
              <li key={targetLocale} role="option" aria-selected={targetLocale === locale}>
                <button
                  onClick={() => handleLanguageChange(targetLocale)}
                  className={`w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 ${
                    targetLocale === locale ? 'bg-green-50 text-green-700' : 'text-gray-700'
                  }`}
                  disabled={targetLocale === locale}
                >
                  {showFlags && (
                    <span className="text-lg" role="img" aria-label={getLocalizedLanguageName(targetLocale, locale)}>
                      {flagEmojis[targetLocale]}
                    </span>
                  )}
                  <span className="text-sm">
                    {getLocalizedLanguageName(targetLocale, locale)}
                  </span>
                  {targetLocale === locale && (
                    <svg className="w-4 h-4 text-green-600 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 点击外部关闭下拉菜单 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
}

/**
 * 简化版语言切换器（用于移动端）
 */
export function MobileLanguageSwitcher({ className = '' }: { className?: string }) {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const t = useTranslations('LanguageSwitcher');

  const flagEmojis: Record<string, string> = {
    'en': '🇺🇸',
    'fr': '🇫🇷',
    'zh': '🇨🇳',
    'es': '🇪🇸',
    'de': '🇩🇪',
    'pt': '🇧🇷'
  };

  const getPathWithoutLocale = () => {
    if (locale === routing.defaultLocale) {
      return pathname;
    }
    return pathname.replace(`/${locale}`, '') || '/';
  };

  const getLocalizedUrl = (targetLocale: string) => {
    const pathWithoutLocale = getPathWithoutLocale();
    
    if (targetLocale === routing.defaultLocale) {
      return pathWithoutLocale;
    }
    return `/${targetLocale}${pathWithoutLocale}`;
  };

  const handleLanguageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const targetLocale = event.target.value;
    const newUrl = getLocalizedUrl(targetLocale);
    router.push(newUrl);
  };

  return (
    <div className={`relative ${className}`}>
      <select
        value={locale}
        onChange={handleLanguageChange}
        className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-green-500"
        aria-label={t('selectLanguage')}
      >
        {routing.locales.map((targetLocale) => (
          <option key={targetLocale} value={targetLocale}>
            {flagEmojis[targetLocale]} {getLocalizedLanguageName(targetLocale, locale)}
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </div>
  );
}
