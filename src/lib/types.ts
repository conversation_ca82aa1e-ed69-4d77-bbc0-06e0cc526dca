// 拖拉机数据模型
export interface TractorBrand {
  id: string;
  name: string;
  slug: string;
  imageSrc: string;
  tractorCount?: number;
  yearRange?: string;
  description?: string;
}

export interface TractorModel {
  id: string;
  name: string;
  slug: string;
  brandId: string;
  brandName: string;
  type: 'farm' | 'lawn';
  imageSrc: string;
  year: number; 
  horsepower: number;
  engine?: string;
  transmission?: string;
  weight?: number;
  features?: string[];
  description?: string;
}

export interface TractorNews {
  id: string;
  title: string;
  excerpt: string;
  content?: string;
  date: string;
  slug: string;
  imageSrc: string;
}

// 筛选选项
export interface FilterOption {
  id: string;
  label: string;
  value: string | number;
}

export interface FilterGroup {
  id: string;
  name: string;
  type: 'checkbox' | 'radio' | 'range';
  options?: FilterOption[];
  rangeMin?: number;
  rangeMax?: number;
  rangeStep?: number;
}

// 搜索相关类型
export interface SearchParams {
  query?: string;
  type?: 'farm' | 'lawn' | 'all';
  brand?: string;
  horsepowerMin?: number;
  horsepowerMax?: number;
  yearMin?: number;
  yearMax?: number;
  features?: string[];
}

// 分页信息
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
}

// 联盟广告相关类型
export interface AffiliateProduct {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  affiliateUrl: string; // SiteStripe生成的链接
  category: 'tractor' | 'equipment' | 'parts' | 'accessories';
  enabled: boolean;
  
  // 增强字段
  rating?: number; // 1-5星评分
  reviewCount?: number; // 评论数量
  tags: string[]; // 产品标签，用于智能匹配
  priority: number; // 显示优先级，数字越大越优先
  isHotPick: boolean; // 是否为热门推荐
  isOnSale: boolean; // 是否有特价
  targetPages: string[]; // 适合显示的页面类型 ['home', 'brand', 'tractor', 'news']
  brandMatch?: string[]; // 匹配的品牌slug
  modelMatch?: string[]; // 匹配的型号
  
  // 统计数据
  clickCount: number; // 总点击数
  impressionCount: number; // 总展示数
  clickRate: number; // 点击率 (clickCount / impressionCount)
  
  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  
  // 多语言支持
  translations?: Record<string, {
    title: string;
    description: string;
    affiliateUrl?: string; // 可选的本地化亚马逊链接
  }>;
}

// 点击跟踪事件
export interface ClickEvent {
  id: string;
  productId: string;
  sessionId: string;
  
  // 页面信息
  sourcePage: 'home' | 'brand' | 'tractor' | 'news';
  sourceUrl: string;
  referrer?: string;
  
  // 用户信息
  userAgent: string;
  ipHash: string; // 哈希处理的IP地址
  locale: string;
  
  // 时间信息
  timestamp: Date;
  
  // 上下文信息
  pageContext?: {
    brandSlug?: string;
    tractorModel?: string;
    newsSlug?: string;
  };
}

// 点击跟踪请求DTO
export interface TrackClickRequest {
  productId: string;
  sourcePage: string;
  sourceUrl: string;
  referrer?: string;
  pageContext?: {
    brandSlug?: string;
    tractorModel?: string;
    newsSlug?: string;
  };
}

// 点击跟踪响应DTO
export interface TrackClickResponse {
  success: boolean;
  trackingId: string;
  redirectUrl: string;
}

// 点击统计数据
export interface ClickStats {
  productId: string;
  totalClicks: number;
  totalImpressions: number;
  clickRate: number;
  clicksByDate: Record<string, number>; // 按日期分组的点击数
  clicksByPage: Record<string, number>; // 按页面分组的点击数
  clicksByLocale: Record<string, number>; // 按语言分组的点击数
}

// 产品性能统计
export interface ProductStats {
  product: AffiliateProduct;
  stats: ClickStats;
  rank: number; // 在所有产品中的排名
}

// 页面性能统计
export interface PageStats {
  pageType: string;
  totalClicks: number;
  totalImpressions: number;
  clickRate: number;
  topProducts: ProductStats[];
}

// 日期范围
export interface DateRange {
  startDate: Date;
  endDate: Date;
}

// 页面上下文
export interface PageContext {
  pageType: 'home' | 'brand' | 'tractor' | 'news';
  brandSlug?: string;
  tractorModel?: string;
  newsSlug?: string;
  locale: string;
}
