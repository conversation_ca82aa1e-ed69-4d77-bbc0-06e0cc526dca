/**
 * 环境配置管理工具
 * 统一管理所有环境变量的读取和验证
 */

// 环境变量类型定义
export interface EnvConfig {
  // 应用基础配置
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  NEXT_TELEMETRY_DISABLED: boolean;

  // MongoDB 配置
  MONGODB_URI: string;
  MONGODB_DB_NAME: string;
  MONGODB_USERNAME: string;
  MONGODB_PASSWORD: string;
  MONGODB_AUTH_SOURCE: string;
  MONGODB_MAX_POOL_SIZE: number;
  MONGODB_MIN_POOL_SIZE: number;
  MONGODB_CONNECT_TIMEOUT: number;
  MONGODB_SERVER_SELECTION_TIMEOUT: number;

  // 构建配置
  BUILD_MODE: 'development' | 'test' | 'full';
  BUILD_STATIC: boolean;
  ANALYZE: boolean;

  // 日志配置
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
  LOG_FILE_PATH: string;
  LOG_CONSOLE: boolean;
  LOG_FILE: boolean;

  // 缓存配置
  CACHE_REVALIDATE_TIME: number;
  CACHE_ENABLED: boolean;

  // 站点配置
  SITE_URL: string;
  SITE_NAME: string;
  DEFAULT_LOCALE: string;
  SUPPORTED_LOCALES: string[];

  // 安全配置
  API_RATE_LIMIT: number;
  CORS_ORIGIN: string;
  SECURITY_HEADERS_ENABLED: boolean;

  // 性能配置
  IMAGES_UNOPTIMIZED: boolean;
  COMPRESS_ENABLED: boolean;

  // 开发工具配置
  TURBOPACK_ENABLED: boolean;
  SKIP_TYPE_CHECK: boolean;
  SKIP_ESLINT: boolean;

  // PM2 配置
  PM2_INSTANCES: string | number;
  PM2_MAX_MEMORY: string;
  PM2_APP_NAME: string;

  // 备份配置
  BACKUP_DIR: string;
  AUTO_BACKUP_ENABLED: boolean;
  BACKUP_RETENTION_DAYS: number;
}

/**
 * 获取环境变量值，支持默认值和类型转换
 */
function getEnvVar<T>(
  key: string,
  defaultValue: T,
  transform?: (value: string) => T
): T {
  const value = process.env[key];
  
  if (value === undefined || value === '') {
    return defaultValue;
  }

  if (transform) {
    try {
      return transform(value);
    } catch (error) {
      console.warn(`环境变量 ${key} 转换失败，使用默认值:`, error);
      return defaultValue;
    }
  }

  return value as T;
}

/**
 * 字符串转布尔值
 */
function parseBoolean(value: string): boolean {
  return value.toLowerCase() === 'true' || value === '1';
}

/**
 * 字符串转数字
 */
function parseNumber(value: string): number {
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    throw new Error(`无法转换为数字: ${value}`);
  }
  return num;
}

/**
 * 字符串转数组（逗号分隔）
 */
function parseArray(value: string): string[] {
  return value.split(',').map(item => item.trim()).filter(Boolean);
}

/**
 * 获取完整的环境配置
 */
export function getEnvConfig(): EnvConfig {
  return {
    // 应用基础配置
    NODE_ENV: getEnvVar('NODE_ENV', 'development') as EnvConfig['NODE_ENV'],
    PORT: getEnvVar('PORT', 3000, parseNumber),
    NEXT_TELEMETRY_DISABLED: getEnvVar('NEXT_TELEMETRY_DISABLED', true, parseBoolean),

    // MongoDB 配置
    MONGODB_URI: getEnvVar('MONGODB_URI', 'mongodb://localhost:27017'),
    MONGODB_DB_NAME: getEnvVar('MONGODB_DB_NAME', 'tractor_data'),
    MONGODB_USERNAME: getEnvVar('MONGODB_USERNAME', 'root'),
    MONGODB_PASSWORD: getEnvVar('MONGODB_PASSWORD', ''),
    MONGODB_AUTH_SOURCE: getEnvVar('MONGODB_AUTH_SOURCE', 'admin'),
    MONGODB_MAX_POOL_SIZE: getEnvVar('MONGODB_MAX_POOL_SIZE', 10, parseNumber),
    MONGODB_MIN_POOL_SIZE: getEnvVar('MONGODB_MIN_POOL_SIZE', 2, parseNumber),
    MONGODB_CONNECT_TIMEOUT: getEnvVar('MONGODB_CONNECT_TIMEOUT', 10000, parseNumber),
    MONGODB_SERVER_SELECTION_TIMEOUT: getEnvVar('MONGODB_SERVER_SELECTION_TIMEOUT', 5000, parseNumber),

    // 构建配置
    BUILD_MODE: getEnvVar('BUILD_MODE', 'development') as EnvConfig['BUILD_MODE'],
    BUILD_STATIC: getEnvVar('BUILD_STATIC', false, parseBoolean),
    ANALYZE: getEnvVar('ANALYZE', false, parseBoolean),

    // 日志配置
    LOG_LEVEL: getEnvVar('LOG_LEVEL', 'info') as EnvConfig['LOG_LEVEL'],
    LOG_FILE_PATH: getEnvVar('LOG_FILE_PATH', './logs'),
    LOG_CONSOLE: getEnvVar('LOG_CONSOLE', true, parseBoolean),
    LOG_FILE: getEnvVar('LOG_FILE', true, parseBoolean),

    // 缓存配置
    CACHE_REVALIDATE_TIME: getEnvVar('CACHE_REVALIDATE_TIME', 3600, parseNumber),
    CACHE_ENABLED: getEnvVar('CACHE_ENABLED', true, parseBoolean),

    // 站点配置
    SITE_URL: getEnvVar('SITE_URL', 'http://localhost:3000'),
    SITE_NAME: getEnvVar('SITE_NAME', 'TractorData'),
    DEFAULT_LOCALE: getEnvVar('DEFAULT_LOCALE', 'en'),
    SUPPORTED_LOCALES: getEnvVar('SUPPORTED_LOCALES', 'en,fr,zh,es,de,pt', parseArray),

    // 安全配置
    API_RATE_LIMIT: getEnvVar('API_RATE_LIMIT', 100, parseNumber),
    CORS_ORIGIN: getEnvVar('CORS_ORIGIN', '*'),
    SECURITY_HEADERS_ENABLED: getEnvVar('SECURITY_HEADERS_ENABLED', true, parseBoolean),

    // 性能配置
    IMAGES_UNOPTIMIZED: getEnvVar('IMAGES_UNOPTIMIZED', true, parseBoolean),
    COMPRESS_ENABLED: getEnvVar('COMPRESS_ENABLED', true, parseBoolean),

    // 开发工具配置
    TURBOPACK_ENABLED: getEnvVar('TURBOPACK_ENABLED', true, parseBoolean),
    SKIP_TYPE_CHECK: getEnvVar('SKIP_TYPE_CHECK', true, parseBoolean),
    SKIP_ESLINT: getEnvVar('SKIP_ESLINT', true, parseBoolean),

    // PM2 配置
    PM2_INSTANCES: getEnvVar('PM2_INSTANCES', 'max'),
    PM2_MAX_MEMORY: getEnvVar('PM2_MAX_MEMORY', '1G'),
    PM2_APP_NAME: getEnvVar('PM2_APP_NAME', 'tractor-data-web'),

    // 备份配置
    BACKUP_DIR: getEnvVar('BACKUP_DIR', './backups'),
    AUTO_BACKUP_ENABLED: getEnvVar('AUTO_BACKUP_ENABLED', false, parseBoolean),
    BACKUP_RETENTION_DAYS: getEnvVar('BACKUP_RETENTION_DAYS', 7, parseNumber),
  };
}

/**
 * 验证必需的环境变量
 */
export function validateEnvConfig(): void {
  const config = getEnvConfig();
  const errors: string[] = [];

  // 验证必需的配置
  if (!config.MONGODB_URI) {
    errors.push('MONGODB_URI 是必需的');
  }

  if (!config.MONGODB_DB_NAME) {
    errors.push('MONGODB_DB_NAME 是必需的');
  }

  if (!config.MONGODB_USERNAME) {
    errors.push('MONGODB_USERNAME 是必需的');
  }

  if (!config.MONGODB_PASSWORD) {
    errors.push('MONGODB_PASSWORD 是必需的');
  }

  if (errors.length > 0) {
    throw new Error(`环境配置验证失败:\n${errors.join('\n')}`);
  }
}

/**
 * 获取 MongoDB 连接字符串
 */
export function getMongoConnectionString(): string {
  const config = getEnvConfig();
  
  // 构建完整的 MongoDB 连接字符串
  const url = new URL(config.MONGODB_URI);
  url.username = config.MONGODB_USERNAME;
  url.password = config.MONGODB_PASSWORD;
  url.pathname = `/${config.MONGODB_DB_NAME}`;
  url.searchParams.set('authSource', config.MONGODB_AUTH_SOURCE);
  url.searchParams.set('maxPoolSize', config.MONGODB_MAX_POOL_SIZE.toString());
  url.searchParams.set('minPoolSize', config.MONGODB_MIN_POOL_SIZE.toString());
  url.searchParams.set('connectTimeoutMS', config.MONGODB_CONNECT_TIMEOUT.toString());
  url.searchParams.set('serverSelectionTimeoutMS', config.MONGODB_SERVER_SELECTION_TIMEOUT.toString());
  
  return url.toString();
}

// 导出单例配置对象
export const env = getEnvConfig();

// 在模块加载时验证配置
try {
  validateEnvConfig();
} catch (error) {
  console.error('环境配置验证失败:', error);
  // 在开发环境中，我们可以继续运行，但会显示警告
  if (env.NODE_ENV === 'production') {
    process.exit(1);
  }
}
