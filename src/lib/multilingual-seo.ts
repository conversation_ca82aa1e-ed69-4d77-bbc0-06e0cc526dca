import { routing } from '@/i18n/routing';

const BASE_URL = 'https://tractordata.site';

/**
 * 语言到地区的映射
 */
const LOCALE_REGION_MAP: Record<string, string> = {
  'en': 'en-US',
  'fr': 'fr-FR', 
  'zh': 'zh-CN',
  'es': 'es-ES',
  'de': 'de-DE',
  'pt': 'pt-BR'
};

/**
 * 语言到货币的映射
 */
const LOCALE_CURRENCY_MAP: Record<string, string> = {
  'en': 'USD',
  'fr': 'EUR',
  'zh': 'CNY',
  'es': 'EUR',
  'de': 'EUR',
  'pt': 'BRL'
};

/**
 * 语言到时区的映射
 */
const LOCALE_TIMEZONE_MAP: Record<string, string> = {
  'en': 'America/New_York',
  'fr': 'Europe/Paris',
  'zh': 'Asia/Shanghai',
  'es': 'Europe/Madrid',
  'de': 'Europe/Berlin',
  'pt': 'America/Sao_Paulo'
};

/**
 * 生成所有语言的hreflang链接
 */
export function generateHreflangLinks(pathWithoutLocale: string): Record<string, string> {
  const hreflangLinks: Record<string, string> = {};
  
  // 为每种支持的语言生成链接
  for (const locale of routing.locales) {
    const url = locale === routing.defaultLocale 
      ? `${BASE_URL}${pathWithoutLocale}`
      : `${BASE_URL}/${locale}${pathWithoutLocale}`;
    
    hreflangLinks[LOCALE_REGION_MAP[locale] || locale] = url;
  }
  
  // 添加 x-default 指向默认语言
  hreflangLinks['x-default'] = `${BASE_URL}${pathWithoutLocale}`;
  
  return hreflangLinks;
}

/**
 * 生成语言特定的结构化数据
 */
export function generateLocalizedStructuredData(
  baseData: any,
  locale: string,
  pathWithoutLocale: string
) {
  const localizedData = {
    ...baseData,
    inLanguage: LOCALE_REGION_MAP[locale] || locale,
    url: locale === routing.defaultLocale 
      ? `${BASE_URL}${pathWithoutLocale}`
      : `${BASE_URL}/${locale}${pathWithoutLocale}`
  };

  // 添加多语言版本链接
  const alternateVersions = routing.locales.map(loc => ({
    "@type": "WebPage",
    "url": loc === routing.defaultLocale 
      ? `${BASE_URL}${pathWithoutLocale}`
      : `${BASE_URL}/${loc}${pathWithoutLocale}`,
    "inLanguage": LOCALE_REGION_MAP[loc] || loc
  }));

  if (alternateVersions.length > 1) {
    localizedData.workTranslation = alternateVersions.filter(
      version => version.inLanguage !== localizedData.inLanguage
    );
  }

  return localizedData;
}

/**
 * 生成本地化的组织结构化数据
 */
export function generateLocalizedOrganizationData(
  organizationData: any,
  locale: string
) {
  const localizedData = {
    ...organizationData,
    inLanguage: LOCALE_REGION_MAP[locale] || locale
  };

  // 添加本地化的地址信息
  if (organizationData.address) {
    localizedData.address = {
      ...organizationData.address,
      addressCountry: getCountryForLocale(locale)
    };
  }

  // 添加本地化的货币信息
  if (organizationData.makesOffer) {
    localizedData.makesOffer = organizationData.makesOffer.map((offer: any) => ({
      ...offer,
      priceCurrency: LOCALE_CURRENCY_MAP[locale] || 'USD'
    }));
  }

  return localizedData;
}

/**
 * 生成本地化的产品结构化数据
 */
export function generateLocalizedProductData(
  productData: any,
  locale: string
) {
  const localizedData = {
    ...productData,
    inLanguage: LOCALE_REGION_MAP[locale] || locale
  };

  // 添加本地化的可用性信息
  if (productData.offers) {
    localizedData.offers = {
      ...productData.offers,
      priceCurrency: LOCALE_CURRENCY_MAP[locale] || 'USD',
      availableAtOrFrom: {
        "@type": "Place",
        "addressCountry": getCountryForLocale(locale)
      }
    };
  }

  return localizedData;
}

/**
 * 生成本地化的文章结构化数据
 */
export function generateLocalizedArticleData(
  articleData: any,
  locale: string
) {
  const localizedData = {
    ...articleData,
    inLanguage: LOCALE_REGION_MAP[locale] || locale
  };

  // 添加本地化的发布者信息
  if (articleData.publisher) {
    localizedData.publisher = {
      ...articleData.publisher,
      address: {
        "@type": "PostalAddress",
        "addressCountry": getCountryForLocale(locale)
      }
    };
  }

  return localizedData;
}

/**
 * 根据语言代码获取国家代码
 */
function getCountryForLocale(locale: string): string {
  const countryMap: Record<string, string> = {
    'en': 'US',
    'fr': 'FR',
    'zh': 'CN',
    'es': 'ES',
    'de': 'DE',
    'pt': 'BR'
  };
  
  return countryMap[locale] || 'US';
}

/**
 * 生成语言选择器的结构化数据
 */
export function generateLanguageSelectorData(currentPath: string, currentLocale: string) {
  const pathWithoutLocale = currentLocale === routing.defaultLocale 
    ? currentPath 
    : currentPath.replace(`/${currentLocale}`, '');

  return {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "url": `${BASE_URL}${currentPath}`,
    "inLanguage": LOCALE_REGION_MAP[currentLocale] || currentLocale,
    "isPartOf": {
      "@type": "WebSite",
      "name": "TractorData",
      "url": BASE_URL,
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${BASE_URL}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      }
    },
    "workTranslation": routing.locales
      .filter(locale => locale !== currentLocale)
      .map(locale => ({
        "@type": "WebPage",
        "url": locale === routing.defaultLocale 
          ? `${BASE_URL}${pathWithoutLocale}`
          : `${BASE_URL}/${locale}${pathWithoutLocale}`,
        "inLanguage": LOCALE_REGION_MAP[locale] || locale
      }))
  };
}

/**
 * 获取语言的本地化名称
 */
export function getLocalizedLanguageName(locale: string, targetLocale: string): string {
  const languageNames: Record<string, Record<string, string>> = {
    'en': {
      'en': 'English',
      'fr': 'French',
      'zh': 'Chinese',
      'es': 'Spanish',
      'de': 'German',
      'pt': 'Portuguese'
    },
    'fr': {
      'en': 'Anglais',
      'fr': 'Français',
      'zh': 'Chinois',
      'es': 'Espagnol',
      'de': 'Allemand',
      'pt': 'Portugais'
    },
    'zh': {
      'en': '英语',
      'fr': '法语',
      'zh': '中文',
      'es': '西班牙语',
      'de': '德语',
      'pt': '葡萄牙语'
    },
    'es': {
      'en': 'Inglés',
      'fr': 'Francés',
      'zh': 'Chino',
      'es': 'Español',
      'de': 'Alemán',
      'pt': 'Portugués'
    },
    'de': {
      'en': 'Englisch',
      'fr': 'Französisch',
      'zh': 'Chinesisch',
      'es': 'Spanisch',
      'de': 'Deutsch',
      'pt': 'Portugiesisch'
    },
    'pt': {
      'en': 'Inglês',
      'fr': 'Francês',
      'zh': 'Chinês',
      'es': 'Espanhol',
      'de': 'Alemão',
      'pt': 'Português'
    }
  };

  return languageNames[targetLocale]?.[locale] || locale.toUpperCase();
}
