import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 创建URL友好的slug（使用连字符）
 * @param name 需要转换为slug的字符串
 * @returns URL友好的slug字符串
 */
export function createSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * 创建下划线格式的slug
 * 专门用于需要下划线分隔的场景
 *
 * 转换示例：
 * - "<PERSON> Deere" → "john_deere"
 * - "Case IH" → "case_ih"
 * - "New Holland" → "new_holland"
 * - "Massey Ferguson" → "massey_ferguson"
 *
 * @param name 需要转换为下划线slug的字符串
 * @returns 下划线格式的slug字符串
 */
export function createUnderscoreSlug(name: string): string {
  if (!name || typeof name !== 'string') {
    return '';
  }

  return name
    .toLowerCase()                    // 转为小写
    .trim()                          // 去除首尾空格
    .replace(/\s+/g, '_')            // 空格替换为下划线
    .replace(/[^\w\-_]/g, '')        // 移除特殊字符，保留字母、数字、下划线、连字符
    .replace(/_{2,}/g, '_')          // 多个连续下划线替换为单个
    .replace(/^_+|_+$/g, '');        // 去除首尾下划线
}
