import { MongoClient } from 'mongodb';
import { env, getMongoConnectionString } from './env';

// 根据环境优化连接配置
const isBuildTime = process.env.NODE_ENV === 'production' && process.env.NEXT_PHASE === 'phase-production-build';
const isTestMode = process.env.BUILD_MODE === 'test';

let cachedClient: MongoClient | null = null;
let connectionPromise: Promise<{ client: MongoClient; db: any }> | null = null;

export async function connectToDatabase() {
  // 如果已有缓存的客户端，直接返回
  if (cachedClient) {
    return {
      client: cachedClient,
      db: cachedClient.db(env.MONGODB_DB_NAME)
    };
  }

  // 如果正在连接中，等待连接完成
  if (connectionPromise) {
    return connectionPromise;
  }

  // 根据环境选择连接配置
  const getConnectionConfig = () => {
    const baseConfig = {
      auth: {
        username: env.MONGODB_USERNAME,
        password: env.MONGODB_PASSWORD,
      },
      authSource: env.MONGODB_AUTH_SOURCE,
      retryWrites: true,
      retryReads: true,
      maxPoolSize: env.MONGODB_MAX_POOL_SIZE,
      minPoolSize: env.MONGODB_MIN_POOL_SIZE,
      connectTimeoutMS: env.MONGODB_CONNECT_TIMEOUT,
      serverSelectionTimeoutMS: env.MONGODB_SERVER_SELECTION_TIMEOUT,
    };

    if (isTestMode) {
      // 测试模式：轻量配置
      return {
        ...baseConfig,
        maxPoolSize: 5,
        minPoolSize: 2,
        connectTimeoutMS: 15000,
        socketTimeoutMS: 30000,
        serverSelectionTimeoutMS: 10000,
        heartbeatFrequencyMS: 15000,
      };
    } else if (isBuildTime) {
      // 构建时：增强配置
      return {
        ...baseConfig,
        maxPoolSize: 50,
        minPoolSize: 10,
        connectTimeoutMS: 30000,
        socketTimeoutMS: 120000,
        serverSelectionTimeoutMS: 30000,
        heartbeatFrequencyMS: 30000,
        maxIdleTimeMS: 60000,
      };
    } else {
      // 运行时：平衡配置
      return {
        ...baseConfig,
        maxPoolSize: 5,        // 合理的最大连接数
        minPoolSize: 2,        // 合理的最小连接数
        connectTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        serverSelectionTimeoutMS: 5000,
        heartbeatFrequencyMS: 10000,
        maxIdleTimeMS: 30000,
      };
    }
  };

  // 创建新的连接
  connectionPromise = (async () => {
    const config = getConnectionConfig();
    const connectionString = getMongoConnectionString();
    const client = new MongoClient(connectionString, config);

    try {
      await client.connect();
      console.log(`成功连接到 MongoDB! (${isTestMode ? 'test' : (isBuildTime ? 'build' : 'runtime')} 模式)`);
      cachedClient = client;

      return {
        client,
        db: client.db(env.MONGODB_DB_NAME)
      };
    } catch (err) {
      console.error('连接 MongoDB 时出错:', err);
      connectionPromise = null; // 重置连接Promise以允许重试
      throw err;
    }
  })();

  return connectionPromise;
}
