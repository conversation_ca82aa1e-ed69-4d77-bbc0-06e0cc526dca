/**
 * 亚马逊联盟配置
 * 管理不同语言/地区的亚马逊站点映射
 */

export interface AmazonSiteConfig {
  domain: string;
  currency: string;
  language: string;
  countryCode: string;
  name: string;
}

// 亚马逊站点配置
export const AMAZON_SITES: Record<string, AmazonSiteConfig> = {
  'en': {
    domain: 'amazon.com',
    currency: 'USD',
    language: 'en',
    countryCode: 'US',
    name: 'Amazon.com'
  },
  'fr': {
    domain: 'amazon.fr',
    currency: 'EUR',
    language: 'fr',
    countryCode: 'FR',
    name: 'Amazon.fr'
  },
  'de': {
    domain: 'amazon.de',
    currency: 'EUR',
    language: 'de',
    countryCode: 'DE',
    name: 'Amazon.de'
  },
  'es': {
    domain: 'amazon.es',
    currency: 'EUR',
    language: 'es',
    countryCode: 'ES',
    name: 'Amazon.es'
  },
  'zh': {
    domain: 'amazon.com',
    currency: 'USD',
    language: 'en',
    countryCode: 'US',
    name: 'Amazon.com'
  },
  'pt': {
    domain: 'amazon.com',
    currency: 'USD',
    language: 'en',
    countryCode: 'US',
    name: 'Amazon.com'
  }
};

// 广告显示配置
export interface AffiliateDisplayConfig {
  // 是否显示多语言文本
  showLocalizedText: boolean;
  // 是否使用本地化链接（如果可用）
  useLocalizedLinks: boolean;
  // 是否显示链接目标说明
  showLinkDestination: boolean;
  // 默认回退到英文站点
  fallbackToEnglish: boolean;
}

// 默认配置
export const DEFAULT_AFFILIATE_CONFIG: AffiliateDisplayConfig = {
  showLocalizedText: true,
  useLocalizedLinks: false, // 暂时禁用，因为大多数产品只有英文链接
  showLinkDestination: true,
  fallbackToEnglish: true
};

/**
 * 获取指定语言的亚马逊站点配置
 */
export function getAmazonSiteConfig(locale: string): AmazonSiteConfig {
  return AMAZON_SITES[locale] || AMAZON_SITES['en'];
}

/**
 * 获取当前的广告显示配置（支持从 localStorage 读取）
 */
export function getCurrentAffiliateConfig(): AffiliateDisplayConfig {
  if (typeof window === 'undefined') {
    return DEFAULT_AFFILIATE_CONFIG;
  }

  try {
    const savedConfig = localStorage.getItem('affiliateDisplayConfig');
    if (savedConfig) {
      return { ...DEFAULT_AFFILIATE_CONFIG, ...JSON.parse(savedConfig) };
    }
  } catch (error) {
    console.error('Failed to load affiliate config:', error);
  }

  return DEFAULT_AFFILIATE_CONFIG;
}

/**
 * 根据语言和配置获取最适合的联盟链接
 */
export function getOptimalAffiliateUrl(
  product: { affiliateUrl: string; translations?: Record<string, { affiliateUrl?: string }> },
  locale: string,
  config: AffiliateDisplayConfig = getCurrentAffiliateConfig()
): string {
  // 如果启用本地化链接且存在对应语言的链接
  if (config.useLocalizedLinks && product.translations?.[locale]?.affiliateUrl) {
    return product.translations[locale].affiliateUrl!;
  }
  
  // 否则使用默认链接
  return product.affiliateUrl;
}

/**
 * 获取链接目标站点的显示名称
 */
export function getLinkDestinationName(
  product: { affiliateUrl: string; translations?: Record<string, { affiliateUrl?: string }> },
  locale: string,
  config: AffiliateDisplayConfig = getCurrentAffiliateConfig()
): string {
  const url = getOptimalAffiliateUrl(product, locale, config);
  
  // 从URL中提取域名
  try {
    const domain = new URL(url).hostname.toLowerCase();
    
    // 匹配亚马逊站点
    for (const [, siteConfig] of Object.entries(AMAZON_SITES)) {
      if (domain.includes(siteConfig.domain)) {
        return siteConfig.name;
      }
    }
    
    // 如果没有匹配到，返回域名
    return domain;
  } catch {
    return 'Amazon';
  }
}
