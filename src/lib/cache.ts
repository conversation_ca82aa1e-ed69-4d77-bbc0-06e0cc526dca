import { cache } from 'react';
import { unstable_cache } from 'next/cache';
import { connectToDatabase } from './mongodb';
import { routing } from '@/i18n/routing';

/**
 * 缓存数据库查询结果，用于静态生成 - 内存优化版本
 */
export const getCachedBrands = unstable_cache(
  async (locale: string, type?: string) => {
    const { db } = await connectToDatabase();
    const query: any = { language: locale };

    if (type && type !== 'all') {
      query.tractor_type = type;
    }

    const result = await db.collection('brands')
      .find(query)
      .project({
        _id: 1,
        brand_name: 1,
        brand_logo: 1,
        brand_key: 1,
        tractor_type: 1,
        power_range: 1,
        years_range: 1,
        brand_introduction: 1
      })
      .limit(50)  // 限制返回数量以节省内存
      .toArray();

    // 手动触发垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }

    return result;
  },
  ['brands'],
  {
    revalidate: 7200, // 增加到2小时，减少缓存更新频率
    tags: ['brands']
  }
);

/**
 * 缓存新闻数据
 */
export const getCachedNews = unstable_cache(
  async (locale: string, limit: number = 50) => {
    const { db } = await connectToDatabase();
    
    return await db.collection('news')
      .find({ language: locale })
      .sort({ date: -1 })
      .limit(limit)
      .toArray();
  },
  ['news'],
  {
    revalidate: 1800, // 30分钟
    tags: ['news']
  }
);

/**
 * 缓存型号数据
 */
export const getCachedModels = unstable_cache(
  async (brandKey?: string) => {
    const { db } = await connectToDatabase();
    const query = brandKey ? { brand_key: brandKey } : {};
    
    return await db.collection('models')
      .find(query)
      .project({
        _id: 1,
        model_key: 1,
        model_name: 1,
        brand_key: 1,
        power: 1,
        tractor_type: 1,
        years_range: 1
      })
      .toArray();
  },
  ['models'],
  {
    revalidate: 86400, // 24小时
    tags: ['models']
  }
);

/**
 * 获取所有支持的语言参数
 */
export function getAllLocaleParams() {
  return routing.locales.map((locale) => ({
    locale: locale,
  }));
}

/**
 * 页面级缓存包装器
 */
export const withPageCache = <T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  cacheKey: string,
  revalidate: number = 3600
) => {
  return cache(async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error(`Cache error for ${cacheKey}:`, error);
      throw error;
    }
  });
};

/**
 * 构建时数据预取
 */
export async function prefetchBuildData() {
  console.log('开始预取构建数据...');
  
  try {
    // 预取所有语言的品牌数据
    for (const locale of routing.locales) {
      await getCachedBrands(locale);
      await getCachedNews(locale, 10);
      console.log(`✓ 预取 ${locale} 语言数据完成`);
    }
    
    // 预取型号数据（不分语言）
    await getCachedModels();
    console.log('✓ 预取型号数据完成');
    
    console.log('数据预取完成');
  } catch (error) {
    console.error('数据预取失败:', error);
    throw error;
  }
}
