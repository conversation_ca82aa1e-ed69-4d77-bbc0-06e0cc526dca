import { AffiliateProduct, PageContext } from './types';
import { connectToDatabase } from './mongodb';

/**
 * 产品匹配服务
 * 根据页面上下文智能匹配相关的联盟产品
 */
export class ProductMatcher {
  /**
   * 根据页面上下文获取匹配的产品
   */
  static async getProductsForPage(
    pageContext: PageContext, 
    maxProducts: number = 4
  ): Promise<AffiliateProduct[]> {
    const { pageType, brandSlug, tractorModel, newsSlug } = pageContext;
    
    try {
      const { db } = await connectToDatabase();
      const collection = db.collection('affiliate_products');
      
      // 获取所有启用的产品
      const enabledProductsData = await collection.find({ enabled: true }).toArray();
      
      // 转换 MongoDB 文档为 AffiliateProduct 类型
      const enabledProducts: AffiliateProduct[] = enabledProductsData.map(doc => ({
        id: doc.id || doc._id.toString(),
        title: doc.title,
        description: doc.description,
        imageUrl: doc.imageUrl,
        affiliateUrl: doc.affiliateUrl,
        category: doc.category,
        enabled: doc.enabled,
        rating: doc.rating || 0,
        reviewCount: doc.reviewCount || 0,
        tags: doc.tags || [],
        priority: doc.priority || 0,
        isHotPick: doc.isHotPick || false,
        isOnSale: doc.isOnSale || false,
        targetPages: doc.targetPages || ['home'],
        brandMatch: doc.brandMatch || [],
        modelMatch: doc.modelMatch || [],
        clickCount: doc.clickCount || 0,
        impressionCount: doc.impressionCount || 0,
        clickRate: doc.clickRate || 0,
        createdAt: doc.createdAt || new Date(),
        updatedAt: doc.updatedAt || new Date(),
        translations: doc.translations || {}
      }));
    
    // 根据页面类型进行匹配
    let matchedProducts: AffiliateProduct[] = [];
    
    switch (pageType) {
      case 'brand':
        matchedProducts = this.matchProductsForBrandPage(enabledProducts, brandSlug);
        break;
      case 'tractor':
        matchedProducts = this.matchProductsForTractorPage(enabledProducts, tractorModel);
        break;
      case 'news':
        matchedProducts = this.matchProductsForNewsPage(enabledProducts, newsSlug);
        break;
      case 'home':
      default:
        matchedProducts = this.matchProductsForHomePage(enabledProducts);
        break;
    }
    
    // 按优先级和点击率排序
    const sortedProducts = this.sortProductsByScore(matchedProducts, pageContext);
    
    // 返回指定数量的产品
    return sortedProducts.slice(0, maxProducts);
    } catch (error) {
      console.error('Error fetching products for page:', error);
      return [];
    }
  }

  /**
   * 为品牌页面匹配产品
   */
  private static matchProductsForBrandPage(
    products: AffiliateProduct[], 
    brandSlug?: string
  ): AffiliateProduct[] {
    if (!brandSlug) {
      return this.getGeneralProducts(products);
    }

    // 1. 优先匹配品牌相关产品
    const brandMatched = products.filter(product => 
      product.brandMatch?.includes(brandSlug) ||
      product.tags.some(tag => tag.includes(brandSlug.replace('-', '_')))
    );

    // 2. 匹配适合品牌页面的产品
    const pageMatched = products.filter(product => 
      product.targetPages.includes('brand')
    );

    // 3. 合并并去重
    const matched = this.mergeAndDeduplicateProducts([brandMatched, pageMatched]);

    // 4. 如果匹配的产品不够，补充通用产品
    if (matched.length < 4) {
      const general = this.getGeneralProducts(products)
        .filter(p => !matched.find(m => m.id === p.id));
      matched.push(...general.slice(0, 4 - matched.length));
    }

    return matched;
  }

  /**
   * 为拖拉机详情页匹配产品
   */
  private static matchProductsForTractorPage(
    products: AffiliateProduct[], 
    tractorModel?: string
  ): AffiliateProduct[] {
    // 1. 匹配特定型号的产品
    const modelMatched = tractorModel 
      ? products.filter(product => 
          product.modelMatch?.includes(tractorModel) ||
          product.tags.some(tag => tractorModel.toLowerCase().includes(tag))
        )
      : [];

    // 2. 匹配拖拉机相关的配件和工具
    const tractorRelated = products.filter(product => 
      product.category === 'parts' || 
      product.category === 'equipment' ||
      product.targetPages.includes('tractor') ||
      product.tags.some(tag => ['maintenance', 'repair', 'parts', 'hydraulic'].includes(tag))
    );

    // 3. 合并并去重
    const matched = this.mergeAndDeduplicateProducts([
      ...modelMatched,
      ...tractorRelated
    ]);

    // 4. 如果不够，补充通用产品
    if (matched.length < 4) {
      const general = this.getGeneralProducts(products)
        .filter(p => !matched.find(m => m.id === p.id));
      matched.push(...general.slice(0, 4 - matched.length));
    }

    return matched;
  }

  /**
   * 为新闻页面匹配产品
   */
  private static matchProductsForNewsPage(
    products: AffiliateProduct[], 
    newsSlug?: string
  ): AffiliateProduct[] {
    // 1. 匹配新闻页面适合的产品
    const newsMatched = products.filter(product => 
      product.targetPages.includes('news')
    );

    // 2. 匹配教育性和参考性产品（如书籍）
    const educationalProducts = products.filter(product => 
      product.category === 'accessories' &&
      product.tags.some(tag => ['book', 'reference', 'guide'].includes(tag))
    );

    // 3. 合并并去重
    const matched = this.mergeAndDeduplicateProducts([
      ...newsMatched,
      ...educationalProducts
    ]);

    // 4. 如果不够，补充通用产品
    if (matched.length < 4) {
      const general = this.getGeneralProducts(products)
        .filter(p => !matched.find(m => m.id === p.id));
      matched.push(...general.slice(0, 4 - matched.length));
    }

    return matched;
  }

  /**
   * 为首页匹配产品
   */
  private static matchProductsForHomePage(products: AffiliateProduct[]): AffiliateProduct[] {
    // 首页显示热门推荐和高优先级产品
    return products.filter(product => 
      product.targetPages.includes('home')
    );
  }

  /**
   * 获取通用推荐产品
   */
  private static getGeneralProducts(products: AffiliateProduct[]): AffiliateProduct[] {
    return products.filter(product => 
      product.isHotPick || product.priority >= 8
    );
  }

  /**
   * 合并产品数组并去重
   */
  private static mergeAndDeduplicateProducts(productArrays: AffiliateProduct[]): AffiliateProduct[] {
    const seen = new Set<string>();
    const result: AffiliateProduct[] = [];

    for (const product of productArrays) {
      if (!seen.has(product.id)) {
        seen.add(product.id);
        result.push(product);
      }
    }

    return result;
  }

  /**
   * 根据评分对产品进行排序
   */
  private static sortProductsByScore(
    products: AffiliateProduct[], 
    pageContext: PageContext
  ): AffiliateProduct[] {
    return products.sort((a, b) => {
      const scoreA = this.calculateProductScore(a, pageContext);
      const scoreB = this.calculateProductScore(b, pageContext);
      return scoreB - scoreA; // 降序排列
    });
  }

  /**
   * 计算产品在特定页面上下文中的评分
   */
  private static calculateProductScore(
    product: AffiliateProduct, 
    pageContext: PageContext
  ): number {
    let score = product.priority * 10; // 基础优先级分数

    // 匹配度加分
    if (this.isExactMatch(product, pageContext)) {
      score += 50;
    } else if (this.isCategoryMatch(product, pageContext)) {
      score += 30;
    } else if (this.isPageMatch(product, pageContext)) {
      score += 20;
    }

    // 性能加分
    score += product.clickRate * 100; // 点击率加分
    if (product.isHotPick) score += 25; // 热门推荐加分
    if (product.isOnSale) score += 15; // 特价加分
    if (product.rating && product.rating >= 4.5) score += 10; // 高评分加分

    // 时效性调整
    const daysSinceUpdate = this.getDaysSince(product.updatedAt);
    if (daysSinceUpdate > 30) {
      score *= 0.9; // 长时间未更新的产品降权
    }

    return score;
  }

  /**
   * 检查是否为精确匹配
   */
  private static isExactMatch(product: AffiliateProduct, pageContext: PageContext): boolean {
    const { pageType, brandSlug, tractorModel } = pageContext;

    if (pageType === 'brand' && brandSlug) {
      return product.brandMatch?.includes(brandSlug) || false;
    }

    if (pageType === 'tractor' && tractorModel) {
      return product.modelMatch?.includes(tractorModel) || false;
    }

    return false;
  }

  /**
   * 检查是否为分类匹配
   */
  private static isCategoryMatch(product: AffiliateProduct, pageContext: PageContext): boolean {
    const { pageType } = pageContext;

    if (pageType === 'tractor') {
      return product.category === 'parts' || product.category === 'equipment';
    }

    if (pageType === 'news') {
      return product.category === 'accessories' && 
             product.tags.some(tag => ['book', 'reference'].includes(tag));
    }

    return false;
  }

  /**
   * 检查是否为页面匹配
   */
  private static isPageMatch(product: AffiliateProduct, pageContext: PageContext): boolean {
    return product.targetPages.includes(pageContext.pageType);
  }

  /**
   * 计算距离指定日期的天数
   */
  private static getDaysSince(date: Date): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - new Date(date).getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}