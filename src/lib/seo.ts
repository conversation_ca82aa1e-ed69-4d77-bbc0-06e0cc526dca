import { Metadata } from 'next';

const BASE_URL = 'https://tractordata.site';
const SITE_NAME = 'TractorData';

interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product'; // Note: 'product' will be converted to 'website' for OpenGraph
  locale?: string;
  alternateLanguages?: Record<string, string>;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  noFollow?: boolean;
}

/**
 * 生成完整的SEO元数据
 */
export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords,
    image,
    url,
    type = 'website',
    locale = 'en',
    alternateLanguages = {},
    publishedTime,
    modifiedTime,
    author,
    section,
    tags = [],
    noIndex = false,
    noFollow = false
  } = config;

  // 构建完整的URL
  const fullUrl = url ? (url.startsWith('http') ? url : `${BASE_URL}${url}`) : BASE_URL;
  const fullImageUrl = image ? (image.startsWith('http') ? image : `${BASE_URL}${image}`) : `${BASE_URL}/logo.svg`;

  // 构建robots指令
  const robots = [];
  if (noIndex) robots.push('noindex');
  if (noFollow) robots.push('nofollow');
  if (robots.length === 0) robots.push('index', 'follow');

  const metadata: Metadata = {
    metadataBase: new URL(BASE_URL),
    title: {
      default: title,
      template: `%s | ${SITE_NAME}`
    },
    description,
    keywords: keywords || tags.join(', '),
    robots: robots.join(', '),
    
    // Open Graph
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName: SITE_NAME,
      locale,
      type: type === 'article' ? 'article' : 'website', // Only use valid OpenGraph types
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
    },

    // Twitter Cards
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [fullImageUrl],
      creator: '@tractordata',
      site: '@tractordata',
    },

    // 其他元标签
    other: {
      'theme-color': '#2a5234',
      'msapplication-TileColor': '#2a5234',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'format-detection': 'telephone=no',
    }
  };

  // 添加文章特定的元数据
  if (type === 'article') {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(author && { authors: [author] }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags })
    };
  }

  // 添加产品特定的元数据
  if (type === 'product') {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'website' // OpenGraph doesn't support 'product' type, use 'website' instead
    };
  }

  // 添加多语言支持
  if (Object.keys(alternateLanguages).length > 0) {
    metadata.alternates = {
      canonical: fullUrl,
      languages: alternateLanguages
    };
  }

  return metadata;
}

/**
 * 生成品牌页面SEO元数据
 */
export function generateBrandSEOMetadata(
  brandData: any,
  locale: string = 'en',
  translations: any = {}
): Metadata {
  const brandName = brandData.name;
  const modelCount = brandData.model_count || 0;
  
  const title = translations.title ||
    `${brandName} Tractors - Specifications & Models`;

  const description = translations.description ||
    `Explore ${brandName} tractor models, specifications, and detailed information. Find ${modelCount} ${brandName} tractor models with complete technical details.`;

  const keywords = `${brandName}, ${brandName} tractors, ${brandName} specifications, farm equipment, agricultural machinery, tractor models`;

  return generateSEOMetadata({
    title,
    description,
    keywords,
    image: brandData.logo,
    url: `/${locale === 'en' ? '' : locale + '/'}brands/${brandData.brand_key}`,
    type: 'website',
    locale
  });
}

/**
 * 生成拖拉机型号页面SEO元数据
 */
export function generateTractorSEOMetadata(
  tractorData: any,
  locale: string = 'en',
  translations: any = {}
): Metadata {
  const brandName = tractorData.brand_name;
  const modelName = tractorData.model_name;
  const fullName = `${brandName} ${modelName}`;
  
  const title = translations.title ||
    `${fullName} - Specifications & Details`;

  const description = translations.description ||
    `Detailed specifications for ${fullName} tractor. Engine power, dimensions, weight, transmission details and complete technical information.`;

  const keywords = `${brandName}, ${modelName}, ${fullName}, tractor specifications, ${brandName} ${modelName} specs, farm equipment`;

  return generateSEOMetadata({
    title,
    description,
    keywords,
    image: tractorData.image_url,
    url: `/${locale === 'en' ? '' : locale + '/'}tractors/${tractorData.brand_key}/${tractorData.model_key}`,
    type: 'product',
    locale
  });
}

/**
 * 生成新闻文章SEO元数据
 */
export function generateNewsSEOMetadata(
  newsData: any,
  locale: string = 'en',
  translations: any = {}
): Metadata {
  const title = newsData.title;
  const description = newsData.summary || newsData.excerpt || 
    `${title.substring(0, 150)}...`;

  const keywords = newsData.tags?.join(', ') || 
    'agricultural news, tractor news, farm equipment, farming industry';

  return generateSEOMetadata({
    title,
    description,
    keywords,
    image: newsData.featured_image || newsData.image,
    url: `/${locale === 'en' ? '' : locale + '/'}news/${newsData.slug}`,
    type: 'article',
    locale,
    publishedTime: newsData.published_date || newsData.date,
    modifiedTime: newsData.updated_date,
    author: newsData.author,
    section: newsData.category || 'Agricultural News',
    tags: newsData.tags || []
  });
}

/**
 * 生成首页SEO元数据
 */
export function generateHomeSEOMetadata(
  locale: string = 'en',
  translations: any = {}
): Metadata {
  const title = translations.title || 'TractorData - Comprehensive Tractor Database';
  const description = translations.description || 
    'Find detailed specifications, comparisons, and information for all makes and models of farm tractors. The most comprehensive tractor database online.';

  const keywords = translations.keywords || 
    'tractors, farm equipment, agricultural machinery, tractor specs, John Deere, Kubota, Case IH, New Holland, Massey Ferguson';

  return generateSEOMetadata({
    title,
    description,
    keywords,
    url: locale === 'en' ? '/' : `/${locale}/`,
    type: 'website',
    locale
  });
}
