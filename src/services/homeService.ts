import { cache } from 'react';
import { connectToDatabase } from '@/lib/mongodb';
import { SimpleBrand } from './brandService';
import { routing } from '@/i18n/routing';
import { NewsArticle } from './newsService';
import { createSlug } from '@/lib/utils';

export interface HomePageData {
  farmBrands: SimpleBrand[];
  lawnBrands: SimpleBrand[];
  news: NewsArticle[];
}

/**
 * 获取首页所需的所有数据，一次性获取以减少数据库连接和查询次数
 * 使用 cache() 函数进一步提高性能
 *
 * @param locale 当前语言
 * @returns 包含所有首页数据的对象
 */
export const getHomePageData = cache(async (
  locale: string = 'en'
): Promise<HomePageData> => {
  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = routing.locales.includes(locale as any) ? locale : 'en';

    // 只建立一次数据库连接
    const { db } = await connectToDatabase();

    // 并行执行所有查询，而不是串行执行
    const [farmBrands, lawnBrands, news] = await Promise.all([
      // 获取农场拖拉机品牌
      db.collection('brands')
        .find({
          tractor_type: 'farm',
          language: targetLocale
        })
        .project({
          _id: 1,
          brand_name: 1,
          brand_logo: 1,
          brand_key: 1
        })
        .limit(10)
        .toArray(),

      // 获取草坪拖拉机品牌
      db.collection('brands')
        .find({
          tractor_type: 'lawn',
          language: targetLocale
        })
        .project({
          _id: 1,
          brand_name: 1,
          brand_logo: 1,
          brand_key: 1
        })
        .limit(10)
        .toArray(),

      // 获取最新新闻
      db.collection('news')
        .find({ language: targetLocale })
        .sort({ date: -1 })
        .limit(4)
        .toArray()
    ]);

    // 处理品牌数据
    const formattedFarmBrands = farmBrands.map(brand => ({
      ...brand,
      _id: brand._id.toString(),
      slug: brand.brand_key || createSlug(brand.brand_name || '')
    })) as SimpleBrand[];

    const formattedLawnBrands = lawnBrands.map(brand => ({
      ...brand,
      _id: brand._id.toString(),
      slug: brand.brand_key || createSlug(brand.brand_name || '')
    })) as SimpleBrand[];

    // 处理新闻数据
    const formattedNews = news.map(item => ({
      ...item,
      _id: item._id.toString()
    })) as NewsArticle[];

    return {
      farmBrands: formattedFarmBrands,
      lawnBrands: formattedLawnBrands,
      news: formattedNews
    };
  } catch (error) {
    console.error('获取首页数据失败:', error);
    // 返回空数据，保证即使发生错误页面也不会崩溃
    return {
      farmBrands: [],
      lawnBrands: [],
      news: []
    };
  }
});
