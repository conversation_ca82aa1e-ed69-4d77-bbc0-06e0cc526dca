import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { routing } from '@/i18n/routing';

// 使用routing中定义的支持语言
export const SUPPORTED_LOCALES = routing.locales;

// 定义与brands表相匹配的类型
export type Brand = {
  _id?: string;
  id?: string;
  brand_introduction?: string;
  models?: Array<{
    factory?: string;
    model_key?: string;
    model_name?: string;
    model_url?: string;
    power?: string;
    series?: string;
    years_range?: string;
  }>;
  power_range?: string;
  tractor_brand?: string;
  tractor_type?: string;
  years_range?: string;
  brand_key?: string;
  related_brands?: any[];
  tractor_brand_url?: string;
  brand_logo?: string;
  tractor_count?: string;
  brand_name?: string;
  brand_website?: string;
  slug: string;
  language?: string;
  history?: Array<{ year: string; title: string; description: string }>;
  [key: string]: any;
};

// 定义Model类型
export type Model = {
  _id?: string;
  factory?: string;
  model_key?: string;
  power?: string;
  series?: string;
  years_range?: string;
  brand_key?: string;
  tractor_brand?: string;
  tractor_type?: string;
  model_name: string;
  model_url?: string;
};

// 简化的品牌数据类型，只包含首页展示所需字段
export type SimpleBrand = {
  _id: string;
  brand_name: string;
  brand_logo?: string;
  brand_key?: string;
  slug: string;
};

// 分页结果类型
export type PaginatedResult<T> = {
  data: T[];
  pagination: {
    total: number;
    page: number;
    pages: number;
    limit: number;
  };
};

// 品牌详情结果类型
export type BrandDetailResult = {
  brand: Brand;
  models: Model[];
};

/**
 * 获取特定类型的前10个品牌，仅包含展示所需的最少字段
 * @param type 品牌类型：'farm'或'lawn'，默认为'all'
 * @param locale 语言代码，默认为'en'
 * @returns 简化的品牌数据数组
 */
export async function getTopBrands(type: string = 'all', locale: string = 'en'): Promise<SimpleBrand[]> {
  const { db } = await connectToDatabase();
  const collection = db.collection('brands');

  const query: any = {};

  // 应用类型过滤
  if (type && type !== 'all') {
    query.tractor_type = type;
  }

  // 验证语言，如果不支持则使用英语
  const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';
  query.language = targetLocale;

  // 只查询所需的最少字段
  const projection = {
    _id: 1,
    brand_name: 1,
    brand_logo: 1,
    brand_key: 1
  };

  // 获取前10条记录
  const brands = await collection.find(query)
    .project(projection)
    .limit(10)
    .toArray();

  // 转换MongoDB的_id字段为字符串，添加slug字段
  return brands.map(doc => ({
    ...doc,
    _id: doc._id.toString(),
    slug: doc.brand_key || createSlug(doc.brand_name || '')
  })) as SimpleBrand[];
}

/**
 * 获取所有品牌（支持分页）
 * @param type 品牌类型：'farm'或'lawn'，默认为'all'
 * @param search 搜索关键词
 * @param locale 语言代码，默认为'en'
 * @param page 页码，默认为1
 * @param limit 每页数量，默认为8
 * @returns 分页的品牌数据
 */
export async function getAllBrands(
  type: string = 'all',
  search?: string,
  locale: string = 'en',
  page: number = 1,
  limit: number = 8
): Promise<PaginatedResult<Brand>> {
  const { db } = await connectToDatabase();
  const collection = db.collection('brands');

  const query: any = {};

  // 应用类型过滤
  if (type && type !== 'all') {
    query.tractor_type = type;
  }

  // 验证语言，如果不支持则使用英语
  const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';
  query.language = targetLocale;

  // 应用搜索过滤
  if (search) {
    query.$or = [
      { brand_name: { $regex: search, $options: 'i' } },
      { brand_introduction: { $regex: search, $options: 'i' } }
    ];
  }

  // 定义需要查询的字段
  const projection = {
    _id: 1,
    brand_name: 1,
    brand_introduction: 1,
    brand_key: 1,
    tractor_type: 1,
    power_range: 1,
    years_range: 1,
    tractor_count: 1,
    brand_website: 1,
    brand_logo: 1,
    tractor_brand: 1
  };

  // 获取总数
  const total = await collection.countDocuments(query);

  // 计算总页数
  const pages = Math.ceil(total / limit);

  // 获取当前页的数据
  const brands = await collection.find(query)
    .project(projection)
    .skip((page - 1) * limit)
    .limit(limit)
    .toArray();

  // 转换MongoDB的_id字段为字符串，添加slug字段
  const data = brands.map(doc => ({
    ...doc,
    _id: doc._id.toString(),
    slug: doc.brand_key || createSlug(doc.brand_name || '')
  })) as Brand[];

  return {
    data,
    pagination: {
      total,
      page,
      pages,
      limit
    }
  };
}

/**
 * 根据ID获取品牌详情
 * @param id 品牌ID或slug
 * @param locale 语言代码，默认为'en'
 * @returns 品牌详情或null
 */
export async function getBrandById(id: string, locale: string = 'en'): Promise<Brand | null> {
  const { db } = await connectToDatabase();
  const collection = db.collection('brands');

  try {
    let brand;
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';

    // 尝试使用ObjectId查询
    if (ObjectId.isValid(id)) {
      brand = await collection.findOne({
        _id: new ObjectId(id),
        language: targetLocale
      });
    }

    // 如果通过ID没找到，尝试使用brand_key或slug查询
    if (!brand) {
      brand = await collection.findOne({
        $or: [
          { brand_key: id },
          { slug: id }
        ],
        language: targetLocale
      });
    }

    if (!brand) return null;

    // 转换MongoDB的_id字段为字符串
    return {
      ...brand,
      _id: brand._id.toString(),
      slug: brand.brand_key || createSlug(brand.brand_name || '')
    } as Brand;
  } catch (error) {
    console.error('Error fetching brand by ID:', error);
    return null;
  }
}

/**
 * 根据slug获取品牌详情和关联的模型
 * @param slug 品牌slug
 * @param locale 语言代码，默认为'en'
 * @returns 品牌详情和模型列表，如果未找到则返回null
 */
export async function getBrandBySlug(slug: string, locale: string = 'en'): Promise<BrandDetailResult | null> {
  const { db } = await connectToDatabase();

  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';
    // console.log(targetLocale, 'targetLocale')

    // 获取品牌信息
    const brandsCollection = db.collection('brands');
    const brand = await brandsCollection.findOne({
      $or: [
        { brand_key: slug },
        { slug: slug }
      ],
      language: targetLocale
    });

    if (!brand) {
      return null;
    }

    // 获取关联的模型信息
    const modelsCollection = db.collection('models');
    const modelsData = await modelsCollection.find({ brand_key: brand.brand_key }).toArray();

    // 转换模型数据为Model类型
    const models: Model[] = modelsData.map(doc => ({
      _id: doc._id.toString(),
      factory: doc.factory,
      model_key: doc.model_key,
      power: doc.power,
      series: doc.series,
      years_range: doc.years_range,
      brand_key: doc.brand_key,
      tractor_brand: doc.tractor_brand,
      tractor_type: doc.tractor_type,
      model_name: doc.model_name || '',
      model_url: doc.model_url
    }));

    // 转换品牌数据
    const formattedBrand: Brand = {
      ...brand,
      _id: brand._id.toString(),
      id: brand.id || brand._id.toString(),
      slug: brand.brand_key || createSlug(brand.brand_name || ''),
      name: brand.brand_name,
      description: brand.brand_introduction || `${brand.brand_name}是拖拉机行业的知名品牌`,
      type: brand.tractor_type,
      power_range: brand.power_range,
      years_range: brand.years_range,
      website: brand.brand_website,
      logo: brand.brand_logo,
      related_brands: brand.related_brands || [],
      history: brand.history || []
    };

    return {
      brand: formattedBrand,
      models
    };
  } catch (error) {
    console.error('获取品牌数据失败:', error);
    return null;
  }
}

// 创建URL友好的slug
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}
