import {connectToDatabase} from "@/lib/mongodb";
import {cache} from "react";
import {routing} from "@/i18n/routing";

export interface Brand {
    _id: string;
    brand_key: string;
    brand_name: string;
    logo_url: string;
}

export interface Model {
    _id: string;
    model_key: string;
    model_name: string;
    power: string;
    tractor_type: string;
}

export interface TractorDetails {
    _id: string;
    model_name: string;
    power: string;
    tractor_type: string;
    years_range: string;
    tractor_brand: string;
    photos: {
        url: string;
        title: string;
    }[];
}

/**
 * 直接从数据库获取所有品牌数据
 * 使用 React cache 避免重复请求
 * @param locale 当前语言，默认为'en'
 */
export const getAllBrands = cache(async (locale: string = 'en'): Promise<Brand[]> => {
    try {
        const { db } = await connectToDatabase();

        // 验证语言，如果不支持则使用英语
        const targetLocale = routing.locales.includes(locale as any) ? locale : 'en';

        const brands = await db.collection('brands')
            .find({ language: targetLocale })
            .project({
                _id: 1,
                brand_key: 1,
                brand_name: 1,
                logo_url: 1
            })
            .sort({ brand_name: 1 })
            .toArray();

        // 将MongoDB对象序列化为纯JavaScript对象
        // 这样可以避免在服务器组件到客户端组件传递时的序列化问题
        const serializedBrands = brands.map(brand => ({
            _id: brand._id.toString(), // 将ObjectId转换为字符串
            brand_key: brand.brand_key,
            brand_name: brand.brand_name,
            logo_url: brand.logo_url || ''
        }));

        return serializedBrands;
    } catch (error) {
        console.error('Error fetching brands from database:', error);
        return [];
    }
});