import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// 拖拉机模型数据类型定义
export type TractorModel = {
  _id?: string;
  model_key: string;
  factory: string;
  model_name: string;
  model_url: string;
  power: string;
  series: string;
  years_range: string;
  brand_key: string;
  tractor_brand: string;
  tractor_type: string;
  photos?: Array<{
    url: string;
    title: string;
  }>;
}

// 分页结果类型
export type PaginatedResult = {
  tractors: TractorModel[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  }
}

// 筛选选项类型
export type FilterOptions = {
  tractor_brands: string[];
  tractor_types: string[];
  factories: string[];
  power_range: { min: string, max: string };
  years_range: { min: number, max: number };
}

/**
 * 查询拖拉机模型数据，支持多种筛选条件
 */
export async function getTractorModels(filters: {
  tractor_type?: string,
  tractor_brand?: string,
  brand_key?: string,
  power_min?: string,
  power_max?: string,
  year_start?: number,
  year_end?: number,
  factory?: string,
  search?: string,
  sort?: string,
  page?: number,
  limit?: number
}): Promise<PaginatedResult> {
  const { db } = await connectToDatabase();
  const collection = db.collection('models');
  
  const query: any = {};
  
  // 拖拉机类型筛选
  if (filters.tractor_type && filters.tractor_type !== 'all') {
    query.tractor_type = filters.tractor_type;
  }
  
  // 品牌筛选
  if (filters.tractor_brand) {
    query.tractor_brand = { $regex: filters.tractor_brand, $options: 'i' };
  }
  
  // 品牌键筛选
  if (filters.brand_key) {
    query.brand_key = filters.brand_key;
  }
  
  // 功率范围筛选 - power字段是字符串类型
  if (filters.power_min || filters.power_max) {
    query.power = {};
    if (filters.power_min) {
      query.power.$gte = filters.power_min;
    }
    if (filters.power_max) {
      query.power.$lte = filters.power_max;
    }
  }
  
  // 年份范围筛选 - 需要解析years_range字段 (格式如 "1995 - 2001")
  if (filters.year_start || filters.year_end) {
    // 使用聚合管道进行更精确的年份筛选
    const aggregationPipeline = [];
    
    // 首先添加一个管道阶段来提取年份
    aggregationPipeline.push({
      $addFields: {
        extractedYears: {
          $regexFind: {
            input: "$years_range",
            regex: "(\\d{4})\\s*-\\s*(\\d{4})"
          }
        }
      }
    });
    
    // 然后添加一个阶段来解析这些年份为数字
    aggregationPipeline.push({
      $addFields: {
        startYear: { 
          $toInt: { $arrayElemAt: ["$extractedYears.captures", 0] } 
        },
        endYear: { 
          $toInt: { $arrayElemAt: ["$extractedYears.captures", 1] } 
        }
      }
    });
    
    // 最后添加筛选条件
    const matchCondition: any = { $and: [] };
    
    if (filters.year_start) {
      // 结束年份必须大于等于开始筛选年份
      matchCondition.$and.push({ endYear: { $gte: filters.year_start } });
    }
    
    if (filters.year_end) {
      // 开始年份必须小于等于结束筛选年份
      matchCondition.$and.push({ startYear: { $lte: filters.year_end } });
    }
    
    aggregationPipeline.push({ $match: matchCondition });
    
    // 执行聚合查询
    const filteredIds = await collection.aggregate([
      ...aggregationPipeline,
      { $project: { _id: 1 } }
    ]).toArray();
    
    // 将筛选结果添加到主查询
    if (filteredIds.length > 0) {
      query._id = { $in: filteredIds.map(doc => doc._id) };
    } else {
      // 如果没有匹配的结果，返回空结果集
      query._id = { $in: [] };
    }
  }
  
  // 工厂筛选
  if (filters.factory) {
    query.factory = { $regex: filters.factory, $options: 'i' };
  }
  
  // 搜索功能 - 匹配多个字段
  if (filters.search) {
    query.$or = [
      { model_name: { $regex: filters.search, $options: 'i' } },
      { tractor_brand: { $regex: filters.search, $options: 'i' } },
      { model_name: { $regex: filters.search, $options: 'i' } }
    ];
  }
  
  // 分页
  const page = filters.page || 1;
  const limit = filters.limit || 20;
  const skip = (page - 1) * limit;
  
  // 排序
  const sortOption: any = {};
  if (filters.sort) {
    switch(filters.sort) {
      case 'power_asc':
        // 数值排序 - 需要将power字符串转为数值
        sortOption.power = 1;
        break;
      case 'power_desc':
        sortOption.power = -1;
        break;
      case 'year_desc':
        // 按年份排序 - 这里简化为按years_range字段排序
        sortOption.years_range = -1;
        break;
      case 'year_asc':
        sortOption.years_range = 1;
        break;
      default:
        // 默认排序
        sortOption.model_name = 1;
    }
  } else {
    // 默认排序
    sortOption.model_name = 1;
  }
  
  // 获取查询结果
  const tractors = await collection
    .find(query)
    .sort(sortOption)
    .skip(skip)
    .limit(limit)
    .toArray();
    
  // 获取总数用于分页
  const total = await collection.countDocuments(query);
  
  // 返回结果
  return {
    tractors: tractors.map(t => ({
      ...t,
      _id: t._id ? t._id.toString() : undefined
    })) as TractorModel[],
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };
}

/**
 * 获取单个拖拉机模型详情
 */
export async function getTractorModelByKey(modelKey: string): Promise<TractorModel | null> {
  const { db } = await connectToDatabase();
  const collection = db.collection('models');
  
  const model = await collection.findOne({ model_key: modelKey });
  
  if (!model) return null;
  
  return {
    ...model,
    _id: model._id ? model._id.toString() : undefined
  } as TractorModel;
}

/**
 * 获取筛选选项数据
 */
export async function getFilterOptions(): Promise<FilterOptions> {
  const { db } = await connectToDatabase();
  const collection = db.collection('models');
  
  // 获取唯一的筛选选项
  const brands = await collection.distinct('tractor_brand');
  const types = await collection.distinct('tractor_type');
  // 不再获取工厂数据
  
  // 获取功率范围
  const powerStats = await collection.aggregate([
    { $group: { 
      _id: null, 
      min: { $min: "$power" }, 
      max: { $max: "$power" } 
    }}
  ]).toArray();
  
  // 获取年份范围 - 需要从字符串中提取
  const yearsData = await collection.find({}, { projection: { years_range: 1 } }).toArray();
  let minYear = 2023, maxYear = 1900;
  
  yearsData.forEach(data => {
    if (data.years_range) {
      const match = data.years_range.match(/(\d{4})\s*-\s*(\d{4})/);
      if (match) {
        const start = parseInt(match[1]);
        const end = parseInt(match[2]);
        if (start < minYear) minYear = start;
        if (end > maxYear) maxYear = end;
      }
    }
  });
  
  // 如果没有有效数据，设置默认范围
  if (minYear > maxYear) {
    minYear = 1950;
    maxYear = new Date().getFullYear();
  }
  
  return {
    tractor_brands: brands,
    tractor_types: types,
    factories: [], // 返回空数组
    power_range: {
      min: powerStats[0]?.min || "0",
      max: powerStats[0]?.max || "650"
    },
    years_range: {
      min: minYear,
      max: maxYear
    }
  };
} 