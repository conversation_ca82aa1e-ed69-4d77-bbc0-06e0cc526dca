import { connectToDatabase } from '@/lib/mongodb';

// 定义与tractors表相匹配的类型
export type TableCell = {
    text?: string;
    links?: { href: string; text: string }[];
    images?: { src: string; alt?: string }[];
};

export type TableData = {
    index?: number;
    type?: string;
    title?: string;
    headers?: string[];
    rows: TableCell[][];
};

export type TractorDetailData = {
    url: string;
    brand?: string;
    model?: string;
    model_key: string;
    type: string;
    language: string;
    crawledAt: string;
    data: {
        overview?: TableData[];
        engine?: TableData[];
        transmission?: TableData[];
        dimensions?: TableData[];
        tests?: TableData[];
    };
    modelPhotos?: {
        url: string;
        title: string;
        description: string;
    }[];
};

// 从MongoDB直接获取拖拉机数据
export async function getTractorData(brand: string, model: string, language: string = 'en'): Promise<TractorDetailData | null> {
    try {
        const { db } = await connectToDatabase();
        const tractorsCollection = db.collection('tractors');
        const modelsCollection = db.collection('models');

        const modelKey = decodeURIComponent(model);

        // 首先尝试查找指定语言的记录
        // console.log(`查询 tractors 表中 model_key=${model}, language=${language} 的记录`);
        let tractorData = await tractorsCollection.findOne({
            model_key: modelKey,
            language: language
        });

        // 如果找不到指定语言的记录，则尝试查找英语版本
        if (!tractorData && language !== 'en') {
            console.log(`未找到 ${language} 语言的记录，尝试查找英语(en)版本`);
            tractorData = await tractorsCollection.findOne({
                model_key: modelKey,
                language: 'en'
            });
        }

        if (!tractorData) {
            console.log(`未找到符合条件的拖拉机数据: brand=${brand}, model_key=${model}, language=${language} 或 en`);
            return null;
        }

        // console.log(`找到了拖拉机数据`);

        // 从 models 表中获取照片数据
        // console.log(`查询 models 表中 model_key=${model} 的照片数据`);
        const modelData = await modelsCollection.findOne({
            model_key: decodeURIComponent(model)
        }, { projection: { photos: 1 } });

        // 准备brandAndModel字段
        const rawData = tractorData as any;

        // 使用已有的brand和model字段，或从标题和URL中提取
        const brandAndModel = rawData.brandAndModel ||
        rawData.brand && rawData.model ? `${rawData.brand} ${rawData.model}` :
            rawData.title || model;

        // 组装符合TractorDetailData格式的数据
        const formattedData: TractorDetailData = {
            ...rawData,
            brandAndModel, // 确保brandAndModel字段存在
            type: rawData.type || 'unknown',
            language: rawData.language || 'zh',
            crawledAt: rawData.crawledAt || new Date().toISOString(),
            data: rawData.data || {}
        };

        // 如果找到了 models 表中的照片数据，则添加到返回数据中
        if (modelData && modelData.photos) {
            formattedData.modelPhotos = modelData.photos;
        }

        return formattedData;
    } catch (error) {
        console.error('获取拖拉机数据时出错:', error);
        return null;
    }
}
