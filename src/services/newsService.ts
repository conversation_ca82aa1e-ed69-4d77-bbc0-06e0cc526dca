import { cache } from 'react';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { routing } from '@/i18n/routing';

// 使用routing中定义的支持语言
export const SUPPORTED_LOCALES = routing.locales;

// 定义与mongodb news表匹配的新闻类型
export interface NewsItem {
  _id?: string | ObjectId;
  id?: string;
  title: string;
  content_text: string;
  date: string;
  images: Array<{
    url: string;
    alt: string;
  }>;
  slug: string;
  source: string;
  year: number;
  crawled_at: string;
  has_detail: boolean;
  summary: string;
  language?: string;
}

// NewsArticle是前端组件使用的接口，与数据库结构直接对应
export interface NewsArticle {
  _id: string;
  title: string;
  content_text: string;
  date: string;
  images: Array<{
    url: string;
    alt: string;
  }>;
  slug: string;
  source: string;
  year: number;
  crawled_at: string;
  summary: string;
  language: string;
}

/**
 * 获取最新的新闻
 * @param locale 语言
 * @param limit 限制数量
 * @returns 新闻列表
 */
export const getLatestNews = cache(async (
  locale: string = 'en',
  limit: number = 8
): Promise<NewsArticle[]> => {
  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';

    const { db } = await connectToDatabase();

    // 查询数据库，按日期倒序排列
    const news = await db.collection('news')
      .find({ language: targetLocale })
      .sort({ date: -1 })
      .limit(limit)
      .toArray();

    // 将 _id 转换为字符串
    return news.map(item => ({
      ...item,
      _id: item._id.toString()
    })) as NewsArticle[];
  } catch (error) {
    console.error(`获取最新新闻时出错:`, error);
    return [];
  }
});

/**
 * 按来源获取新闻
 * @param source 新闻来源
 * @param locale 语言
 * @param limit 限制数量
 * @returns 新闻列表
 */
export const getNewsBySource = cache(async (
  source: string,
  locale: string = 'en',
  limit: number = 8
): Promise<NewsArticle[]> => {
  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';

    const { db } = await connectToDatabase();

    // 查询特定来源的新闻
    const news = await db.collection('news')
      .find({ source, language: targetLocale })
      .sort({ date: -1 })
      .limit(limit)
      .toArray();

    // 将 _id 转换为字符串
    return news.map(item => ({
      ...item,
      _id: item._id.toString()
    })) as NewsArticle[];
  } catch (error) {
    console.error(`获取${source}来源新闻时出错:`, error);
    return [];
  }
});

/**
 * 按年份获取新闻
 * @param year 年份
 * @param locale 语言
 * @param limit 限制数量
 * @returns 新闻列表
 */
export const getNewsByYear = cache(async (
  year: number,
  locale: string = 'en',
  limit: number = 8
): Promise<NewsArticle[]> => {
  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';

    const { db } = await connectToDatabase();

    // 查询特定年份的新闻
    const news = await db.collection('news')
      .find({ year, language: targetLocale })
      .sort({ date: -1 })
      .limit(limit)
      .toArray();

    // 将 _id 转换为字符串
    return news.map(item => ({
      ...item,
      _id: item._id.toString()
    })) as NewsArticle[];
  } catch (error) {
    console.error(`获取${year}年新闻时出错:`, error);
    return [];
  }
});

/**
 * 根据slug获取新闻详情
 * @param slug 新闻slug
 * @param locale 语言
 * @returns 新闻详情
 */
export const getNewsBySlug = cache(async (
  slug: string,
  locale: string = 'en'
): Promise<NewsArticle | null> => {
  try {
    // 验证语言，如果不支持则使用英语
    const targetLocale = SUPPORTED_LOCALES.includes(locale) ? locale : 'en';

    const { db } = await connectToDatabase();

    // 查询特定slug的新闻
    const news = await db.collection('news')
      .findOne({ slug, language: targetLocale });

    // 如果找不到指定语言的新闻，尝试查找英文版本
    if (!news && targetLocale !== 'en') {
      const enNews = await db.collection('news')
        .findOne({ slug, language: 'en' });

      if (enNews) {
        return {
          ...enNews,
          _id: enNews._id.toString()
        } as NewsArticle;
      }

      return null;
    }

    if (!news) return null;

    // 将 _id 转换为字符串
    return {
      ...news,
      _id: news._id.toString()
    } as NewsArticle;
  } catch (error) {
    console.error(`获取slug为${slug}的新闻时出错:`, error);
    return null;
  }
});

/**
 * 获取最新的拖拉机新闻 (保留原函数名以兼容现有代码)
 * 实际上是获取最新新闻
 */
export const getLatestTractorNews = cache(async (
  locale: string = 'en',
  limit: number = 4
): Promise<NewsArticle[]> => {
  return getLatestNews(locale, limit);
});