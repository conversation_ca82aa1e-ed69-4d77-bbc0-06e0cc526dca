import { NextRequest, NextResponse } from 'next/server';

interface WebVitalMetric {
  name: string;
  value: number;
  id: string;
  delta: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
  url: string;
  userAgent: string;
}

/**
 * Web Vitals 数据收集端点
 * 接收并处理Core Web Vitals指标
 */
export async function POST(request: NextRequest) {
  try {
    const metric: WebVitalMetric = await request.json();

    // 验证必需字段
    if (!metric.name || typeof metric.value !== 'number') {
      return NextResponse.json(
        { error: 'Invalid metric data' },
        { status: 400 }
      );
    }

    // 在生产环境中，你可以将数据发送到：
    // 1. 数据库存储
    // 2. 第三方分析服务
    // 3. 日志系统
    
    // 示例：记录到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vitals Metric:', {
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        url: metric.url,
        timestamp: new Date(metric.timestamp).toISOString()
      });
    }

    // 示例：发送到外部分析服务
    if (process.env.ANALYTICS_WEBHOOK_URL) {
      try {
        await fetch(process.env.ANALYTICS_WEBHOOK_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`,
          },
          body: JSON.stringify({
            ...metric,
            source: 'tractordata.site',
            environment: process.env.NODE_ENV,
          }),
        });
      } catch (error) {
        console.error('Failed to send to analytics service:', error);
        // 不要因为外部服务失败而让请求失败
      }
    }

    // 可以添加数据库存储逻辑
    // await saveMetricToDatabase(metric);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing web vitals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 获取Web Vitals统计数据（可选）
 */
export async function GET(request: NextRequest) {
  try {
    // 这里可以返回聚合的Web Vitals数据
    // 例如：平均值、百分位数等
    
    const stats = {
      message: 'Web Vitals endpoint is active',
      timestamp: new Date().toISOString(),
      // 可以添加实际的统计数据
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error getting web vitals stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
