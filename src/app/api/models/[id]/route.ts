import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // API 保护已在全局中间件中实现
    const id = params.id;
    
    // 检查ID是否有效
    if (!id) {
      return NextResponse.json(
        { error: '型号ID不能为空' },
        { status: 400 }
      );
    }
    
    const { db } = await connectToDatabase();
    
    // 根据ID的格式决定查询方式
    let model;
    
    // 检查ID是否为有效的ObjectId格式
    if (ObjectId.isValid(id)) {
      model = await db.collection('models').findOne({ _id: new ObjectId(id) });
    } else {
      // 如果不是ObjectId，使用model_key查询
      model = await db.collection('models').findOne({ model_key: id });
    }
    
    if (!model) {
      return NextResponse.json(
        { error: '找不到指定型号' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(model);
  } catch (error) {
    console.error('获取型号详情失败:', error);
    return NextResponse.json(
      { error: '获取型号详情失败' },
      { status: 500 }
    );
  }
} 