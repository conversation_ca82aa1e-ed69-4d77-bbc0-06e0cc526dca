import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    // API 保护已在全局中间件中实现
    
    const { searchParams } = new URL(request.url);
    
    // 获取品牌参数
    const brandKey = searchParams.get('brandKey');
    
    // 品牌参数必须存在
    if (!brandKey) {
      return NextResponse.json(
        { error: '品牌参数不能为空' },
        { status: 400 }
      );
    }
    
    const { db } = await connectToDatabase();
    
    // 执行数据库查询，获取该品牌下所有型号
    const models = await db.collection('models')
      .find({ brand_key: brandKey })
      .project({
        _id: 1,
        model_key: 1,
        model_name: 1,
        power: 1,
        tractor_type: 1,
      })
      .sort({ model_name: 1 }) // 按名称字母顺序排序
      .toArray();
    
    return NextResponse.json(models);
  } catch (error) {
    console.error('获取型号数据失败:', error);
    return NextResponse.json(
      { error: '获取型号数据失败' },
      { status: 500 }
    );
  }
} 