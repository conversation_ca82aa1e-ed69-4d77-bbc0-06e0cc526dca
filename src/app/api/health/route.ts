import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// 健康检查配置
const HEALTH_CHECK_CONFIG = {
  timeout: 5000, // 5秒超时
  requiredServices: ['database', 'filesystem', 'memory'],
  version: process.env.npm_package_version || '1.0.0',
  buildId: process.env.BUILD_ID || 'unknown',
  // 内存检查阈值配置 - 针对小内存环境优化
  memory: {
    heapUsageThreshold: 85, // 堆内存使用率阈值 (%) - 针对小堆内存环境调整
    systemUsageThreshold: 90, // 系统内存使用率阈值 (%)
    warningThreshold: 70, // 警告阈值 (%) - 更早预警
  }
};

// 检查数据库连接
async function checkDatabase(): Promise<{ status: 'healthy' | 'unhealthy'; message: string; responseTime: number }> {
  const startTime = Date.now();
  
  try {
    const { db } = await connectToDatabase();
    
    // 执行简单查询测试连接
    await db.admin().ping();
    
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      message: 'Database connection successful',
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'unhealthy',
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      responseTime
    };
  }
}

// 检查文件系统
function checkFilesystem(): { status: 'healthy' | 'unhealthy'; message: string } {
  try {
    const fs = require('fs');
    const path = require('path');
    
    // 检查关键目录是否存在
    const criticalPaths = [
      '.next',
      'public',
      'src'
    ];
    
    for (const criticalPath of criticalPaths) {
      if (!fs.existsSync(criticalPath)) {
        return {
          status: 'unhealthy',
          message: `Critical path missing: ${criticalPath}`
        };
      }
    }
    
    // 检查是否可以写入临时文件
    const tempFile = path.join(process.cwd(), '.health-check-temp');
    fs.writeFileSync(tempFile, 'health-check');
    fs.unlinkSync(tempFile);
    
    return {
      status: 'healthy',
      message: 'Filesystem access successful'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `Filesystem check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

// 检查内存使用情况 - 优化版本，更智能的检测逻辑
function checkMemory(): { status: 'healthy' | 'unhealthy' | 'warning'; message: string; usage: any; details?: any } {
  try {
    const memoryUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();

    // 转换为MB
    const usage = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
      systemTotal: Math.round(totalMemory / 1024 / 1024),
      systemFree: Math.round(freeMemory / 1024 / 1024)
    };

    // 计算使用率
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    const systemUsagePercent = ((totalMemory - freeMemory) / totalMemory) * 100;
    const rssUsagePercent = (memoryUsage.rss / totalMemory) * 100;

    const details = {
      heapUsagePercent: parseFloat(heapUsagePercent.toFixed(1)),
      systemUsagePercent: parseFloat(systemUsagePercent.toFixed(1)),
      rssUsagePercent: parseFloat(rssUsagePercent.toFixed(1)),
      thresholds: HEALTH_CHECK_CONFIG.memory
    };

    // 智能检测逻辑：
    // 1. 如果堆内存总量太小（<100MB），主要看RSS和系统内存
    // 2. 如果堆内存总量正常（>=100MB），才看堆内存使用率

    const isSmallHeap = memoryUsage.heapTotal < 100 * 1024 * 1024; // 100MB

    if (isSmallHeap) {
      // 小堆内存环境：主要检查RSS和系统内存
      if (rssUsagePercent > 50) { // RSS超过系统内存50%
        return {
          status: 'unhealthy',
          message: `High RSS memory usage: ${rssUsagePercent.toFixed(1)}% of system memory (${usage.rss}MB)`,
          usage,
          details
        };
      }

      if (systemUsagePercent > HEALTH_CHECK_CONFIG.memory.systemUsageThreshold) {
        return {
          status: 'unhealthy',
          message: `Critical system memory usage: ${systemUsagePercent.toFixed(1)}% (threshold: ${HEALTH_CHECK_CONFIG.memory.systemUsageThreshold}%)`,
          usage,
          details
        };
      }

      if (rssUsagePercent > 30) { // RSS超过系统内存30%给警告
        return {
          status: 'warning',
          message: `Moderate RSS memory usage: ${rssUsagePercent.toFixed(1)}% of system memory (${usage.rss}MB)`,
          usage,
          details
        };
      }

      return {
        status: 'healthy',
        message: `Memory usage normal (small heap mode) - RSS: ${usage.rss}MB (${rssUsagePercent.toFixed(1)}%), System: ${systemUsagePercent.toFixed(1)}%`,
        usage,
        details
      };
    } else {
      // 正常堆内存环境：使用原有逻辑
      if (heapUsagePercent > HEALTH_CHECK_CONFIG.memory.heapUsageThreshold) {
        return {
          status: 'unhealthy',
          message: `Critical heap usage: ${heapUsagePercent.toFixed(1)}% (threshold: ${HEALTH_CHECK_CONFIG.memory.heapUsageThreshold}%)`,
          usage,
          details
        };
      }

      if (systemUsagePercent > HEALTH_CHECK_CONFIG.memory.systemUsageThreshold) {
        return {
          status: 'unhealthy',
          message: `Critical system memory usage: ${systemUsagePercent.toFixed(1)}% (threshold: ${HEALTH_CHECK_CONFIG.memory.systemUsageThreshold}%)`,
          usage,
          details
        };
      }

      if (heapUsagePercent > HEALTH_CHECK_CONFIG.memory.warningThreshold) {
        return {
          status: 'warning',
          message: `High heap usage: ${heapUsagePercent.toFixed(1)}% (warning threshold: ${HEALTH_CHECK_CONFIG.memory.warningThreshold}%)`,
          usage,
          details
        };
      }

      return {
        status: 'healthy',
        message: `Memory usage normal - Heap: ${heapUsagePercent.toFixed(1)}%, System: ${systemUsagePercent.toFixed(1)}%`,
        usage,
        details
      };
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: `Memory check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      usage: null
    };
  }
}

// 获取应用状态信息
function getAppStatus() {
  return {
    version: HEALTH_CHECK_CONFIG.version,
    buildId: HEALTH_CHECK_CONFIG.buildId,
    nodeVersion: process.version,
    platform: process.platform,
    uptime: Math.round(process.uptime()),
    timestamp: new Date().toISOString(),
    pid: process.pid
  };
}

// GET 请求处理器
export async function GET(_request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // 并行执行所有健康检查
    const [databaseCheck, filesystemCheck, memoryCheck] = await Promise.all([
      checkDatabase(),
      Promise.resolve(checkFilesystem()),
      Promise.resolve(checkMemory())
    ]);
    
    const checks = {
      database: databaseCheck,
      filesystem: filesystemCheck,
      memory: memoryCheck
    };
    
    // 确定整体健康状态
    const hasUnhealthy = Object.values(checks).some(check => check.status === 'unhealthy');
    const hasWarning = Object.values(checks).some(check => check.status === 'warning');

    let overallStatus: 'healthy' | 'warning' | 'unhealthy';
    if (hasUnhealthy) {
      overallStatus = 'unhealthy';
    } else if (hasWarning) {
      overallStatus = 'warning';
    } else {
      overallStatus = 'healthy';
    }
    
    const responseTime = Date.now() - startTime;
    
    const healthResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime,
      app: getAppStatus(),
      checks,
      summary: {
        total: Object.keys(checks).length,
        healthy: Object.values(checks).filter(check => check.status === 'healthy').length,
        warning: Object.values(checks).filter(check => check.status === 'warning').length,
        unhealthy: Object.values(checks).filter(check => check.status === 'unhealthy').length
      }
    };
    
    // 根据健康状态返回相应的HTTP状态码
    let httpStatus: number;
    switch (overallStatus) {
      case 'healthy':
        httpStatus = 200;
        break;
      case 'warning':
        httpStatus = 200; // 警告状态仍返回200，但在响应体中标明warning
        break;
      case 'unhealthy':
        httpStatus = 503;
        break;
      default:
        httpStatus = 503;
    }
    
    return NextResponse.json(healthResponse, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      app: getAppStatus()
    };
    
    return NextResponse.json(errorResponse, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}

// HEAD 请求处理器（用于简单的存活检查）
export async function HEAD(_request: NextRequest) {
  try {
    // 简单的存活检查，不执行详细的健康检查
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
