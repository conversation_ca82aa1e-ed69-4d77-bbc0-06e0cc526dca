import { NextRequest, NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';
import { TrackClickRequest, TrackClickResponse, ClickEvent } from '@/lib/types';
import { createHash } from 'crypto';

// MongoDB连接
async function getMongoClient() {
  const uri = process.env.MONGODB_URI!;
  const username = process.env.MONGODB_USERNAME;
  const password = process.env.MONGODB_PASSWORD;
  const authSource = process.env.MONGODB_AUTH_SOURCE || 'admin';
  
  // 构建完整的MongoDB连接字符串
  const fullUri = username && password 
    ? `mongodb://${username}:${password}@${uri.replace('mongodb://', '')}?authSource=${authSource}`
    : uri;
  
  const client = new MongoClient(fullUri, {
    maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '5'),
    minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE || '1'),
    connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT || '10000'),
    serverSelectionTimeoutMS: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT || '5000'),
  });
  
  await client.connect();
  return client;
}

// 生成IP哈希
function hashIP(ip: string): string {
  return createHash('sha256').update(ip + process.env.IP_SALT || 'default-salt').digest('hex');
}

// 生成会话ID
function generateSessionId(): string {
  return createHash('md5').update(Date.now() + Math.random().toString()).digest('hex');
}

export async function POST(request: NextRequest) {
  try {
    const body: TrackClickRequest = await request.json();
    
    // 验证必需字段
    if (!body.productId || !body.sourcePage || !body.sourceUrl) {
      return NextResponse.json(
        { error: '缺少必需的字段' },
        { status: 400 }
      );
    }

    // 获取用户信息
    const userAgent = request.headers.get('user-agent') || '';
    const forwardedFor = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const ip = forwardedFor?.split(',')[0] || realIP || request.ip || '127.0.0.1';
    const ipHash = hashIP(ip);
    
    // 从cookie获取或生成会话ID
    const existingSessionId = request.cookies.get('affiliate_session')?.value;
    const sessionId = existingSessionId || generateSessionId();

    // 创建点击事件记录
    const clickEvent: Omit<ClickEvent, 'id'> = {
      productId: body.productId,
      sessionId,
      sourcePage: body.sourcePage as any,
      sourceUrl: body.sourceUrl,
      referrer: body.referrer,
      userAgent,
      ipHash,
      locale: request.headers.get('accept-language')?.split(',')[0] || 'en',
      timestamp: new Date(),
      pageContext: body.pageContext
    };

    // 连接数据库并保存点击事件
    const client = await getMongoClient();
    const db = client.db('tractor_data');
    
    try {
      // 插入点击事件
      const result = await db.collection('click_events').insertOne(clickEvent);
      const trackingId = result.insertedId.toString();

      // 从数据库中查找产品
      const productsCollection = db.collection('affiliateProducts');
      const product = await productsCollection.findOne({ 
        id: body.productId, 
        enabled: true 
      });

      if (!product) {
        return NextResponse.json(
          { error: '产品未找到或已禁用' },
          { status: 404 }
        );
      }

      // 更新产品点击统计
      await productsCollection.updateOne(
        { id: body.productId },
        { 
          $inc: { clickCount: 1 },
          $set: { updatedAt: new Date() }
        }
      );

      const response: TrackClickResponse = {
        success: true,
        trackingId,
        redirectUrl: product.affiliateUrl
      };

      // 设置会话cookie（如果是新会话）
      const nextResponse = NextResponse.json(response);
      if (!existingSessionId) {
        nextResponse.cookies.set('affiliate_session', sessionId, {
          maxAge: 30 * 24 * 60 * 60, // 30天
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax'
        });
      }

      return nextResponse;

    } finally {
      await client.close();
    }

  } catch (error) {
    console.error('点击跟踪错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}