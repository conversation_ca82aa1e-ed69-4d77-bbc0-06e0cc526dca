import { NextRequest, NextResponse } from 'next/server';
import { ProductMatcher } from '@/lib/productMatcher';
import { PageContext } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const pageType = searchParams.get('pageType') as PageContext['pageType'];
    const brandSlug = searchParams.get('brandSlug') || undefined;
    const tractorModel = searchParams.get('tractorModel') || undefined;
    const newsSlug = searchParams.get('newsSlug') || undefined;
    const maxProducts = parseInt(searchParams.get('maxProducts') || '4');
    const locale = searchParams.get('locale') || 'en';

    if (!pageType) {
      return NextResponse.json(
        { error: 'pageType is required' },
        { status: 400 }
      );
    }

    const pageContext: PageContext = {
      pageType,
      brandSlug,
      tractorModel,
      newsSlug,
      locale
    };

    const products = await ProductMatcher.getProductsForPage(pageContext, maxProducts);

    return NextResponse.json({ products });
  } catch (error) {
    console.error('Error fetching affiliate products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}