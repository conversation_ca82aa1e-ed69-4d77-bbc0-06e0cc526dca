import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

interface ImportProduct {
  title: string;
  description: string;
  imageUrl: string;
  affiliateUrl: string;
  category: string;
  enabled?: boolean;
  rating?: number;
  reviewCount?: number;
  tags?: string;
  priority?: number;
  isHotPick?: boolean;
  isOnSale?: boolean;
  targetPages?: string;
  brandMatch?: string;
  modelMatch?: string;
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const mode = formData.get('mode') as string || 'create'; // 'create', 'update', 'upsert'

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    const fileContent = await file.text();
    let products: ImportProduct[] = [];

    // 解析文件内容
    if (file.name.endsWith('.json')) {
      try {
        const jsonData = JSON.parse(fileContent);
        products = Array.isArray(jsonData) ? jsonData : jsonData.data || [];
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid JSON format' },
          { status: 400 }
        );
      }
    } else if (file.name.endsWith('.csv')) {
      // 简单的 CSV 解析
      const lines = fileContent.split('\n');
      const headers = lines[0].split(',').map(h => h.trim());
      
      products = lines.slice(1)
        .filter(line => line.trim())
        .map(line => {
          const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
          const product: any = {};
          headers.forEach((header, index) => {
            product[header] = values[index] || '';
          });
          return product;
        });
    } else {
      return NextResponse.json(
        { error: 'Unsupported file format. Please use JSON or CSV.' },
        { status: 400 }
      );
    }

    if (!products.length) {
      return NextResponse.json(
        { error: 'No valid products found in file' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliateProducts');

    const results = {
      created: 0,
      updated: 0,
      errors: [] as string[]
    };

    // 处理每个产品
    for (let i = 0; i < products.length; i++) {
      const productData = products[i];
      
      try {
        // 验证必需字段
        if (!productData.title || !productData.description || !productData.imageUrl || !productData.affiliateUrl) {
          results.errors.push(`Row ${i + 1}: Missing required fields (title, description, imageUrl, affiliateUrl)`);
          continue;
        }

        // 转换数据格式
        const processedProduct = {
          title: productData.title,
          description: productData.description,
          imageUrl: productData.imageUrl,
          affiliateUrl: productData.affiliateUrl,
          category: productData.category || 'tractor',
          enabled: productData.enabled !== undefined ? Boolean(productData.enabled) : true,
          rating: productData.rating ? Number(productData.rating) : 0,
          reviewCount: productData.reviewCount ? Number(productData.reviewCount) : 0,
          tags: productData.tags ? productData.tags.split(';').filter(Boolean) : [],
          priority: productData.priority ? Number(productData.priority) : 0,
          isHotPick: Boolean(productData.isHotPick),
          isOnSale: Boolean(productData.isOnSale),
          targetPages: productData.targetPages ? productData.targetPages.split(';').filter(Boolean) : ['home'],
          brandMatch: productData.brandMatch ? productData.brandMatch.split(';').filter(Boolean) : [],
          modelMatch: productData.modelMatch ? productData.modelMatch.split(';').filter(Boolean) : [],
          clickCount: 0,
          clickRate: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        if (mode === 'create') {
          // 仅创建新产品
          await collection.insertOne(processedProduct);
          results.created++;
        } else if (mode === 'update') {
          // 仅更新现有产品（基于 title 匹配）
          const updateResult = await collection.updateOne(
            { title: processedProduct.title },
            { 
              $set: {
                ...processedProduct,
                updatedAt: new Date()
              }
            }
          );
          if (updateResult.matchedCount > 0) {
            results.updated++;
          } else {
            results.errors.push(`Row ${i + 1}: Product "${productData.title}" not found for update`);
          }
        } else if (mode === 'upsert') {
          // 创建或更新
          const upsertResult = await collection.updateOne(
            { title: processedProduct.title },
            { 
              $set: {
                ...processedProduct,
                updatedAt: new Date()
              },
              $setOnInsert: {
                createdAt: new Date()
              }
            },
            { upsert: true }
          );
          if (upsertResult.upsertedCount > 0) {
            results.created++;
          } else {
            results.updated++;
          }
        }

      } catch (error) {
        results.errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Import completed. Created: ${results.created}, Updated: ${results.updated}, Errors: ${results.errors.length}`,
      results
    });

  } catch (error) {
    console.error('Import error:', error);
    return NextResponse.json(
      { error: 'Failed to import products' },
      { status: 500 }
    );
  }
}