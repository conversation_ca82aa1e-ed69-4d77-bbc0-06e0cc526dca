import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { AffiliateProduct } from '@/lib/types';
import { ObjectId } from 'mongodb';

// GET - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const status = searchParams.get('status') || '';
    const sortBy = searchParams.get('sortBy') || 'updatedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 构建查询条件
    const query: any = {};
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    if (category) {
      query.category = category;
    }
    
    if (status === 'active') {
      query.enabled = true;
    } else if (status === 'inactive') {
      query.enabled = false;
    }

    // 计算总数
    const total = await collection.countDocuments(query);
    
    // 获取分页数据
    const skip = (page - 1) * limit;
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
    
    const products = await collection
      .find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .toArray();

    // 转换数据格式
    const formattedProducts = products.map(product => ({
      ...product,
      id: product._id.toString(),
      _id: undefined
    }));

    return NextResponse.json({
      success: true,
      data: formattedProducts,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        itemsPerPage: limit,
        totalItems: total
      }
    });

  } catch (error) {
    console.error('获取产品列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取产品列表失败' },
      { status: 500 }
    );
  }
}

// POST - 创建新产品
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    const requiredFields = ['title', 'description', 'imageUrl', 'affiliateUrl', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `缺少必需字段: ${field}` },
          { status: 400 }
        );
      }
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 创建产品对象
    const newProduct: Omit<AffiliateProduct, 'id'> = {
      title: body.title,
      description: body.description,
      imageUrl: body.imageUrl,
      affiliateUrl: body.affiliateUrl,
      category: body.category,
      enabled: body.enabled ?? true,
      
      // 可选字段
      rating: body.rating || 0,
      reviewCount: body.reviewCount || 0,
      tags: body.tags || [],
      priority: body.priority || 0,
      isHotPick: body.isHotPick || false,
      isOnSale: body.isOnSale || false,
      targetPages: body.targetPages || ['home'],
      brandMatch: body.brandMatch || [],
      modelMatch: body.modelMatch || [],
      
      // 统计数据初始化
      clickCount: 0,
      impressionCount: 0,
      clickRate: 0,
      
      // 时间戳
      createdAt: new Date(),
      updatedAt: new Date(),
      
      // 多语言支持
      translations: body.translations || {}
    };

    const result = await collection.insertOne(newProduct);
    
    if (result.insertedId) {
      const createdProduct = {
        ...newProduct,
        id: result.insertedId.toString()
      };
      
      return NextResponse.json({
        success: true,
        data: createdProduct,
        message: '产品创建成功'
      }, { status: 201 });
    } else {
      throw new Error('插入产品失败');
    }

  } catch (error) {
    console.error('创建产品失败:', error);
    return NextResponse.json(
      { success: false, error: '创建产品失败' },
      { status: 500 }
    );
  }
}

// PUT - 批量更新产品状态
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { productIds, updates } = body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: '缺少产品ID列表' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 转换产品ID为ObjectId
    const objectIds = productIds.map((id: string) => new ObjectId(id));
    
    // 更新数据
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };

    const result = await collection.updateMany(
      { _id: { $in: objectIds } },
      { $set: updateData }
    );

    return NextResponse.json({
      success: true,
      message: `成功更新 ${result.modifiedCount} 个产品`,
      modifiedCount: result.modifiedCount
    });

  } catch (error) {
    console.error('批量更新产品失败:', error);
    return NextResponse.json(
      { success: false, error: '批量更新产品失败' },
      { status: 500 }
    );
  }
}

// DELETE - 批量删除产品
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { productIds } = body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: '缺少产品ID列表' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 转换产品ID为ObjectId
    const objectIds = productIds.map((id: string) => new ObjectId(id));
    
    const result = await collection.deleteMany({
      _id: { $in: objectIds }
    });

    return NextResponse.json({
      success: true,
      message: `成功删除 ${result.deletedCount} 个产品`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    console.error('批量删除产品失败:', error);
    return NextResponse.json(
      { success: false, error: '批量删除产品失败' },
      { status: 500 }
    );
  }
}