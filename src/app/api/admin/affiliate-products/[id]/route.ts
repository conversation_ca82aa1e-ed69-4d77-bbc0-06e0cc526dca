import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { AffiliateProduct } from '@/lib/types';
import { ObjectId } from 'mongodb';

// GET - 获取单个产品详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的产品ID' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    const product = await collection.findOne({ _id: new ObjectId(id) });

    if (!product) {
      return NextResponse.json(
        { success: false, error: '产品不存在' },
        { status: 404 }
      );
    }

    // 转换数据格式
    const formattedProduct = {
      ...product,
      id: product._id.toString(),
      _id: undefined
    };

    return NextResponse.json({
      success: true,
      data: formattedProduct
    });

  } catch (error) {
    console.error('获取产品详情失败:', error);
    return NextResponse.json(
      { success: false, error: '获取产品详情失败' },
      { status: 500 }
    );
  }
}

// PUT - 更新单个产品
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的产品ID' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 检查产品是否存在
    const existingProduct = await collection.findOne({ _id: new ObjectId(id) });
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: '产品不存在' },
        { status: 404 }
      );
    }

    // 准备更新数据
    const updateData: any = {
      updatedAt: new Date()
    };

    // 只更新提供的字段
    const allowedFields = [
      'title', 'description', 'imageUrl', 'affiliateUrl', 'category', 'enabled',
      'rating', 'reviewCount', 'tags', 'priority', 'isHotPick', 'isOnSale',
      'targetPages', 'brandMatch', 'modelMatch', 'translations'
    ];

    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.modifiedCount === 0) {
      return NextResponse.json(
        { success: false, error: '没有数据被更新' },
        { status: 400 }
      );
    }

    // 获取更新后的产品
    const updatedProduct = await collection.findOne({ _id: new ObjectId(id) });
    const formattedProduct = {
      ...updatedProduct,
      id: updatedProduct!._id.toString(),
      _id: undefined
    };

    return NextResponse.json({
      success: true,
      data: formattedProduct,
      message: '产品更新成功'
    });

  } catch (error) {
    console.error('更新产品失败:', error);
    return NextResponse.json(
      { success: false, error: '更新产品失败' },
      { status: 500 }
    );
  }
}

// DELETE - 删除单个产品
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的产品ID' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 检查产品是否存在
    const existingProduct = await collection.findOne({ _id: new ObjectId(id) });
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: '产品不存在' },
        { status: 404 }
      );
    }

    const result = await collection.deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: '删除产品失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '产品删除成功'
    });

  } catch (error) {
    console.error('删除产品失败:', error);
    return NextResponse.json(
      { success: false, error: '删除产品失败' },
      { status: 500 }
    );
  }
}

// PATCH - 部分更新产品（如切换状态）
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的产品ID' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliate_products');

    // 检查产品是否存在
    const existingProduct = await collection.findOne({ _id: new ObjectId(id) });
    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: '产品不存在' },
        { status: 404 }
      );
    }

    // 处理特殊操作
    const { action } = body;
    let updateData: any = { updatedAt: new Date() };

    switch (action) {
      case 'toggle_status':
        updateData.enabled = !existingProduct.enabled;
        break;
      case 'toggle_hot_pick':
        updateData.isHotPick = !existingProduct.isHotPick;
        break;
      case 'toggle_on_sale':
        updateData.isOnSale = !existingProduct.isOnSale;
        break;
      case 'update_priority':
        if (typeof body.priority === 'number') {
          updateData.priority = body.priority;
        } else {
          return NextResponse.json(
            { success: false, error: '优先级必须是数字' },
            { status: 400 }
          );
        }
        break;
      default:
        // 直接更新提供的字段
        Object.keys(body).forEach(key => {
          if (key !== 'action') {
            updateData[key] = body[key];
          }
        });
    }

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    if (result.modifiedCount === 0) {
      return NextResponse.json(
        { success: false, error: '没有数据被更新' },
        { status: 400 }
      );
    }

    // 获取更新后的产品
    const updatedProduct = await collection.findOne({ _id: new ObjectId(id) });
    const formattedProduct = {
      ...updatedProduct,
      id: updatedProduct!._id.toString(),
      _id: undefined
    };

    return NextResponse.json({
      success: true,
      data: formattedProduct,
      message: '产品更新成功'
    });

  } catch (error) {
    console.error('部分更新产品失败:', error);
    return NextResponse.json(
      { success: false, error: '部分更新产品失败' },
      { status: 500 }
    );
  }
}