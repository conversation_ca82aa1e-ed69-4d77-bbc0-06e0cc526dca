import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function POST(request: NextRequest) {
  try {
    const { action, productIds } = await request.json();

    if (!action || !productIds || !Array.isArray(productIds)) {
      return NextResponse.json(
        { error: 'Action and productIds are required' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliateProducts');

    // 转换为 ObjectId
    const objectIds = productIds.map(id => new ObjectId(id));

    let result;
    let message;

    switch (action) {
      case 'enable':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              enabled: true,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products enabled`;
        break;

      case 'disable':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              enabled: false,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products disabled`;
        break;

      case 'delete':
        result = await collection.deleteMany(
          { _id: { $in: objectIds } }
        );
        message = `${result.deletedCount} products deleted`;
        break;

      case 'set-hot-pick':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              isHotPick: true,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products marked as hot picks`;
        break;

      case 'unset-hot-pick':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              isHotPick: false,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products unmarked as hot picks`;
        break;

      case 'set-on-sale':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              isOnSale: true,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products marked as on sale`;
        break;

      case 'unset-on-sale':
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              isOnSale: false,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products unmarked as on sale`;
        break;

      case 'update-category':
        const { category } = await request.json();
        if (!category) {
          return NextResponse.json(
            { error: 'Category is required for update-category action' },
            { status: 400 }
          );
        }
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              category,
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products updated to category: ${category}`;
        break;

      case 'update-priority':
        const { priority } = await request.json();
        if (priority === undefined) {
          return NextResponse.json(
            { error: 'Priority is required for update-priority action' },
            { status: 400 }
          );
        }
        result = await collection.updateMany(
          { _id: { $in: objectIds } },
          { 
            $set: { 
              priority: parseInt(priority),
              updatedAt: new Date()
            } 
          }
        );
        message = `${result.modifiedCount} products updated to priority: ${priority}`;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message,
      modifiedCount: result.modifiedCount || result.deletedCount || 0
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}