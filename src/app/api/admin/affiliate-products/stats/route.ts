import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// GET - 获取产品统计信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // 天数
    const category = searchParams.get('category') || '';

    const { db } = await connectToDatabase();
    const productsCollection = db.collection('affiliate_products');
    const clicksCollection = db.collection('affiliate_clicks');

    // 计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(period));

    // 基础产品统计
    const totalProducts = await productsCollection.countDocuments();
    const activeProducts = await productsCollection.countDocuments({ enabled: true });
    const inactiveProducts = totalProducts - activeProducts;

    // 按类别统计
    const categoryStats = await productsCollection.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          activeCount: {
            $sum: { $cond: [{ $eq: ['$enabled', true] }, 1, 0] }
          }
        }
      },
      { $sort: { count: -1 } }
    ]).toArray();

    // 点击统计
    const clickStats = await clicksCollection.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: null,
          totalClicks: { $sum: 1 },
          uniqueProducts: { $addToSet: '$productId' }
        }
      }
    ]).toArray();

    const totalClicks = clickStats[0]?.totalClicks || 0;
    const uniqueClickedProducts = clickStats[0]?.uniqueProducts?.length || 0;

    // 热门产品统计
    const topProducts = await clicksCollection.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$productId',
          clickCount: { $sum: 1 }
        }
      },
      { $sort: { clickCount: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'affiliate_products',
          localField: '_id',
          foreignField: '_id',
          as: 'product'
        }
      },
      {
        $project: {
          productId: '$_id',
          clickCount: 1,
          productTitle: { $arrayElemAt: ['$product.title', 0] },
          productCategory: { $arrayElemAt: ['$product.category', 0] }
        }
      }
    ]).toArray();

    // 每日点击趋势
    const dailyClicks = await clicksCollection.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$timestamp'
            }
          },
          clicks: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]).toArray();

    // 按页面类型统计
    const pageTypeStats = await clicksCollection.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$sourcePage',
          clicks: { $sum: 1 }
        }
      },
      { $sort: { clicks: -1 } }
    ]).toArray();

    // 性能指标
    const performanceMetrics = await productsCollection.aggregate([
      {
        $group: {
          _id: null,
          avgClickRate: { $avg: '$clickRate' },
          totalImpressions: { $sum: '$impressionCount' },
          totalProductClicks: { $sum: '$clickCount' }
        }
      }
    ]).toArray();

    const avgClickRate = performanceMetrics[0]?.avgClickRate || 0;
    const totalImpressions = performanceMetrics[0]?.totalImpressions || 0;
    const totalProductClicks = performanceMetrics[0]?.totalProductClicks || 0;

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          totalProducts,
          activeProducts,
          inactiveProducts,
          totalClicks,
          uniqueClickedProducts,
          avgClickRate: Math.round(avgClickRate * 100) / 100,
          totalImpressions,
          totalProductClicks
        },
        categoryStats: categoryStats.map(stat => ({
          category: stat._id,
          total: stat.count,
          active: stat.activeCount,
          inactive: stat.count - stat.activeCount
        })),
        topProducts: topProducts.map(product => ({
          productId: product.productId,
          title: product.productTitle,
          category: product.productCategory,
          clicks: product.clickCount
        })),
        dailyTrend: dailyClicks.map(day => ({
          date: day._id,
          clicks: day.clicks
        })),
        pageTypeStats: pageTypeStats.map(stat => ({
          pageType: stat._id,
          clicks: stat.clicks
        })),
        period: {
          days: parseInt(period),
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    });

  } catch (error) {
    console.error('获取产品统计失败:', error);
    return NextResponse.json(
      { success: false, error: '获取产品统计失败' },
      { status: 500 }
    );
  }
}