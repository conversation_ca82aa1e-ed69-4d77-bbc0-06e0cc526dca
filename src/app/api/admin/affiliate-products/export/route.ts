import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json';
    const category = searchParams.get('category');
    const enabled = searchParams.get('enabled');

    const { db } = await connectToDatabase();
    const collection = db.collection('affiliateProducts');

    // 构建查询条件
    const query: any = {};
    if (category) {
      query.category = category;
    }
    if (enabled !== null) {
      query.enabled = enabled === 'true';
    }

    // 获取产品数据
    const products = await collection.find(query).toArray();

    // 转换数据格式
    const exportData = products.map(product => ({
      id: product._id.toString(),
      title: product.title,
      description: product.description,
      imageUrl: product.imageUrl,
      affiliateUrl: product.affiliateUrl,
      category: product.category,
      enabled: product.enabled,
      rating: product.rating || 0,
      reviewCount: product.reviewCount || 0,
      tags: product.tags?.join(';') || '',
      priority: product.priority || 0,
      isHotPick: product.isHotPick || false,
      isOnSale: product.isOnSale || false,
      targetPages: product.targetPages?.join(';') || '',
      brandMatch: product.brandMatch?.join(';') || '',
      modelMatch: product.modelMatch?.join(';') || '',
      clickCount: product.clickCount || 0,
      clickRate: product.clickRate || 0,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }));

    if (format === 'csv') {
      // 生成 CSV 格式
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header as keyof typeof row];
            // 处理包含逗号或引号的值
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="affiliate-products-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    // 默认返回 JSON 格式
    return NextResponse.json({
      success: true,
      data: exportData,
      count: exportData.length,
      exportedAt: new Date().toISOString()
    }, {
      headers: {
        'Content-Disposition': `attachment; filename="affiliate-products-${new Date().toISOString().split('T')[0]}.json"`
      }
    });

  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json(
      { error: 'Failed to export products' },
      { status: 500 }
    );
  }
}