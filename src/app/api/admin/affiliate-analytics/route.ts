import { NextRequest, NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';
import { ClickStats, ProductStats, DateRange } from '@/lib/types';
import { connectToDatabase } from '@/lib/mongodb';

// MongoDB连接
async function getMongoClient() {
  const uri = process.env.MONGODB_URI!;
  const username = process.env.MONGODB_USERNAME;
  const password = process.env.MONGODB_PASSWORD;
  const authSource = process.env.MONGODB_AUTH_SOURCE || 'admin';

  // 构建完整的MongoDB连接字符串
  const fullUri = username && password
    ? `mongodb://${username}:${password}@${uri.replace('mongodb://', '')}?authSource=${authSource}`
    : uri;

  const client = new MongoClient(fullUri, {
    maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '5'),
    minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE || '1'),
    connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT || '10000'),
    serverSelectionTimeoutMS: parseInt(process.env.MONGODB_SERVER_SELECTION_TIMEOUT || '5000'),
  });

  await client.connect();
  return client;
}

// 解析日期范围
function parseDateRange(searchParams: URLSearchParams): DateRange {
  const startDateStr = searchParams.get('startDate');
  const endDateStr = searchParams.get('endDate');

  const endDate = endDateStr ? new Date(endDateStr) : new Date();
  const startDate = startDateStr ? new Date(startDateStr) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 默认30天前

  return { startDate, endDate };
}

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const pageType = searchParams.get('pageType');
    const dateRange = parseDateRange(searchParams);
    const limit = parseInt(searchParams.get('limit') || '10');

    const client = await getMongoClient();
    const db = client.db('tractor_data');

    try {
      // 构建查询条件
      const matchConditions: any = {
        timestamp: {
          $gte: dateRange.startDate,
          $lte: dateRange.endDate
        }
      };

      if (productId) {
        matchConditions.productId = productId;
      }

      if (pageType) {
        matchConditions.sourcePage = pageType;
      }

      // 如果查询特定产品的统计
      if (productId) {
        const clickStats = await getProductClickStats(db, productId, dateRange);
        return NextResponse.json(clickStats);
      }

      // 获取产品排行榜
      const topProducts = await getTopProducts(db, dateRange, limit, pageType);

      // 获取总体统计
      const totalStats = await getTotalStats(db, dateRange, pageType);

      return NextResponse.json({
        totalStats,
        topProducts,
        dateRange
      });

    } finally {
      await client.close();
    }

  } catch (error) {
    console.error('获取分析数据错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// 获取产品点击统计
async function getProductClickStats(db: any, productId: string, dateRange: DateRange): Promise<ClickStats> {
  const pipeline = [
    {
      $match: {
        productId,
        timestamp: {
          $gte: dateRange.startDate,
          $lte: dateRange.endDate
        }
      }
    },
    {
      $group: {
        _id: null,
        totalClicks: { $sum: 1 },
        clicksByDate: {
          $push: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$timestamp" } }
          }
        },
        clicksByPage: {
          $push: "$sourcePage"
        },
        clicksByLocale: {
          $push: "$locale"
        }
      }
    }
  ];

  const result = await db.collection('click_events').aggregate(pipeline).toArray();

  if (result.length === 0) {
    return {
      productId,
      totalClicks: 0,
      totalImpressions: 0,
      clickRate: 0,
      clicksByDate: {},
      clicksByPage: {},
      clicksByLocale: {}
    };
  }

  const data = result[0];

  // 处理按日期分组的点击数
  const clicksByDate: Record<string, number> = {};
  data.clicksByDate.forEach((item: any) => {
    clicksByDate[item.date] = (clicksByDate[item.date] || 0) + 1;
  });

  // 处理按页面分组的点击数
  const clicksByPage: Record<string, number> = {};
  data.clicksByPage.forEach((page: string) => {
    clicksByPage[page] = (clicksByPage[page] || 0) + 1;
  });

  // 处理按语言分组的点击数
  const clicksByLocale: Record<string, number> = {};
  data.clicksByLocale.forEach((locale: string) => {
    clicksByLocale[locale] = (clicksByLocale[locale] || 0) + 1;
  });

  // 获取产品的展示次数（这里简化处理，实际应该有单独的展示跟踪）
  const productsCollection = db.collection('affiliateProducts');
  const product = await productsCollection.findOne({ id: productId });
  const totalImpressions = product?.impressionCount || data.totalClicks * 10; // 假设展示次数是点击次数的10倍

  return {
    productId,
    totalClicks: data.totalClicks,
    totalImpressions,
    clickRate: totalImpressions > 0 ? data.totalClicks / totalImpressions : 0,
    clicksByDate,
    clicksByPage,
    clicksByLocale
  };
}

// 获取热门产品排行榜
async function getTopProducts(db: any, dateRange: DateRange, limit: number, pageType?: string): Promise<ProductStats[]> {
  const matchConditions: any = {
    timestamp: {
      $gte: dateRange.startDate,
      $lte: dateRange.endDate
    }
  };

  if (pageType) {
    matchConditions.sourcePage = pageType;
  }

  const pipeline = [
    { $match: matchConditions },
    {
      $group: {
        _id: "$productId",
        totalClicks: { $sum: 1 }
      }
    },
    { $sort: { totalClicks: -1 } },
    { $limit: limit }
  ];

  const clickData = await db.collection('click_events').aggregate(pipeline).toArray();

  const productStats: ProductStats[] = [];

  // 获取产品数据
  const productsCollection = db.collection('affiliateProducts');
  const affiliateProductsData = await productsCollection.find({}).toArray();
  const affiliateProducts = affiliateProductsData.map(doc => ({
    id: doc.id || doc._id.toString(),
    title: doc.title,
    description: doc.description,
    imageUrl: doc.imageUrl,
    affiliateUrl: doc.affiliateUrl,
    category: doc.category,
    enabled: doc.enabled,
    rating: doc.rating || 0,
    reviewCount: doc.reviewCount || 0,
    tags: doc.tags || [],
    priority: doc.priority || 0,
    isHotPick: doc.isHotPick || false,
    isOnSale: doc.isOnSale || false,
    targetPages: doc.targetPages || ['home'],
    brandMatch: doc.brandMatch || [],
    modelMatch: doc.modelMatch || [],
    clickCount: doc.clickCount || 0,
    impressionCount: doc.impressionCount || 0,
    clickRate: doc.clickRate || 0,
    createdAt: doc.createdAt || new Date(),
    updatedAt: doc.updatedAt || new Date(),
    translations: doc.translations || {}
  }));

  for (let i = 0; i < clickData.length; i++) {
    const item = clickData[i];
    const product = affiliateProducts.find(p => p.id === item._id);

    if (product) {
      const stats = await getProductClickStats(db, item._id, dateRange);
      productStats.push({
        product,
        stats,
        rank: i + 1
      });
    }
  }

  return productStats;
}

// 获取总体统计
async function getTotalStats(db: any, dateRange: DateRange, pageType?: string) {
  const matchConditions: any = {
    timestamp: {
      $gte: dateRange.startDate,
      $lte: dateRange.endDate
    }
  };

  if (pageType) {
    matchConditions.sourcePage = pageType;
  }

  const pipeline = [
    { $match: matchConditions },
    {
      $group: {
        _id: null,
        totalClicks: { $sum: 1 },
        uniqueProducts: { $addToSet: "$productId" },
        uniqueSessions: { $addToSet: "$sessionId" }
      }
    }
  ];

  const result = await db.collection('click_events').aggregate(pipeline).toArray();

  if (result.length === 0) {
    return {
      totalClicks: 0,
      uniqueProducts: 0,
      uniqueSessions: 0,
      averageClicksPerSession: 0
    };
  }

  const data = result[0];

  return {
    totalClicks: data.totalClicks,
    uniqueProducts: data.uniqueProducts.length,
    uniqueSessions: data.uniqueSessions.length,
    averageClicksPerSession: data.uniqueSessions.length > 0 ? data.totalClicks / data.uniqueSessions.length : 0
  };
}