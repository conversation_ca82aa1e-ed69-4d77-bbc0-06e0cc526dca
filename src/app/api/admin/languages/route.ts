import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// 默认支持的语言配置
const defaultLanguages = [
  { 
    code: 'en', 
    name: 'English', 
    nativeName: 'English',
    flag: '🇺🇸', 
    enabled: true, 
    isDefault: true,
    amazonSite: 'amazon.com'
  },
  { 
    code: 'zh', 
    name: 'Chinese', 
    nativeName: '中文',
    flag: '🇨🇳', 
    enabled: true, 
    isDefault: false,
    amazonSite: 'amazon.com'
  },
  { 
    code: 'fr', 
    name: 'French', 
    nativeName: 'Français',
    flag: '🇫🇷', 
    enabled: true, 
    isDefault: false,
    amazonSite: 'amazon.fr'
  },
  { 
    code: 'de', 
    name: 'German', 
    nativeName: 'Deutsch',
    flag: '🇩🇪', 
    enabled: true, 
    isDefault: false,
    amazonSite: 'amazon.de'
  },
  { 
    code: 'es', 
    name: 'Spanish', 
    nativeName: 'Español',
    flag: '🇪🇸', 
    enabled: true, 
    isDefault: false,
    amazonSite: 'amazon.es'
  },
  { 
    code: 'pt', 
    name: 'Portuguese', 
    nativeName: 'Português',
    flag: '🇵🇹', 
    enabled: true, 
    isDefault: false,
    amazonSite: 'amazon.com'
  }
];

// GET - 获取语言配置
export async function GET(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();
    const collection = db.collection('language_config');
    
    // 尝试从数据库获取配置
    let languages = await collection.find({}).toArray();
    
    // 如果数据库中没有配置，使用默认配置并保存到数据库
    if (languages.length === 0) {
      await collection.insertMany(defaultLanguages);
      languages = defaultLanguages;
    }
    
    return NextResponse.json({
      success: true,
      data: languages
    });
    
  } catch (error) {
    console.error('Failed to get language config:', error);
    return NextResponse.json(
      { error: 'Failed to get language configuration' },
      { status: 500 }
    );
  }
}

// POST - 添加新语言
export async function POST(request: NextRequest) {
  try {
    const languageData = await request.json();
    
    // 验证必需字段
    const requiredFields = ['code', 'name', 'nativeName', 'flag'];
    for (const field of requiredFields) {
      if (!languageData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }
    
    const { db } = await connectToDatabase();
    const collection = db.collection('language_config');
    
    // 检查语言代码是否已存在
    const existing = await collection.findOne({ code: languageData.code });
    if (existing) {
      return NextResponse.json(
        { error: 'Language code already exists' },
        { status: 400 }
      );
    }
    
    // 添加默认值
    const newLanguage = {
      ...languageData,
      enabled: languageData.enabled ?? true,
      isDefault: false, // 新添加的语言不能是默认语言
      amazonSite: languageData.amazonSite || 'amazon.com',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await collection.insertOne(newLanguage);
    
    return NextResponse.json({
      success: true,
      data: { ...newLanguage, _id: result.insertedId }
    });
    
  } catch (error) {
    console.error('Failed to add language:', error);
    return NextResponse.json(
      { error: 'Failed to add language' },
      { status: 500 }
    );
  }
}

// PUT - 更新语言配置
export async function PUT(request: NextRequest) {
  try {
    const { languages } = await request.json();
    
    if (!Array.isArray(languages)) {
      return NextResponse.json(
        { error: 'Languages must be an array' },
        { status: 400 }
      );
    }
    
    const { db } = await connectToDatabase();
    const collection = db.collection('language_config');
    
    // 确保至少有一个默认语言
    const defaultLanguages = languages.filter(lang => lang.isDefault);
    if (defaultLanguages.length === 0) {
      return NextResponse.json(
        { error: 'At least one language must be set as default' },
        { status: 400 }
      );
    }
    
    // 确保只有一个默认语言
    if (defaultLanguages.length > 1) {
      return NextResponse.json(
        { error: 'Only one language can be set as default' },
        { status: 400 }
      );
    }
    
    // 批量更新
    const bulkOps = languages.map(lang => ({
      updateOne: {
        filter: { code: lang.code },
        update: { 
          $set: { 
            ...lang, 
            updatedAt: new Date() 
          } 
        },
        upsert: true
      }
    }));
    
    await collection.bulkWrite(bulkOps);
    
    return NextResponse.json({
      success: true,
      message: 'Language configuration updated successfully'
    });
    
  } catch (error) {
    console.error('Failed to update language config:', error);
    return NextResponse.json(
      { error: 'Failed to update language configuration' },
      { status: 500 }
    );
  }
}

// DELETE - 删除语言
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const languageCode = searchParams.get('code');
    
    if (!languageCode) {
      return NextResponse.json(
        { error: 'Language code is required' },
        { status: 400 }
      );
    }
    
    const { db } = await connectToDatabase();
    const collection = db.collection('language_config');
    
    // 检查是否是默认语言
    const language = await collection.findOne({ code: languageCode });
    if (language?.isDefault) {
      return NextResponse.json(
        { error: 'Cannot delete default language' },
        { status: 400 }
      );
    }
    
    // 删除语言配置
    const result = await collection.deleteOne({ code: languageCode });
    
    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Language not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Language deleted successfully'
    });
    
  } catch (error) {
    console.error('Failed to delete language:', error);
    return NextResponse.json(
      { error: 'Failed to delete language' },
      { status: 500 }
    );
  }
}
