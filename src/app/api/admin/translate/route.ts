import { NextRequest, NextResponse } from 'next/server';

// 简单的翻译映射（可以后续集成真实的翻译API）
const translationMappings: Record<string, Record<string, string>> = {
  // 常用农机词汇翻译
  'tractor': {
    'zh': '拖拉机',
    'fr': 'tracteur',
    'de': 'Traktor',
    'es': 'tractor',
    'pt': 'trator'
  },
  'farm': {
    'zh': '农场',
    'fr': 'ferme',
    'de': 'Bauernhof',
    'es': 'granja',
    'pt': 'fazenda'
  },
  'model': {
    'zh': '模型',
    'fr': 'modèle',
    'de': 'Modell',
    'es': 'modelo',
    'pt': 'modelo'
  },
  'john deere': {
    'zh': '约翰迪尔',
    'fr': '<PERSON>',
    'de': '<PERSON>',
    'es': '<PERSON>',
    'pt': '<PERSON>'
  },
  'equipment': {
    'zh': '设备',
    'fr': 'équipement',
    'de': 'Ausrüstung',
    'es': 'equipo',
    'pt': 'equipamento'
  },
  'parts': {
    'zh': '零件',
    'fr': 'pièces',
    'de': 'Teile',
    'es': 'partes',
    'pt': 'peças'
  },
  'accessories': {
    'zh': '配件',
    'fr': 'accessoires',
    'de': 'Zubehör',
    'es': 'accesorios',
    'pt': 'acessórios'
  }
};

// 简单的翻译函数
function simpleTranslate(text: string, targetLang: string): string {
  const lowerText = text.toLowerCase();
  
  // 查找完全匹配
  if (translationMappings[lowerText] && translationMappings[lowerText][targetLang]) {
    return translationMappings[lowerText][targetLang];
  }
  
  // 查找部分匹配并替换
  let translatedText = text;
  Object.keys(translationMappings).forEach(key => {
    if (translationMappings[key][targetLang]) {
      const regex = new RegExp(key, 'gi');
      translatedText = translatedText.replace(regex, translationMappings[key][targetLang]);
    }
  });
  
  return translatedText;
}

// 生成翻译建议
function generateTranslationSuggestion(text: string, targetLang: string): string {
  const suggestion = simpleTranslate(text, targetLang);
  
  const langNames: Record<string, string> = {
    'zh': '中文',
    'fr': '法文',
    'de': '德文',
    'es': '西班牙文',
    'pt': '葡萄牙文'
  };
  
  if (suggestion === text) {
    return `建议翻译为${langNames[targetLang]}：\n"${text}"\n\n请根据实际情况调整翻译内容。`;
  } else {
    return `建议翻译为${langNames[targetLang]}：\n"${suggestion}"\n\n这是基于常用词汇的自动翻译，请根据实际情况调整。`;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { text, targetLanguage, type } = await request.json();
    
    if (!text || !targetLanguage) {
      return NextResponse.json(
        { error: 'Text and target language are required' },
        { status: 400 }
      );
    }

    // 支持的语言列表
    const supportedLanguages = ['zh', 'fr', 'de', 'es', 'pt'];
    
    if (!supportedLanguages.includes(targetLanguage)) {
      return NextResponse.json(
        { error: 'Unsupported target language' },
        { status: 400 }
      );
    }

    let result;
    
    if (type === 'suggestion') {
      // 返回翻译建议
      result = {
        suggestion: generateTranslationSuggestion(text, targetLanguage),
        autoTranslation: simpleTranslate(text, targetLanguage)
      };
    } else {
      // 返回简单翻译
      result = {
        translatedText: simpleTranslate(text, targetLanguage),
        originalText: text,
        targetLanguage: targetLanguage
      };
    }

    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Translation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 批量翻译
export async function PUT(request: NextRequest) {
  try {
    const { texts, targetLanguage } = await request.json();
    
    if (!texts || !Array.isArray(texts) || !targetLanguage) {
      return NextResponse.json(
        { error: 'Texts array and target language are required' },
        { status: 400 }
      );
    }

    const translations = texts.map(text => ({
      original: text,
      translated: simpleTranslate(text, targetLanguage)
    }));

    return NextResponse.json({
      translations,
      targetLanguage
    });
    
  } catch (error) {
    console.error('Batch translation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
