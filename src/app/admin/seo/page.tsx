import { Metadata } from 'next';
import AdminLayout from '../components/AdminLayout';
import { Search, TrendingUp, Target, FileText } from 'lucide-react';

export const metadata: Metadata = {
  title: 'SEO Tools | Admin Dashboard',
  description: 'SEO optimization and monitoring tools',
  robots: 'noindex, nofollow'
};

export default function SEOPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">SEO Tools</h1>
          <p className="mt-1 text-gray-500">
            Optimize your website&apos;s search engine visibility and track keyword performance.
          </p>
        </div>

        {/* Coming Soon Card */}
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-tractor/10 mb-6">
            <Search className="h-8 w-8 text-tractor" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">SEO Dashboard Coming Soon</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            We&apos;re developing powerful SEO tools to help you improve your search rankings and monitor keyword performance.
          </p>
          
          {/* Feature Preview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
            <div className="p-4 border border-gray-200 rounded-lg">
              <Search className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Keyword Research</h3>
              <p className="text-sm text-gray-500 mt-1">Discover high-value keywords for your content</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <TrendingUp className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Rank Tracking</h3>
              <p className="text-sm text-gray-500 mt-1">Monitor your search engine rankings</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Target className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Content Optimization</h3>
              <p className="text-sm text-gray-500 mt-1">Optimize your content for better rankings</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <FileText className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">SEO Audits</h3>
              <p className="text-sm text-gray-500 mt-1">Comprehensive SEO health checks</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}