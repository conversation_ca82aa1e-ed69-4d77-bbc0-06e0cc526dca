'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '../components/AdminLayout';
import ProductForm from './components/ProductForm';
import ProductTable from './components/ProductTable';
import BulkActionsPanel from './components/BulkActionsPanel';
import MultilingualGuide from './components/MultilingualGuide';
import DataStatusChecker from './components/DataStatusChecker';
import { AffiliateProduct } from '@/lib/types';
import {
  Plus,
  Search,
  Download,
  RefreshCw,
  Settings
} from 'lucide-react';
import Link from 'next/link';

export default function AffiliateManagement() {
  const [products, setProducts] = useState<AffiliateProduct[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<AffiliateProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<AffiliateProduct | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取产品列表
  const fetchProducts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/affiliate-products/');
      if (response.ok) {
        const data = await response.json();
        setProducts(data.data || []);
      } else {
        const errorData = await response.json().catch(() => ({}));
        setError(errorData.error || 'Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setError('Network error: Unable to fetch products');
    } finally {
      setIsLoading(false);
    }
  };

  // 筛选产品
  useEffect(() => {
    let filtered = products;

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    if (categoryFilter) {
      filtered = filtered.filter(product => product.category === categoryFilter);
    }

    if (statusFilter === 'active') {
      filtered = filtered.filter(product => product.enabled);
    } else if (statusFilter === 'inactive') {
      filtered = filtered.filter(product => !product.enabled);
    }

    setFilteredProducts(filtered);
    setCurrentPage(1);
  }, [products, searchTerm, categoryFilter, statusFilter]);

  // 初始化加载
  useEffect(() => {
    fetchProducts();
  }, []);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + N: 新建产品
      if ((event.ctrlKey || event.metaKey) && event.key === 'n' && !showForm) {
        event.preventDefault();
        setShowForm(true);
      }
      // Escape: 关闭表单
      if (event.key === 'Escape' && showForm) {
        setShowForm(false);
        setEditingProduct(null);
        setError(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showForm]);

  // 保存产品
  const handleSaveProduct = async (productData: Partial<AffiliateProduct>) => {
    setIsSubmitting(true);
    try {
      const url = editingProduct 
        ? `/api/admin/affiliate-products/${editingProduct.id}`
        : '/api/admin/affiliate-products';
      
      const method = editingProduct ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        await fetchProducts();
        setShowForm(false);
        setEditingProduct(null);
        setError(null);
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error || 'Failed to save product';
        setError(errorMessage);
        alert('Failed to save product: ' + errorMessage);
      }
    } catch (error) {
      console.error('Error saving product:', error);
      const errorMessage = 'Network error: Unable to save product';
      setError(errorMessage);
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除产品
  const handleDeleteProducts = async (productIds: string[]) => {
    const productText = productIds.length === 1 ? 'product' : 'products';
    if (!confirm(`Are you sure you want to delete ${productIds.length} ${productText}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch('/api/admin/affiliate-products/', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productIds }),
      });

      if (response.ok) {
        await fetchProducts();
      } else {
        const error = await response.json();
        console.error('Failed to delete products:', error);
        alert('Failed to delete products: ' + (error.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error deleting products:', error);
      alert('Error deleting products');
    }
  };

  // 切换产品状态
  const handleToggleStatus = async (productId: string) => {
    try {
      const response = await fetch(`/api/admin/affiliate-products/${productId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'toggle_status' }),
      });

      if (response.ok) {
        await fetchProducts();
      } else {
        const error = await response.json();
        console.error('Failed to toggle status:', error);
      }
    } catch (error) {
      console.error('Error toggling status:', error);
    }
  };

  // 批量操作
  const handleBulkAction = async (action: string, productIds: string[], data?: any) => {
    try {
      setIsLoading(true);
      
      if (action === 'delete') {
        const confirmed = confirm(`Are you sure you want to delete ${productIds.length} product(s)?`);
        if (!confirmed) return;
      }
      
      const response = await fetch('/api/admin/affiliate-products/bulk/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action, 
          productIds,
          ...data
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to perform bulk action');
      }

      const result = await response.json();
      
      // 刷新产品列表
      await fetchProducts();
      
      // 显示成功消息
      alert(result.message || `Successfully performed ${action} on ${productIds.length} product(s)`);
      
    } catch (error) {
      console.error('Bulk action error:', error);
      alert('Failed to perform bulk action');
    } finally {
      setIsLoading(false);
    }
  };

  // 导出产品
  const handleExport = async (format: 'json' | 'csv', filters?: any) => {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      if (filters?.category) params.append('category', filters.category);
      if (filters?.enabled !== undefined) params.append('enabled', filters.enabled.toString());

      const response = await fetch(`/api/admin/affiliate-products/export/?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to export products');
      }

      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `affiliate-products-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
    } catch (error) {
      console.error('Export error:', error);
      alert('Failed to export products');
    }
  };

  // 导入产品
  const handleImport = async (file: File, mode: 'create' | 'update' | 'upsert') => {
    try {
      setIsLoading(true);
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('mode', mode);

      const response = await fetch('/api/admin/affiliate-products/import/', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to import products');
      }

      const result = await response.json();
      
      // 刷新产品列表
      await fetchProducts();
      
      // 显示结果
      alert(result.message);
      
      if (result.results.errors.length > 0) {
        console.warn('Import errors:', result.results.errors);
      }
      
    } catch (error) {
      console.error('Import error:', error);
      alert('Failed to import products');
    } finally {
      setIsLoading(false);
    }
  };

  // 分页逻辑
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage);

  if (showForm) {
    return (
      <AdminLayout>
        <ProductForm
          product={editingProduct}
          onSave={handleSaveProduct}
          onCancel={() => {
            setShowForm(false);
            setEditingProduct(null);
            setError(null);
          }}
          isLoading={isSubmitting}
        />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header with Tabs */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Affiliate Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage your Amazon affiliate products and display settings.
              </p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <Link
                href="/admin/affiliate"
                className="border-tractor text-tractor whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
              >
                Product Management
              </Link>
              <Link
                href="/admin/affiliate/settings"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
              >
                Display Settings
              </Link>
            </nav>
          </div>
        </div>

        {/* Action Bar */}
        <div className="flex items-center justify-between">
          <div></div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handleExport('csv')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button
              onClick={() => fetchProducts()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
            <Link
              href="/admin/affiliate/settings"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
            >
              <Settings className="h-4 w-4 mr-2" />
              Display Settings
            </Link>
            <MultilingualGuide />
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-tractor hover:bg-tractor/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => setError(null)}
                    className="text-sm bg-red-100 text-red-800 rounded-md px-2 py-1 hover:bg-red-200"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-tractor focus:border-tractor"
                    placeholder="Search products by title, description, or tags..."
                  />
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-tractor focus:border-tractor sm:text-sm rounded-md"
                >
                  <option value="">All Categories</option>
                  <option value="tractor">Tractors</option>
                  <option value="equipment">Equipment</option>
                  <option value="parts">Parts</option>
                  <option value="accessories">Accessories</option>
                </select>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-tractor focus:border-tractor sm:text-sm rounded-md"
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* 批量操作面板 */}
        <BulkActionsPanel
          selectedCount={0} // 这将由 ProductTable 内部管理
          onBulkAction={handleBulkAction}
          onClearSelection={() => {}} // 这将由 ProductTable 内部管理
          onExport={handleExport}
          onImport={handleImport}
          isLoading={isLoading}
        />

        {/* Products Table */}
        <ProductTable
          products={paginatedProducts}
          onEdit={(product) => {
            setEditingProduct(product);
            setShowForm(true);
          }}
          onDelete={handleDeleteProducts}
          onToggleStatus={handleToggleStatus}
          onBulkAction={handleBulkAction}
          isLoading={isLoading}
        />

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">{Math.min(startIndex + itemsPerPage, filteredProducts.length)}</span> of{' '}
                  <span className="font-medium">{filteredProducts.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {[...Array(Math.min(5, totalPages))].map((_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === page
                            ? 'border-tractor bg-tractor text-white'
                            : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* 数据状态检查工具 */}
        <DataStatusChecker
          products={products}
          onRefresh={fetchProducts}
        />
      </div>
    </AdminLayout>
  );
}