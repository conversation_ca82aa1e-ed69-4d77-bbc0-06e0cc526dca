import { Metadata } from 'next';
import AdminLayout from '../../components/AdminLayout';
import { 
  TrendingUp, 
  TrendingDown,
  MousePointer,
  DollarSign,
  ShoppingCart,
  Calendar,
  Download
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Affiliate Analytics | Admin Dashboard',
  description: 'View affiliate performance analytics and reports',
  robots: 'noindex, nofollow'
};

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
}

function MetricCard({ title, value, change, changeType, icon: Icon }: MetricCardProps) {
  const changeColor = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600'
  }[changeType];

  const TrendIcon = changeType === 'positive' ? TrendingUp : TrendingDown;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
        <div className="flex-shrink-0">
          <Icon className="h-8 w-8 text-tractor" />
        </div>
      </div>
      <div className="mt-4 flex items-center">
        <TrendIcon className={`h-4 w-4 ${changeColor} mr-1`} />
        <span className={`text-sm font-medium ${changeColor}`}>
          {change}
        </span>
        <span className="text-sm text-gray-500 ml-1">vs last period</span>
      </div>
    </div>
  );
}

// Mock data for top performing products
const topProducts = [
  {
    name: 'John Deere Oil Filter',
    clicks: 156,
    conversions: 8,
    revenue: '$199.92',
    conversionRate: '5.1%'
  },
  {
    name: 'Kubota Hydraulic Fluid',
    clicks: 89,
    conversions: 12,
    revenue: '$1079.88',
    conversionRate: '13.5%'
  },
  {
    name: 'Case IH Seat Cover',
    clicks: 234,
    conversions: 15,
    revenue: '$689.85',
    conversionRate: '6.4%'
  },
  {
    name: 'New Holland Parts Kit',
    clicks: 67,
    conversions: 4,
    revenue: '$156.00',
    conversionRate: '6.0%'
  }
];

export default function AffiliateAnalytics() {
  const metrics = [
    {
      title: 'Total Clicks',
      value: '2,456',
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: MousePointer
    },
    {
      title: 'Total Revenue',
      value: '$3,245.67',
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: DollarSign
    },
    {
      title: 'Conversions',
      value: '89',
      change: '+15.3%',
      changeType: 'positive' as const,
      icon: ShoppingCart
    },
    {
      title: 'Conversion Rate',
      value: '3.6%',
      change: '-0.2%',
      changeType: 'negative' as const,
      icon: TrendingUp
    }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Affiliate Analytics</h1>
            <p className="mt-1 text-sm text-gray-500">
              Track your affiliate performance and revenue metrics.
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <select className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-tractor focus:border-tractor sm:text-sm rounded-md">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 90 days</option>
                <option>Last year</option>
              </select>
            </div>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {metrics.map((metric, index) => (
            <MetricCard key={index} {...metric} />
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Revenue Trend</h2>
            </div>
            <div className="p-6">
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Chart visualization would go here</p>
                  <p className="text-sm text-gray-400">Integration with Chart.js or similar library</p>
                </div>
              </div>
            </div>
          </div>

          {/* Click Distribution */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Click Distribution</h2>
            </div>
            <div className="p-6">
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <MousePointer className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Pie chart would go here</p>
                  <p className="text-sm text-gray-400">Showing clicks by product category</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top Performing Products */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Top Performing Products</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Clicks
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conversions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Revenue
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Conversion Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {topProducts.map((product, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-tractor/10 flex items-center justify-center">
                            <span className="text-sm font-medium text-tractor">
                              {index + 1}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {product.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.clicks}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.conversions}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.revenue}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {product.conversionRate}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Performance Summary</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-tractor">156</div>
                <div className="text-sm text-gray-500">Active Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-tractor">$2,456</div>
                <div className="text-sm text-gray-500">Average Monthly Revenue</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-tractor">4.2%</div>
                <div className="text-sm text-gray-500">Average Conversion Rate</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}