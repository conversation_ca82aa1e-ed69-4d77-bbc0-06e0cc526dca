'use client';

import { useState, useEffect } from 'react';
import { Globe, Settings, Save, RotateCcw } from 'lucide-react';
import { AffiliateDisplayConfig, DEFAULT_AFFILIATE_CONFIG } from '@/lib/amazonConfig';
import AdminLayout from '../../components/AdminLayout';
import Link from 'next/link';

export default function AffiliateSettingsPage() {
  const [config, setConfig] = useState<AffiliateDisplayConfig>(DEFAULT_AFFILIATE_CONFIG);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string>('');

  // 加载配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('affiliateDisplayConfig');
    if (savedConfig) {
      try {
        setConfig(JSON.parse(savedConfig));
      } catch (error) {
        console.error('Failed to load affiliate config:', error);
      }
    }
  }, []);

  // 保存配置
  const handleSave = async () => {
    setIsSaving(true);
    try {
      localStorage.setItem('affiliateDisplayConfig', JSON.stringify(config));
      setSaveMessage('配置已保存！');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      setSaveMessage('保存失败，请重试');
      setTimeout(() => setSaveMessage(''), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为默认配置
  const handleReset = () => {
    setConfig(DEFAULT_AFFILIATE_CONFIG);
    setSaveMessage('已重置为默认配置');
    setTimeout(() => setSaveMessage(''), 3000);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header with Tabs */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Affiliate Management</h1>
              <p className="mt-1 text-sm text-gray-500">
                Manage your Amazon affiliate products and display settings.
              </p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <Link
                href="/admin/affiliate"
                className="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
              >
                Product Management
              </Link>
              <Link
                href="/admin/affiliate/settings"
                className="border-tractor text-tractor whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
              >
                Display Settings
              </Link>
            </nav>
          </div>
        </div>

        {/* 配置表单 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="space-y-6">
            {/* 多语言文本显示 */}
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <input
                  type="checkbox"
                  id="showLocalizedText"
                  checked={config.showLocalizedText}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    showLocalizedText: e.target.checked
                  }))}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
              </div>
              <div className="flex-grow">
                <label htmlFor="showLocalizedText" className="text-sm font-medium text-gray-900">
                  显示本地化文本
                </label>
                <p className="text-sm text-gray-500 mt-1">
                  根据页面语言显示对应的产品标题和描述。关闭后将统一显示英文。
                </p>
              </div>
            </div>

            {/* 本地化链接 */}
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <input
                  type="checkbox"
                  id="useLocalizedLinks"
                  checked={config.useLocalizedLinks}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    useLocalizedLinks: e.target.checked
                  }))}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
              </div>
              <div className="flex-grow">
                <label htmlFor="useLocalizedLinks" className="text-sm font-medium text-gray-900">
                  使用本地化链接
                </label>
                <p className="text-sm text-gray-500 mt-1">
                  如果产品配置了对应语言的亚马逊链接，优先使用本地化链接。
                  <span className="text-amber-600 font-medium">（实验性功能）</span>
                </p>
              </div>
            </div>

            {/* 显示链接目标 */}
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <input
                  type="checkbox"
                  id="showLinkDestination"
                  checked={config.showLinkDestination}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    showLinkDestination: e.target.checked
                  }))}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
              </div>
              <div className="flex-grow">
                <label htmlFor="showLinkDestination" className="text-sm font-medium text-gray-900">
                  显示链接目标说明
                </label>
                <p className="text-sm text-gray-500 mt-1">
                  在按钮下方显示"链接到 Amazon.com"等说明文字，让用户知道会跳转到哪个站点。
                </p>
              </div>
            </div>

            {/* 英文回退 */}
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-1">
                <input
                  type="checkbox"
                  id="fallbackToEnglish"
                  checked={config.fallbackToEnglish}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    fallbackToEnglish: e.target.checked
                  }))}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
              </div>
              <div className="flex-grow">
                <label htmlFor="fallbackToEnglish" className="text-sm font-medium text-gray-900">
                  回退到英文站点
                </label>
                <p className="text-sm text-gray-500 mt-1">
                  当本地化链接不可用时，自动回退到英文亚马逊站点。
                </p>
              </div>
            </div>
          </div>

          {/* 预览区域 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
              <Globe className="h-4 w-4 mr-2" />
              配置预览
            </h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>• 多语言文本：{config.showLocalizedText ? '启用' : '禁用'}</div>
              <div>• 本地化链接：{config.useLocalizedLinks ? '启用' : '禁用'}</div>
              <div>• 链接目标说明：{config.showLinkDestination ? '显示' : '隐藏'}</div>
              <div>• 英文回退：{config.fallbackToEnglish ? '启用' : '禁用'}</div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mt-6 flex items-center justify-between">
            <button
              onClick={handleReset}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              重置为默认
            </button>

            <div className="flex items-center space-x-3">
              {saveMessage && (
                <span className={`text-sm ${saveMessage.includes('失败') ? 'text-red-600' : 'text-green-600'}`}>
                  {saveMessage}
                </span>
              )}
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center px-4 py-2 text-sm font-medium text-white bg-tractor border border-transparent rounded-md hover:bg-tractor/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor disabled:opacity-50"
              >
                {isSaving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {isSaving ? '保存中...' : '保存配置'}
              </button>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">使用建议</h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p><strong>推荐配置（当前默认）：</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>启用多语言文本 - 提升用户体验</li>
              <li>禁用本地化链接 - 确保链接有效性</li>
              <li>显示链接目标说明 - 让用户知道跳转目标</li>
              <li>启用英文回退 - 保证链接可用性</li>
            </ul>
            <p className="mt-3">
              <strong>注意：</strong>配置更改后需要刷新页面才能生效。
            </p>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
