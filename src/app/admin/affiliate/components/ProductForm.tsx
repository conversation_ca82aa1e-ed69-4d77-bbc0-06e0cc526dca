'use client';

import { useState, useEffect } from 'react';
import { AffiliateProduct } from '@/lib/types';
import {
  Save,
  X,
  Upload,
  Eye,
  EyeOff,
  Plus,
  Minus,
  Star,
  Globe,
  Languages
} from 'lucide-react';
import ProductPreview from './ProductPreview';

interface ProductFormProps {
  product?: AffiliateProduct | null;
  onSave: (productData: Partial<AffiliateProduct>) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

const categories = [
  { value: 'tractor', label: 'Tractors' },
  { value: 'equipment', label: 'Equipment' },
  { value: 'parts', label: 'Parts' },
  { value: 'accessories', label: 'Accessories' }
];

const targetPageOptions = [
  { value: 'home', label: 'Home Page' },
  { value: 'brand', label: 'Brand Pages' },
  { value: 'tractor', label: 'Tractor Pages' },
  { value: 'news', label: 'News Pages' }
];

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  enabled: boolean;
  isDefault: boolean;
  amazonSite: string;
}

export default function ProductForm({ 
  product, 
  onSave, 
  onCancel, 
  isLoading = false 
}: ProductFormProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    affiliateUrl: '',
    category: 'tractor' as const,
    enabled: true,
    rating: 0,
    reviewCount: 0,
    tags: [] as string[],
    priority: 0,
    isHotPick: false,
    isOnSale: false,
    targetPages: ['home'] as string[],
    brandMatch: [] as string[],
    modelMatch: [] as string[],
    translations: {}
  });

  const [newTag, setNewTag] = useState('');
  const [newBrandMatch, setNewBrandMatch] = useState('');
  const [newModelMatch, setNewModelMatch] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [activeLanguageTab, setActiveLanguageTab] = useState('en');
  const [showTranslations, setShowTranslations] = useState(false);
  const [supportedLanguages, setSupportedLanguages] = useState<Language[]>([]);
  const [isLoadingLanguages, setIsLoadingLanguages] = useState(true);
  const [isTranslating, setIsTranslating] = useState(false);

  // 加载语言配置
  useEffect(() => {
    const loadLanguages = async () => {
      try {
        const response = await fetch('/api/admin/languages/');
        if (response.ok) {
          const data = await response.json();
          setSupportedLanguages(data.data.filter((lang: Language) => lang.enabled));
        }
      } catch (error) {
        console.error('Failed to load languages:', error);
        // 使用默认语言配置
        setSupportedLanguages([
          { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸', enabled: true, isDefault: true, amazonSite: 'amazon.com' },
          { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳', enabled: true, isDefault: false, amazonSite: 'amazon.com' },
          { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷', enabled: true, isDefault: false, amazonSite: 'amazon.fr' },
          { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', enabled: true, isDefault: false, amazonSite: 'amazon.de' },
          { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸', enabled: true, isDefault: false, amazonSite: 'amazon.es' },
          { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹', enabled: true, isDefault: false, amazonSite: 'amazon.com' }
        ]);
      } finally {
        setIsLoadingLanguages(false);
      }
    };

    loadLanguages();
  }, []);

  // 初始化表单数据
  useEffect(() => {
    if (product) {
      setFormData({
        title: product.title || '',
        description: product.description || '',
        imageUrl: product.imageUrl || '',
        affiliateUrl: product.affiliateUrl || '',
        category: product.category || 'tractor',
        enabled: product.enabled ?? true,
        rating: product.rating || 0,
        reviewCount: product.reviewCount || 0,
        tags: product.tags || [],
        priority: product.priority || 0,
        isHotPick: product.isHotPick || false,
        isOnSale: product.isOnSale || false,
        targetPages: product.targetPages || ['home'],
        brandMatch: product.brandMatch || [],
        modelMatch: product.modelMatch || [],
        translations: product.translations || {}
      });
    }
  }, [product]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayAdd = (field: 'tags' | 'brandMatch' | 'modelMatch', value: string) => {
    if (value.trim() && !formData[field].includes(value.trim())) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()]
      }));
    }
  };

  const handleArrayRemove = (field: 'tags' | 'brandMatch' | 'modelMatch', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleTargetPageToggle = (page: string) => {
    setFormData(prev => ({
      ...prev,
      targetPages: prev.targetPages.includes(page)
        ? prev.targetPages.filter(p => p !== page)
        : [...prev.targetPages, page]
    }));
  };

  // 处理翻译文本更新
  const handleTranslationChange = (langCode: string, field: 'title' | 'description', value: string) => {
    setFormData(prev => ({
      ...prev,
      translations: {
        ...prev.translations,
        [langCode]: {
          ...prev.translations[langCode],
          [field]: value
        }
      }
    }));
  };

  // 删除某个语言的翻译
  const handleRemoveTranslation = (langCode: string) => {
    setFormData(prev => {
      const newTranslations = { ...prev.translations };
      delete newTranslations[langCode];
      return {
        ...prev,
        translations: newTranslations
      };
    });
  };

  // 调用翻译API
  const handleAutoTranslate = async (langCode: string, field: 'title' | 'description') => {
    if (!formData[field]) {
      alert('请先填写英文内容');
      return;
    }

    setIsTranslating(true);
    try {
      const response = await fetch('/api/admin/translate/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: formData[field],
          targetLanguage: langCode,
          type: 'translate'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        handleTranslationChange(langCode, field, data.translatedText);
      } else {
        alert('翻译失败，请手动输入');
      }
    } catch (error) {
      console.error('Translation error:', error);
      alert('翻译服务暂时不可用，请手动输入');
    } finally {
      setIsTranslating(false);
    }
  };

  // 批量翻译
  const handleBatchTranslate = async (langCode: string) => {
    if (!formData.title || !formData.description) {
      alert('请先填写英文标题和描述');
      return;
    }

    setIsTranslating(true);
    try {
      const response = await fetch('/api/admin/translate/', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          texts: [formData.title, formData.description],
          targetLanguage: langCode
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const translations = data.translations;

        setFormData(prev => ({
          ...prev,
          translations: {
            ...prev.translations,
            [langCode]: {
              ...prev.translations[langCode],
              title: translations[0].translated,
              description: translations[1].translated
            }
          }
        }));
      } else {
        alert('批量翻译失败');
      }
    } catch (error) {
      console.error('Batch translation error:', error);
      alert('翻译服务暂时不可用');
    } finally {
      setIsTranslating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 基本验证
    if (!formData.title.trim()) {
      alert('Product title is required');
      return;
    }
    if (!formData.description.trim()) {
      alert('Product description is required');
      return;
    }
    if (!formData.imageUrl.trim()) {
      alert('Product image URL is required');
      return;
    }
    if (!formData.affiliateUrl.trim()) {
      alert('Affiliate URL is required');
      return;
    }
    
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving product:', error);
    }
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => handleInputChange('rating', star)}
            className={`p-1 ${
              star <= formData.rating ? 'text-yellow-400' : 'text-gray-300'
            } hover:text-yellow-400 transition-colors`}
          >
            <Star className="h-5 w-5 fill-current" />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">
          {formData.rating}/5
        </span>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          {product ? 'Edit Product' : 'Add New Product'}
        </h2>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            {showPreview ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Title *
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                placeholder="Enter product title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                required
                rows={4}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                placeholder="Enter product description"
              />
            </div>

            {/* 多语言翻译部分 */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Languages className="h-5 w-5 text-gray-400 mr-2" />
                  <h4 className="text-sm font-medium text-gray-900">多语言翻译</h4>
                  <span className="ml-2 text-xs text-gray-500">（可选）</span>
                </div>
                <button
                  type="button"
                  onClick={() => setShowTranslations(!showTranslations)}
                  className="text-sm text-tractor hover:text-tractor/80"
                >
                  {showTranslations ? '隐藏' : '显示'} 翻译编辑
                </button>
              </div>

              {showTranslations && (
                <div className="space-y-4">
                  {/* 语言标签页 */}
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                      {supportedLanguages.map((lang) => (
                        <button
                          key={lang.code}
                          type="button"
                          onClick={() => setActiveLanguageTab(lang.code)}
                          className={`py-2 px-1 border-b-2 font-medium text-sm ${
                            activeLanguageTab === lang.code
                              ? 'border-tractor text-tractor'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          <span className="mr-2">{lang.flag}</span>
                          {lang.name}
                          {formData.translations[lang.code] && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              已翻译
                            </span>
                          )}
                        </button>
                      ))}
                    </nav>
                  </div>

                  {/* 翻译内容编辑 */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    {activeLanguageTab === 'en' ? (
                      <div className="text-center py-8 text-gray-500">
                        <Globe className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p>英文是默认语言，请在上方的基础信息中编辑</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {/* 批量翻译按钮 */}
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                          <div className="text-sm text-blue-800">
                            为 <strong>{supportedLanguages.find(l => l.code === activeLanguageTab)?.nativeName}</strong> 自动翻译内容
                          </div>
                          <button
                            type="button"
                            onClick={() => handleBatchTranslate(activeLanguageTab)}
                            disabled={isTranslating || !formData.title || !formData.description}
                            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                          >
                            {isTranslating ? (
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                            ) : (
                              <Languages className="h-3 w-3 mr-1" />
                            )}
                            {isTranslating ? '翻译中...' : '批量翻译'}
                          </button>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="block text-sm font-medium text-gray-700">
                              {supportedLanguages.find(l => l.code === activeLanguageTab)?.nativeName} 标题
                            </label>
                            <div className="flex items-center space-x-2">
                              {formData.title && (
                                <button
                                  type="button"
                                  onClick={() => handleAutoTranslate(activeLanguageTab, 'title')}
                                  disabled={isTranslating}
                                  className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
                                >
                                  {isTranslating ? '翻译中...' : '自动翻译'}
                                </button>
                              )}
                            </div>
                          </div>
                          <input
                            type="text"
                            value={formData.translations[activeLanguageTab]?.title || ''}
                            onChange={(e) => handleTranslationChange(activeLanguageTab, 'title', e.target.value)}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                            placeholder={`输入${supportedLanguages.find(l => l.code === activeLanguageTab)?.name}标题`}
                          />
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <label className="block text-sm font-medium text-gray-700">
                              {supportedLanguages.find(l => l.code === activeLanguageTab)?.nativeName} 描述
                            </label>
                            <div className="flex items-center space-x-2">
                              {formData.description && (
                                <button
                                  type="button"
                                  onClick={() => handleAutoTranslate(activeLanguageTab, 'description')}
                                  disabled={isTranslating}
                                  className="text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
                                >
                                  {isTranslating ? '翻译中...' : '自动翻译'}
                                </button>
                              )}
                            </div>
                          </div>
                          <textarea
                            rows={4}
                            value={formData.translations[activeLanguageTab]?.description || ''}
                            onChange={(e) => handleTranslationChange(activeLanguageTab, 'description', e.target.value)}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                            placeholder={`输入${supportedLanguages.find(l => l.code === activeLanguageTab)?.name}描述`}
                          />
                        </div>
                        {formData.translations[activeLanguageTab] && (
                          <div className="flex justify-end">
                            <button
                              type="button"
                              onClick={() => handleRemoveTranslation(activeLanguageTab)}
                              className="text-sm text-red-600 hover:text-red-800"
                            >
                              删除此语言翻译
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                required
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
              >
                {categories.map(cat => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image URL *
              </label>
              <input
                type="url"
                required
                value={formData.imageUrl}
                onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                placeholder="https://example.com/image.jpg"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Affiliate URL *
              </label>
              <input
                type="url"
                required
                value={formData.affiliateUrl}
                onChange={(e) => handleInputChange('affiliateUrl', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                placeholder="https://amazon.com/..."
              />
            </div>
          </div>

          {/* 高级设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Advanced Settings</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rating
              </label>
              {renderStarRating()}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Review Count
              </label>
              <input
                type="number"
                min="0"
                value={formData.reviewCount}
                onChange={(e) => handleInputChange('reviewCount', parseInt(e.target.value) || 0)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', parseInt(e.target.value) || 0)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor"
                placeholder="0-100 (higher = more priority)"
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  checked={formData.enabled}
                  onChange={(e) => handleInputChange('enabled', e.target.checked)}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
                <label htmlFor="enabled" className="ml-2 block text-sm text-gray-900">
                  Product Enabled
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isHotPick"
                  checked={formData.isHotPick}
                  onChange={(e) => handleInputChange('isHotPick', e.target.checked)}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
                <label htmlFor="isHotPick" className="ml-2 block text-sm text-gray-900">
                  Hot Pick
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isOnSale"
                  checked={formData.isOnSale}
                  onChange={(e) => handleInputChange('isOnSale', e.target.checked)}
                  className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                />
                <label htmlFor="isOnSale" className="ml-2 block text-sm text-gray-900">
                  On Sale
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Pages
              </label>
              <div className="space-y-2">
                {targetPageOptions.map(option => (
                  <div key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`target-${option.value}`}
                      checked={formData.targetPages.includes(option.value)}
                      onChange={() => handleTargetPageToggle(option.value)}
                      className="h-4 w-4 text-tractor focus:ring-tractor border-gray-300 rounded"
                    />
                    <label htmlFor={`target-${option.value}`} className="ml-2 block text-sm text-gray-900">
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 标签和匹配 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayAdd('tags', newTag);
                    setNewTag('');
                  }
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor text-sm"
                placeholder="Add tag"
              />
              <button
                type="button"
                onClick={() => {
                  handleArrayAdd('tags', newTag);
                  setNewTag('');
                }}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-tractor/10 text-tractor"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleArrayRemove('tags', index)}
                    className="ml-1 text-tractor hover:text-tractor/80"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand Match
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newBrandMatch}
                onChange={(e) => setNewBrandMatch(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayAdd('brandMatch', newBrandMatch);
                    setNewBrandMatch('');
                  }
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor text-sm"
                placeholder="Brand slug"
              />
              <button
                type="button"
                onClick={() => {
                  handleArrayAdd('brandMatch', newBrandMatch);
                  setNewBrandMatch('');
                }}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.brandMatch.map((brand, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {brand}
                  <button
                    type="button"
                    onClick={() => handleArrayRemove('brandMatch', index)}
                    className="ml-1 text-blue-800 hover:text-blue-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model Match
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newModelMatch}
                onChange={(e) => setNewModelMatch(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleArrayAdd('modelMatch', newModelMatch);
                    setNewModelMatch('');
                  }
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor text-sm"
                placeholder="Model name"
              />
              <button
                type="button"
                onClick={() => {
                  handleArrayAdd('modelMatch', newModelMatch);
                  setNewModelMatch('');
                }}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.modelMatch.map((model, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  {model}
                  <button
                    type="button"
                    onClick={() => handleArrayRemove('modelMatch', index)}
                    className="ml-1 text-green-800 hover:text-green-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* 实时预览 */}
        {showPreview && (
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Live Preview</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Card View</h4>
                <ProductPreview product={formData} className="max-w-sm" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">List View</h4>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start space-x-4">
                    <img
                      src={formData.imageUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzMC4wNTg5IDYwIDIyIDUxLjk0MTEgMjIgNDJDMjIgMzIuMDU4OSAzMC4wNTg5IDI0IDQwIDI0QzQ5Ljk0MTEgMjQgNTggMzIuMDU4OSA1OCA0MkM1OCA1MS45NDExIDQ5Ljk0MTEgNjAgNDAgNjBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik00MCA0OEM0My4zMTM3IDQ4IDQ2IDQ1LjMxMzcgNDYgNDJDNDYgMzguNjg2MyA0My4zMTM3IDM2IDQwIDM2QzM2LjY4NjMgMzYgMzQgMzguNjg2MyAzNCA0MkMzNCA0NS4zMTM3IDM2LjY4NjMgNDggNDAgNDhaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo='}
                      alt={formData.title}
                      className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik00MCA2MEMzMC4wNTg5IDYwIDIyIDUxLjk0MTEgMjIgNDJDMjIgMzIuMDU4OSAzMC4wNTg5IDI0IDQwIDI0QzQ5Ljk0MTEgMjQgNTggMzIuMDU4OSA1OCA0MkM1OCA1MS45NDExIDQ5Ljk0MTEgNjAgNDAgNjBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik00MCA0OEM0My4zMTM3IDQ4IDQ2IDQ1LjMxMzcgNDYgNDJDNDYgMzguNjg2MyA0My4zMTM3IDM2IDQwIDM2QzM2LjY4NjMgMzYgMzQgMzguNjg2MyAzNCA0MkMzNCA0NS4zMTM3IDM2LjY4NjMgNDggNDAgNDhaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=';
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {formData.title || 'Product Title'}
                          </h4>
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {formData.description || 'Product description...'}
                          </p>
                          <div className="flex items-center mt-2 space-x-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3 w-3 ${
                                    i < Math.round(formData.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="text-xs text-gray-500">({formData.reviewCount || 0})</span>
                            {formData.isHotPick && (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                Hot
                              </span>
                            )}
                            {formData.isOnSale && (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                Sale
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tractor hover:bg-tractor/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {product ? 'Update Product' : 'Create Product'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}