'use client';

import { useState } from 'react';
import { 
  Download, 
  Upload, 
  Trash2, 
  ToggleLeft, 
  ToggleRight,
  Star,
  Tag,
  Folder,
  TrendingUp,
  X,
  FileText,
  AlertTriangle
} from 'lucide-react';

interface BulkActionsPanelProps {
  selectedCount: number;
  onBulkAction: (action: string, data?: any) => void;
  onClearSelection: () => void;
  onExport: (format: 'json' | 'csv', filters?: any) => void;
  onImport: (file: File, mode: 'create' | 'update' | 'upsert') => void;
  isLoading?: boolean;
}

export default function BulkActionsPanel({
  selectedCount,
  onBulkAction,
  onClearSelection,
  onExport,
  onImport,
  isLoading = false
}: BulkActionsPanelProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importMode, setImportMode] = useState<'create' | 'update' | 'upsert'>('create');
  const [bulkCategory, setBulkCategory] = useState('');
  const [bulkPriority, setBulkPriority] = useState('');

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImport(file, importMode);
      setShowImportModal(false);
      event.target.value = ''; // 重置文件输入
    }
  };

  const categories = [
    { value: 'tractor', label: 'Tractors' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'parts', label: 'Parts' },
    { value: 'accessories', label: 'Accessories' }
  ];

  return (
    <>
      {/* 批量操作栏 */}
      {selectedCount > 0 && (
        <div className="bg-tractor/10 border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-900">
                  {selectedCount} product{selectedCount > 1 ? 's' : ''} selected
                </span>
                
                {/* 基本操作 */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onBulkAction('enable')}
                    disabled={isLoading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50"
                  >
                    <ToggleRight className="h-3 w-3 mr-1" />
                    Enable
                  </button>
                  
                  <button
                    onClick={() => onBulkAction('disable')}
                    disabled={isLoading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
                  >
                    <ToggleLeft className="h-3 w-3 mr-1" />
                    Disable
                  </button>
                  
                  <button
                    onClick={() => onBulkAction('set-hot-pick')}
                    disabled={isLoading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
                  >
                    <Star className="h-3 w-3 mr-1" />
                    Hot Pick
                  </button>
                  
                  <button
                    onClick={() => onBulkAction('set-on-sale')}
                    disabled={isLoading}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 disabled:opacity-50"
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    On Sale
                  </button>
                  
                  <button
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                  >
                    More Actions
                  </button>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onBulkAction('delete')}
                  disabled={isLoading}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Delete
                </button>
                
                <button
                  onClick={onClearSelection}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Clear selection
                </button>
              </div>
            </div>
            
            {/* 高级操作 */}
            {showAdvanced && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Update Category
                    </label>
                    <div className="flex space-x-2">
                      <select
                        value={bulkCategory}
                        onChange={(e) => setBulkCategory(e.target.value)}
                        className="flex-1 text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-tractor"
                      >
                        <option value="">Select category</option>
                        {categories.map(cat => (
                          <option key={cat.value} value={cat.value}>
                            {cat.label}
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={() => {
                          if (bulkCategory) {
                            onBulkAction('update-category', { category: bulkCategory });
                            setBulkCategory('');
                          }
                        }}
                        disabled={!bulkCategory || isLoading}
                        className="px-2 py-1 text-xs bg-tractor text-white rounded hover:bg-tractor/90 disabled:opacity-50"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Update Priority
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={bulkPriority}
                        onChange={(e) => setBulkPriority(e.target.value)}
                        placeholder="0-100"
                        className="flex-1 text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-tractor"
                      />
                      <button
                        onClick={() => {
                          if (bulkPriority) {
                            onBulkAction('update-priority', { priority: bulkPriority });
                            setBulkPriority('');
                          }
                        }}
                        disabled={!bulkPriority || isLoading}
                        className="px-2 py-1 text-xs bg-tractor text-white rounded hover:bg-tractor/90 disabled:opacity-50"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Remove Flags
                    </label>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => onBulkAction('unset-hot-pick')}
                        disabled={isLoading}
                        className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Remove Hot Pick
                      </button>
                      <button
                        onClick={() => onBulkAction('unset-on-sale')}
                        disabled={isLoading}
                        className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                      >
                        Remove Sale
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* 导入/导出工具栏 */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h3 className="text-sm font-medium text-gray-900">Product Management</h3>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowImportModal(true)}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <Upload className="h-3 w-3 mr-1" />
                Import
              </button>
              
              <div className="relative">
                <button
                  onClick={() => onExport('json')}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export JSON
                </button>
              </div>
              
              <button
                onClick={() => onExport('csv')}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <FileText className="h-3 w-3 mr-1" />
                Export CSV
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* 导入模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setShowImportModal(false)} />
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <Upload className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Import Products
                    </h3>
                    <div className="mt-4 space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Import Mode
                        </label>
                        <select
                          value={importMode}
                          onChange={(e) => setImportMode(e.target.value as any)}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tractor focus:border-tractor text-sm"
                        >
                          <option value="create">Create Only (skip existing)</option>
                          <option value="update">Update Only (skip new)</option>
                          <option value="upsert">Create or Update</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Select File
                        </label>
                        <input
                          type="file"
                          accept=".json,.csv"
                          onChange={handleFileImport}
                          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-tractor file:text-white hover:file:bg-tractor/90"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Supports JSON and CSV files
                        </p>
                      </div>
                      
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <div className="flex">
                          <AlertTriangle className="h-5 w-5 text-yellow-400" />
                          <div className="ml-3">
                            <h4 className="text-sm font-medium text-yellow-800">
                              Import Guidelines
                            </h4>
                            <div className="mt-1 text-sm text-yellow-700">
                              <ul className="list-disc list-inside space-y-1">
                                <li>Required fields: title, description, imageUrl, affiliateUrl</li>
                                <li>Use semicolons (;) to separate multiple tags/matches</li>
                                <li>Boolean fields: true/false or 1/0</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => setShowImportModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}