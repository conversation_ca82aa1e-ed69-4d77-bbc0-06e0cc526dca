'use client';

import { AffiliateProduct } from '@/lib/types';
import { Star, ExternalLink, Tag } from 'lucide-react';

interface ProductPreviewProps {
  product: Partial<AffiliateProduct>;
  className?: string;
}

export default function ProductPreview({ product, className = '' }: ProductPreviewProps) {
  const {
    title = '',
    description = '',
    imageUrl = '',
    rating = 0,
    reviewCount = 0,
    isHotPick = false,
    isOnSale = false,
    tags = [],
    category = 'tractor'
  } = product;

  const categoryColors = {
    tractor: 'bg-blue-100 text-blue-800',
    equipment: 'bg-purple-100 text-purple-800',
    parts: 'bg-orange-100 text-orange-800',
    accessories: 'bg-gray-100 text-gray-800'
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow ${className}`}>
      {/* 产品图片 */}
      <div className="relative">
        <img
          src={imageUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOWNhM2FmIj5Qcm9kdWN0IEltYWdlPC90ZXh0Pjwvc3ZnPg=='}
          alt={title}
          className="w-full h-48 object-cover"
          onError={(e) => {
            (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOWNhM2FmIj5JbWFnZSBub3QgZm91bmQ8L3RleHQ+PC9zdmc+';
          }}
        />
        
        {/* 标签 */}
        <div className="absolute top-2 left-2 flex flex-wrap gap-1">
          {isHotPick && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              🔥 Hot Pick
            </span>
          )}
          {isOnSale && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              💰 On Sale
            </span>
          )}
        </div>

        {/* 分类标签 */}
        <div className="absolute top-2 right-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            categoryColors[category as keyof typeof categoryColors] || categoryColors.accessories
          }`}>
            {category}
          </span>
        </div>
      </div>

      {/* 产品信息 */}
      <div className="p-4">
        {/* 标题 */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {title || 'Product Title'}
        </h3>

        {/* 描述 */}
        <p className="text-sm text-gray-600 mb-3 line-clamp-3">
          {description || 'Product description will appear here...'}
        </p>

        {/* 评分 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < Math.round(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-500">
              {rating.toFixed(1)}/5 ({reviewCount || 0} reviews)
            </span>
          </div>
        </div>

        {/* 标签 */}
        {tags.length > 0 && (
          <div className="flex items-center mb-3">
            <Tag className="h-3 w-3 text-gray-400 mr-1" />
            <div className="flex flex-wrap gap-1">
              {tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {tag}
                </span>
              ))}
              {tags.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <button className="flex-1 bg-tractor text-white px-4 py-2 rounded-md hover:bg-tractor/90 transition-colors text-sm font-medium">
            View on Amazon
          </button>
          <button className="ml-2 p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}