'use client';

import { useState, useEffect } from 'react';
import { RefreshCw, Database, Eye, AlertTriangle } from 'lucide-react';
import { AffiliateProduct } from '@/lib/types';

interface DataStatusCheckerProps {
  products: AffiliateProduct[];
  onRefresh: () => void;
}

export default function DataStatusChecker({ products, onRefresh }: DataStatusCheckerProps) {
  const [isChecking, setIsChecking] = useState(false);
  const [dbData, setDbData] = useState<AffiliateProduct[]>([]);
  const [showComparison, setShowComparison] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('');

  // 避免hydration错误，只在客户端设置时间
  useEffect(() => {
    setCurrentTime(new Date().toLocaleTimeString());
  }, []);

  const checkDatabaseData = async () => {
    setIsChecking(true);
    try {
      const response = await fetch('/api/admin/affiliate-products?limit=1000');
      if (response.ok) {
        const data = await response.json();
        setDbData(data.data || []);
        setShowComparison(true);
      }
    } catch (error) {
      console.error('Failed to check database:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const findDataDifferences = () => {
    const differences = [];
    
    // 检查数量差异
    if (products.length !== dbData.length) {
      differences.push({
        type: 'count',
        message: `数量不一致: 当前显示 ${products.length} 个，数据库有 ${dbData.length} 个`
      });
    }

    // 检查具体产品差异
    const productIds = new Set(products.map(p => p._id));
    const dbIds = new Set(dbData.map(p => p._id));
    
    const missingInCurrent = dbData.filter(p => !productIds.has(p._id));
    const missingInDb = products.filter(p => !dbIds.has(p._id));
    
    if (missingInCurrent.length > 0) {
      differences.push({
        type: 'missing_current',
        message: `当前页面缺少 ${missingInCurrent.length} 个产品`,
        details: missingInCurrent.map(p => p.title)
      });
    }
    
    if (missingInDb.length > 0) {
      differences.push({
        type: 'missing_db',
        message: `数据库缺少 ${missingInDb.length} 个产品`,
        details: missingInDb.map(p => p.title)
      });
    }

    // 检查内容差异
    const contentDiffs = [];
    products.forEach(currentProduct => {
      const dbProduct = dbData.find(p => p._id === currentProduct._id);
      if (dbProduct) {
        if (currentProduct.title !== dbProduct.title) {
          contentDiffs.push(`${currentProduct._id}: 标题不一致`);
        }
        if (currentProduct.enabled !== dbProduct.enabled) {
          contentDiffs.push(`${currentProduct._id}: 启用状态不一致`);
        }
        if (JSON.stringify(currentProduct.translations) !== JSON.stringify(dbProduct.translations)) {
          contentDiffs.push(`${currentProduct._id}: 翻译内容不一致`);
        }
      }
    });

    if (contentDiffs.length > 0) {
      differences.push({
        type: 'content',
        message: `${contentDiffs.length} 个产品内容不一致`,
        details: contentDiffs
      });
    }

    return differences;
  };

  const differences = showComparison ? findDataDifferences() : [];

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Database className="h-5 w-5 text-gray-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900">数据状态检查</h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={checkDatabaseData}
            disabled={isChecking}
            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
          >
            {isChecking ? (
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <Eye className="h-3 w-3 mr-1" />
            )}
            {isChecking ? '检查中...' : '检查数据库'}
          </button>
          <button
            onClick={onRefresh}
            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-white bg-tractor hover:bg-tractor/90"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            刷新页面数据
          </button>
        </div>
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <div>当前页面显示: {products.length} 个产品</div>
        {showComparison && <div>数据库实际: {dbData.length} 个产品</div>}
        <div>最后更新: {currentTime || '加载中...'}</div>
      </div>

      {differences.length > 0 && (
        <div className="mt-4 space-y-2">
          <div className="flex items-center text-amber-600">
            <AlertTriangle className="h-4 w-4 mr-1" />
            <span className="text-sm font-medium">发现数据不一致</span>
          </div>
          {differences.map((diff, index) => (
            <div key={index} className="bg-amber-50 border border-amber-200 rounded p-3">
              <div className="text-sm text-amber-800 font-medium">{diff.message}</div>
              {diff.details && (
                <div className="mt-1 text-xs text-amber-700">
                  {Array.isArray(diff.details) ? (
                    <ul className="list-disc list-inside">
                      {diff.details.slice(0, 5).map((detail, i) => (
                        <li key={i}>{detail}</li>
                      ))}
                      {diff.details.length > 5 && (
                        <li>... 还有 {diff.details.length - 5} 个</li>
                      )}
                    </ul>
                  ) : (
                    <div>{diff.details}</div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {showComparison && differences.length === 0 && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded p-3">
          <div className="text-sm text-green-800">✅ 数据状态一致，没有发现问题</div>
        </div>
      )}
    </div>
  );
}
