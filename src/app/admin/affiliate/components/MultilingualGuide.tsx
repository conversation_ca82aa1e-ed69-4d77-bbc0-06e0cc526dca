'use client';

import { useState } from 'react';
import { HelpCircle, X, Globe, Languages, Eye, Settings } from 'lucide-react';

export default function MultilingualGuide() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      {/* 帮助按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        title="多语言配置帮助"
      >
        <HelpCircle className="h-4 w-4 mr-2" />
        多语言帮助
      </button>

      {/* 帮助弹窗 */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setIsOpen(false)}></div>

            <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <Languages className="h-6 w-6 text-tractor mr-3" />
                  <h3 className="text-lg font-medium text-gray-900">多语言广告配置指南</h3>
                </div>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* 概述 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">功能概述</h4>
                  <p className="text-sm text-blue-800">
                    多语言功能允许您为每个广告产品配置不同语言的标题和描述，提升国际用户的体验。
                    系统会根据用户访问的页面语言自动显示对应的翻译内容。
                  </p>
                </div>

                {/* 配置步骤 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <Settings className="h-4 w-4 mr-2" />
                    配置步骤
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-tractor text-white rounded-full flex items-center justify-center text-xs font-medium">1</div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">填写基础信息</p>
                        <p className="text-sm text-gray-600">先填写英文的产品标题和描述（必填）</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-tractor text-white rounded-full flex items-center justify-center text-xs font-medium">2</div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">展开多语言编辑</p>
                        <p className="text-sm text-gray-600">点击"显示 翻译编辑"按钮展开多语言编辑区域</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-tractor text-white rounded-full flex items-center justify-center text-xs font-medium">3</div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">选择语言标签</p>
                        <p className="text-sm text-gray-600">点击对应的语言标签（中文、法文、德文等）</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-tractor text-white rounded-full flex items-center justify-center text-xs font-medium">4</div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">输入翻译内容</p>
                        <p className="text-sm text-gray-600">为选中的语言输入对应的标题和描述翻译</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-6 h-6 bg-tractor text-white rounded-full flex items-center justify-center text-xs font-medium">5</div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">保存产品</p>
                        <p className="text-sm text-gray-600">点击"Save Product"保存所有语言的内容</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 支持的语言 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    支持的语言
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇺🇸</span>
                      <span className="text-sm text-gray-700">English（英文）- 默认语言</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇨🇳</span>
                      <span className="text-sm text-gray-700">中文（简体）</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇫🇷</span>
                      <span className="text-sm text-gray-700">Français（法文）</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇩🇪</span>
                      <span className="text-sm text-gray-700">Deutsch（德文）</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇪🇸</span>
                      <span className="text-sm text-gray-700">Español（西班牙文）</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">🇵🇹</span>
                      <span className="text-sm text-gray-700">Português（葡萄牙文）</span>
                    </div>
                  </div>
                </div>

                {/* 显示效果 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                    <Eye className="h-4 w-4 mr-2" />
                    显示效果
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-700">英文页面访问者看到：</p>
                        <p className="text-sm text-gray-600 bg-white p-2 rounded border">John Deere 1:16 Big Farm Tractor Model</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">中文页面访问者看到：</p>
                        <p className="text-sm text-gray-600 bg-white p-2 rounded border">约翰迪尔1:16农场拖拉机模型</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">法文页面访问者看到：</p>
                        <p className="text-sm text-gray-600 bg-white p-2 rounded border">Modèle de Tracteur John Deere 1:16</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 注意事项 */}
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-amber-900 mb-2">注意事项</h4>
                  <ul className="text-sm text-amber-800 space-y-1">
                    <li>• 英文是默认语言，必须填写</li>
                    <li>• 其他语言的翻译是可选的，如果没有翻译会显示英文内容</li>
                    <li>• 翻译内容会自动保存到数据库</li>
                    <li>• 可以随时编辑或删除某个语言的翻译</li>
                    <li>• 建议翻译内容保持与英文原意一致</li>
                  </ul>
                </div>

                {/* 测试链接 */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-900 mb-2">测试效果</h4>
                  <p className="text-sm text-green-800 mb-3">配置完成后，您可以访问以下链接查看不同语言的显示效果：</p>
                  <div className="space-y-1 text-sm">
                    <div>• 英文：<code className="bg-white px-2 py-1 rounded">http://localhost:3001/</code></div>
                    <div>• 中文：<code className="bg-white px-2 py-1 rounded">http://localhost:3001/zh</code></div>
                    <div>• 法文：<code className="bg-white px-2 py-1 rounded">http://localhost:3001/fr</code></div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 bg-tractor text-white text-sm font-medium rounded-md hover:bg-tractor/90"
                >
                  我知道了
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
