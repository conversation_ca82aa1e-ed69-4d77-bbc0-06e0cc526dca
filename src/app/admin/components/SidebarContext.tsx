'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';

// TypeScript interfaces
interface SidebarState {
  collapsed: boolean;
  mobileOpen: boolean;
  lastUpdated: number;
}

interface SidebarContextType {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  toggleCollapse: () => void;
  toggleMobile: () => void;
  closeMobile: () => void;
}

interface SidebarProviderProps {
  children: ReactNode;
}

// Storage utilities
const STORAGE_KEY = 'admin-sidebar-state';

const getStoredState = (): Partial<SidebarState> => {
  try {
    if (typeof window === 'undefined') return {};
    
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return {};
    
    const parsed = JSON.parse(stored);
    
    // Validate stored data
    if (typeof parsed.collapsed !== 'boolean') {
      console.warn('Invalid sidebar state in localStorage, resetting');
      localStorage.removeItem(STORAGE_KEY);
      return {};
    }
    
    return parsed;
  } catch (error) {
    console.warn('Failed to parse sidebar state from localStorage:', error);
    localStorage.removeItem(STORAGE_KEY);
    return {};
  }
};

const saveState = (state: Partial<SidebarState>): void => {
  try {
    if (typeof window === 'undefined') return;
    
    const stateToSave = {
      ...state,
      lastUpdated: Date.now()
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));
  } catch (error) {
    console.warn('Failed to save sidebar state to localStorage:', error);
  }
};

// Create context
const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Provider component
export function SidebarProvider({ children }: SidebarProviderProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Initialize state from localStorage on mount
  useEffect(() => {
    const storedState = getStoredState();
    
    if (typeof storedState.collapsed === 'boolean') {
      setIsCollapsed(storedState.collapsed);
    }
  }, []);

  // Save state changes to localStorage
  useEffect(() => {
    saveState({ collapsed: isCollapsed });
  }, [isCollapsed]);

  // Cross-tab synchronization
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEY && e.newValue) {
        try {
          const newState = JSON.parse(e.newValue);
          if (typeof newState.collapsed === 'boolean') {
            setIsCollapsed(newState.collapsed);
          }
        } catch (error) {
          console.warn('Failed to sync sidebar state across tabs:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Ctrl/Cmd + B to toggle sidebar (desktop only)
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
      e.preventDefault();
      // Only toggle on desktop (lg+ screens)
      if (window.innerWidth >= 1024) {
        setIsCollapsed(prev => !prev);
      }
    }
    
    // Escape to close mobile sidebar
    if (e.key === 'Escape' && isMobileOpen) {
      setIsMobileOpen(false);
    }
  }, [isMobileOpen]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Context methods
  const toggleCollapse = () => {
    setIsCollapsed(prev => !prev);
  };

  const toggleMobile = () => {
    setIsMobileOpen(prev => !prev);
  };

  const closeMobile = () => {
    setIsMobileOpen(false);
  };

  const contextValue: SidebarContextType = {
    isCollapsed,
    isMobileOpen,
    toggleCollapse,
    toggleMobile,
    closeMobile
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {children}
    </SidebarContext.Provider>
  );
}

// Custom hook to use sidebar context
export function useSidebar(): SidebarContextType {
  const context = useContext(SidebarContext);
  
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  
  return context;
}