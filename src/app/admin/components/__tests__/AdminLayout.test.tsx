/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import AdminLayout from '../AdminLayout';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('AdminLayout', () => {
  beforeEach(() => {
    mockUsePathname.mockReturnValue('/admin');
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    
    // Mock window dimensions for responsive tests
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });
  });

  it('should render admin layout with sidebar and main content', () => {
    render(
      <AdminLayout>
        <div data-testid="test-content">Test Content</div>
      </AdminLayout>
    );

    expect(screen.getByRole('complementary', { name: /admin navigation sidebar/i })).toBeInTheDocument();
    expect(screen.getByText('Admin Panel')).toBeInTheDocument();
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('should render all navigation items', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Affiliate Management')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Performance')).toBeInTheDocument();
    expect(screen.getByText('SEO Tools')).toBeInTheDocument();
  });

  it('should highlight active navigation item', () => {
    mockUsePathname.mockReturnValue('/admin/affiliate');
    
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const affiliateLink = screen.getByRole('link', { name: /affiliate management/i });
    expect(affiliateLink).toHaveClass('bg-tractor', 'text-white');
  });

  it('should toggle sidebar collapse on desktop', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    
    fireEvent.click(collapseButton);
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
    });
  });

  it('should handle mobile sidebar toggle', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      value: 768,
    });

    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const openButton = screen.getByRole('button', { name: /open sidebar/i });
    fireEvent.click(openButton);

    expect(screen.getByRole('button', { name: /close sidebar/i })).toBeInTheDocument();
  });

  it('should close mobile sidebar when clicking backdrop', () => {
    Object.defineProperty(window, 'innerWidth', {
      value: 768,
    });

    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // Open mobile sidebar
    const openButton = screen.getByRole('button', { name: /open sidebar/i });
    fireEvent.click(openButton);

    // Click backdrop
    const backdrop = document.querySelector('.bg-gray-600.bg-opacity-75');
    expect(backdrop).toBeInTheDocument();
    
    if (backdrop) {
      fireEvent.click(backdrop);
    }

    expect(screen.queryByRole('button', { name: /close sidebar/i })).not.toBeInTheDocument();
  });

  it('should have proper ARIA attributes', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const sidebar = screen.getByRole('complementary');
    expect(sidebar).toHaveAttribute('aria-label', 'Admin navigation sidebar');
    expect(sidebar).toHaveAttribute('aria-expanded', 'false');

    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    expect(collapseButton).toHaveAttribute('title');
  });

  it('should show tooltips for collapsed navigation items', async () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    // Collapse sidebar
    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    fireEvent.click(collapseButton);

    await waitFor(() => {
      const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
      expect(dashboardLink).toHaveAttribute('title', 'Dashboard');
    });
  });

  it('should handle keyboard navigation', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    
    // Test focus styles
    collapseButton.focus();
    expect(collapseButton).toHaveClass('focus:ring-2', 'focus:ring-tractor');
  });

  it('should render back to site link', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    const backLink = screen.getByRole('link', { name: /back to site/i });
    expect(backLink).toHaveAttribute('href', '/');
  });

  it('should render user info section', () => {
    render(
      <AdminLayout>
        <div>Test Content</div>
      </AdminLayout>
    );

    expect(screen.getByText('Admin User')).toBeInTheDocument();
    expect(screen.getByText('Administrator')).toBeInTheDocument();
  });
});