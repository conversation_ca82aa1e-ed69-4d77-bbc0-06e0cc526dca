/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AdminLayout from '../AdminLayout';

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/admin'),
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('AdminLayout Integration Tests', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    
    // Mock desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });
  });

  it('should complete full sidebar collapse/expand workflow', async () => {
    render(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    // Initial state - sidebar should be expanded
    expect(screen.getByText('Admin Panel')).toBeVisible();
    expect(screen.getByText('Dashboard')).toBeVisible();

    // Collapse sidebar
    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    fireEvent.click(collapseButton);

    // Verify collapsed state
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
    });

    // Verify localStorage was called
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'admin-sidebar-state',
      expect.stringContaining('"collapsed":true')
    );

    // Expand sidebar again
    const expandButton = screen.getByRole('button', { name: /expand sidebar/i });
    fireEvent.click(expandButton);

    // Verify expanded state
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /collapse sidebar/i })).toBeInTheDocument();
    });
  });

  it('should handle mobile workflow correctly', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      value: 768,
    });

    render(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    // Mobile sidebar should be closed initially
    expect(screen.queryByRole('button', { name: /close sidebar/i })).not.toBeInTheDocument();

    // Open mobile sidebar
    const openButton = screen.getByRole('button', { name: /open sidebar/i });
    fireEvent.click(openButton);

    // Verify mobile sidebar is open
    expect(screen.getByRole('button', { name: /close sidebar/i })).toBeInTheDocument();
    
    // Verify backdrop is present
    const backdrop = document.querySelector('.bg-gray-600.bg-opacity-75');
    expect(backdrop).toBeInTheDocument();

    // Close via backdrop
    if (backdrop) {
      fireEvent.click(backdrop);
    }

    // Verify sidebar is closed
    expect(screen.queryByRole('button', { name: /close sidebar/i })).not.toBeInTheDocument();
  });

  it('should persist and restore sidebar state', () => {
    // Mock stored collapsed state
    localStorageMock.getItem.mockReturnValue(
      JSON.stringify({ collapsed: true, lastUpdated: Date.now() })
    );

    render(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    // Should restore collapsed state
    expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
  });

  it('should handle responsive breakpoint changes', () => {
    const { rerender } = render(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    // Desktop - should show collapse button
    expect(screen.getByRole('button', { name: /collapse sidebar/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /open sidebar/i })).not.toBeInTheDocument();

    // Switch to mobile
    Object.defineProperty(window, 'innerWidth', {
      value: 768,
    });

    rerender(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    // Mobile - should show open button, hide collapse button
    expect(screen.getByRole('button', { name: /open sidebar/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /collapse sidebar/i })).not.toBeInTheDocument();
  });

  it('should maintain accessibility throughout interactions', async () => {
    render(
      <AdminLayout>
        <div data-testid="main-content">Main Content</div>
      </AdminLayout>
    );

    const sidebar = screen.getByRole('complementary');
    
    // Check initial ARIA attributes
    expect(sidebar).toHaveAttribute('aria-label', 'Admin navigation sidebar');
    expect(sidebar).toHaveAttribute('aria-expanded', 'false');

    // Test keyboard navigation
    const collapseButton = screen.getByRole('button', { name: /collapse sidebar/i });
    
    // Focus should work
    collapseButton.focus();
    expect(document.activeElement).toBe(collapseButton);

    // Keyboard activation should work
    fireEvent.keyDown(collapseButton, { key: 'Enter' });
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
    });
  });
});