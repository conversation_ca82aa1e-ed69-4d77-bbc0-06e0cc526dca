/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SidebarProvider, useSidebar } from '../SidebarContext';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test component that uses the sidebar context
function TestComponent() {
  const { isCollapsed, isMobileOpen, toggleCollapse, toggleMobile, closeMobile } = useSidebar();
  
  return (
    <div>
      <div data-testid="collapsed-state">{isCollapsed.toString()}</div>
      <div data-testid="mobile-open-state">{isMobileOpen.toString()}</div>
      <button data-testid="toggle-collapse" onClick={toggleCollapse}>
        Toggle Collapse
      </button>
      <button data-testid="toggle-mobile" onClick={toggleMobile}>
        Toggle Mobile
      </button>
      <button data-testid="close-mobile" onClick={closeMobile}>
        Close Mobile
      </button>
    </div>
  );
}

describe('SidebarContext', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
  });

  it('should provide default state values', () => {
    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('false');
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('false');
  });

  it('should toggle collapse state', () => {
    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    const toggleButton = screen.getByTestId('toggle-collapse');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('true');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('false');
  });

  it('should toggle mobile state', () => {
    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    const toggleButton = screen.getByTestId('toggle-mobile');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('true');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('false');
  });

  it('should close mobile sidebar', () => {
    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    const toggleButton = screen.getByTestId('toggle-mobile');
    const closeButton = screen.getByTestId('close-mobile');
    
    // Open mobile sidebar
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('true');
    
    // Close mobile sidebar
    fireEvent.click(closeButton);
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('false');
  });

  it('should save collapsed state to localStorage', async () => {
    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    const toggleButton = screen.getByTestId('toggle-collapse');
    fireEvent.click(toggleButton);

    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'admin-sidebar-state',
        expect.stringContaining('"collapsed":true')
      );
    });
  });

  it('should restore state from localStorage', () => {
    localStorageMock.getItem.mockReturnValue(
      JSON.stringify({ collapsed: true, lastUpdated: Date.now() })
    );

    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('true');
  });

  it('should handle invalid localStorage data gracefully', () => {
    localStorageMock.getItem.mockReturnValue('invalid-json');
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('false');
    expect(consoleSpy).toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });

  it('should handle keyboard shortcuts', () => {
    // Mock window.innerWidth for desktop detection
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });

    render(
      <SidebarProvider>
        <TestComponent />
      </SidebarProvider>
    );

    // Test Ctrl+B shortcut
    fireEvent.keyDown(window, { key: 'b', ctrlKey: true });
    expect(screen.getByTestId('collapsed-state')).toHaveTextContent('true');

    // Test Escape key for mobile
    fireEvent.click(screen.getByTestId('toggle-mobile'));
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('true');
    
    fireEvent.keyDown(window, { key: 'Escape' });
    expect(screen.getByTestId('mobile-open-state')).toHaveTextContent('false');
  });

  it('should throw error when used outside provider', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useSidebar must be used within a SidebarProvider');
    
    consoleSpy.mockRestore();
  });
});