import { Metadata } from 'next';
import AdminLayout from '../components/AdminLayout';
import { TrendingUp, Clock, Zap, Globe } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Performance | Admin Dashboard',
  description: 'Monitor and optimize website performance',
  robots: 'noindex, nofollow'
};

export default function PerformancePage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Performance Monitoring</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor website performance, loading times, and user experience metrics.
          </p>
        </div>

        {/* Coming Soon Card */}
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-tractor/10 mb-6">
            <TrendingUp className="h-8 w-8 text-tractor" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Dashboard Coming Soon</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            We&apos;re building comprehensive performance monitoring tools to help you optimize your website&apos;s speed and user experience.
          </p>
          
          {/* Feature Preview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
            <div className="p-4 border border-gray-200 rounded-lg">
              <Clock className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Load Time Analysis</h3>
              <p className="text-sm text-gray-500 mt-1">Monitor page load times and identify bottlenecks</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Zap className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Core Web Vitals</h3>
              <p className="text-sm text-gray-500 mt-1">Track LCP, FID, and CLS metrics</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Globe className="h-6 w-6 text-tractor mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Global Performance</h3>
              <p className="text-sm text-gray-500 mt-1">Monitor performance across different regions</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}