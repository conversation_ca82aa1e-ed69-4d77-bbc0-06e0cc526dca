import { Metadata } from 'next';
import AdminLayout from './components/AdminLayout';
import { 
  BarChart3, 
  ShoppingCart, 
  TrendingUp, 
  DollarSign,
  MousePointer
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Admin Dashboard | TractorData',
  description: 'Administrative dashboard for TractorData website management',
  robots: 'noindex, nofollow'
};

interface DashboardCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
}

function DashboardCard({ title, value, change, changeType, icon: Icon }: DashboardCardProps) {
  const changeColor = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-gray-600'
  }[changeType];

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
        <div className="flex-shrink-0">
          <Icon className="h-8 w-8 text-tractor" />
        </div>
      </div>
      <div className="mt-4">
        <span className={`text-sm font-medium ${changeColor}`}>
          {change}
        </span>
        <span className="text-sm text-gray-500 ml-1">from last month</span>
      </div>
    </div>
  );
}

export default function AdminDashboard() {
  const dashboardStats = [
    {
      title: 'Total Clicks',
      value: '12,345',
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: MousePointer
    },
    {
      title: 'Revenue',
      value: '$2,456',
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: DollarSign
    },
    {
      title: 'Active Products',
      value: '156',
      change: '+3',
      changeType: 'positive' as const,
      icon: ShoppingCart
    },
    {
      title: 'Conversion Rate',
      value: '3.2%',
      change: '-0.1%',
      changeType: 'negative' as const,
      icon: TrendingUp
    }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome to the TractorData admin panel. Here&apos;s an overview of your affiliate performance.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {dashboardStats.map((stat, index) => (
            <DashboardCard key={index} {...stat} />
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <a
                href="/admin/affiliate"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ShoppingCart className="h-6 w-6 text-tractor mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Manage Products</h3>
                  <p className="text-sm text-gray-500">Add, edit, or remove affiliate products</p>
                </div>
              </a>

              <a
                href="/admin/affiliate/analytics"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <BarChart3 className="h-6 w-6 text-tractor mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-gray-900">View Analytics</h3>
                  <p className="text-sm text-gray-500">Check performance metrics and reports</p>
                </div>
              </a>

              <a
                href="/admin/seo"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <TrendingUp className="h-6 w-6 text-tractor mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-gray-900">SEO Tools</h3>
                  <p className="text-sm text-gray-500">Optimize content and performance</p>
                </div>
              </a>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <MousePointer className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    New click recorded for <span className="font-medium">John Deere Tractor Parts</span>
                  </p>
                  <p className="text-sm text-gray-500">2 minutes ago</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <ShoppingCart className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    Product <span className="font-medium">Kubota Engine Oil</span> was updated
                  </p>
                  <p className="text-sm text-gray-500">1 hour ago</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <TrendingUp className="h-4 w-4 text-yellow-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    Weekly performance report generated
                  </p>
                  <p className="text-sm text-gray-500">3 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}