import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { Header } from "../components/Header";
import { Footer } from "../components/Footer";
import { NewsArticlesList } from "./components/NewsArticlesList";
import { NewsHero } from "./components/NewsHero";
import { getLatestNews, type NewsArticle } from "@/services/newsService";
import { getAllLocaleParams } from '@/lib/cache';

// ISR配置 - 新闻页面使用增量静态再生成
export const revalidate = 1800; // 30分钟重新验证

// 生成所有语言版本的静态参数
export async function generateStaticParams() {
  // 渐进式ISR：根据构建模式决定预生成策略
  const buildMode = process.env.BUILD_MODE || 'full';

  if (buildMode === 'test') {
    // 测试模式：只生成英语版本
    return [{ locale: 'en' }];
  }

  // 完整模式：生成所有语言版本
  return getAllLocaleParams();
}

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  // Ensure params is resolved before accessing its properties
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'NewsPage.metadata' });

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords')
    // 移除 alternates 配置，使用 layout 中的全局 hreflang 设置
  };
}

export default async function NewsPage({ params }: { params: { locale: string } }) {
  // 获取本地化翻译内容
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'NewsPage' });

  // 获取最新新闻
  let latestNews: NewsArticle[] = [];
  try {
    latestNews = await getLatestNews(resolvedParams.locale, 50);
  } catch (error) {
    console.error('Error fetching news:', error);
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main>
        <NewsHero />

        {/* 新闻列表部分 */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            {latestNews.length > 0 ? (
              <NewsArticlesList articles={latestNews} />
            ) : (
              <div className="text-center py-16">
                <h2 className="text-xl font-bold text-gray-700 mb-2">{t('noNews.title')}</h2>
                <p className="text-gray-500">{t('noNews.message')}</p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
