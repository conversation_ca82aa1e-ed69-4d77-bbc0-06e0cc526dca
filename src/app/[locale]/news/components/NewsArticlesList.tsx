import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import type { NewsArticle } from '@/services/newsService';

interface NewsArticlesListProps {
  articles: NewsArticle[];
}

export function NewsArticlesList({ articles }: NewsArticlesListProps) {
  const t = useTranslations('NewsPage');
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {articles.map(article => (
        <article
          key={article._id}
          className="group bg-white border border-gray-200 rounded-lg hover:border-tractor hover:shadow-sm transition-all p-6 flex flex-col h-full"
        >
          <div className="flex-grow">
            {article.date && (
              <div className="text-xs text-gray-500 mb-2">{article.date}</div>
            )}
            <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-tractor transition-colors line-clamp-2">
              <Link href={`/news/${article.slug}`} className="hover:underline">
                {article.title}
              </Link>
            </h2>
            <p className="text-gray-700 text-sm line-clamp-3 mb-4">{article.summary}</p>
            
            {article.images && article.images.length > 0 && (
              <div className="mt-2 mb-4">
                <img 
                  src={article.images[0].url} 
                  alt={article.images[0].alt || article.title}
                  className="w-full h-40 object-cover rounded-md"
                />
              </div>
            )}
          </div>
          
          <div className="mt-2 flex items-center justify-between">
            <Link
              href={`/news/${article.slug}`}
              className="inline-flex items-center text-tractor font-medium hover:underline text-sm"
            >
              {t('readMore')}
              <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </Link>
            
            <span className="text-xs text-gray-500">{article.source}</span>
          </div>
        </article>
      ))}
    </div>
  );
}
