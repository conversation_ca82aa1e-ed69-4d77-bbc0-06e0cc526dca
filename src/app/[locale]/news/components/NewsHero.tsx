import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export function NewsHero() {
  const t = useTranslations('NewsPage.hero');
  
  return (
    <section className="bg-gradient-to-r from-tractor/90 to-tractor py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center text-white">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">{t('title')}</h1>
          <p className="text-lg md:text-xl opacity-90 mb-8">
            {t('subtitle')}
          </p>
          <div className="w-20 h-1 bg-white/40 mx-auto mb-8"></div>
          
          <Link 
            href="/news" 
            className="px-6 py-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors text-sm font-medium inline-block"
          >
            {t('browseAll')}
          </Link>
        </div>
      </div>
    </section>
  );
}
