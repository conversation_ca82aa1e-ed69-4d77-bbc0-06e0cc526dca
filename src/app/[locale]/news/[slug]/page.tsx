import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Header } from '../../components/Header';
import { Footer } from '../../components/Footer';
import { Breadcrumb } from '../components/Breadcrumb';
import { getNewsBySlug, getLatestNews, type NewsArticle } from '@/services/newsService';
import { ArticleHeader, ArticleContent, RelatedArticles, NewsPageAds } from './components';
import Breadcrumbs from '@/components/Breadcrumbs';
import JsonLd from '@/components/JsonLd';
import { getCachedNews } from '@/lib/cache';
import { routing } from '@/i18n/routing';

// ISR配置 - 新闻详情页使用增量静态再生成
export const revalidate = 3600; // 1小时重新验证
export const dynamicParams = true; // 允许动态生成未预生成的页面

// 生成静态参数 - 预生成最新的新闻文章
export async function generateStaticParams() {
  // 在构建时，如果无法连接数据库，返回空数组
  if (process.env.NODE_ENV === 'production' && process.env.BUILD_TIME === 'true') {
    console.log('构建时跳过新闻静态参数生成');
    return [];
  }

  try {
    const buildMode = process.env.BUILD_MODE || 'full';
    const params = [];

    // 全量生成所有新闻文章
    const maxArticles = Infinity; // 生成所有新闻

    for (const locale of routing.locales) {
      try {
        const news = await getCachedNews(locale, maxArticles);

        for (const article of news) {
          if (article.slug) {
            params.push({
              locale: locale,
              slug: article.slug
            });
          }
        }
      } catch (error) {
        console.warn(`跳过 ${locale} 语言新闻参数生成:`, error.message);
        continue;
      }
    }

    console.log(`生成 ${params.length} 个新闻详情页静态参数 (${buildMode} 模式)`);
    return params;
  } catch (error) {
    console.error('生成新闻静态参数失败:', error);
    return [];
  }
}

// 获取相关文章
async function getRelatedArticles(currentArticle: NewsArticle, locale: string, limit = 3): Promise<NewsArticle[]> {
  try {
    // 获取最新新闻
    const latestNews = await getLatestNews(locale, limit + 1);

    // 过滤掉当前文章
    return latestNews.filter(article => article.slug !== currentArticle.slug).slice(0, limit);
  } catch (error) {
    console.error('获取相关文章时出错:', error);
    return [];
  }
}

// 生成页面元数据
export async function generateMetadata({ params }: { params: { slug: string; locale: string } }): Promise<Metadata> {
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'NewsPage.articleDetail' });
  const article = await getNewsBySlug(resolvedParams.slug, resolvedParams.locale);

  if (!article) {
    return {
      title: t('notFound.title'),
      description: t('notFound.description')
    };
  }

  // 构建更丰富的元数据
  const hasImages = article.images && article.images.length > 0;
  const mainImage = hasImages ? article.images[0] : null;
  const keywords = article.tags ? article.tags.join(', ') : `tractor news, ${article.title}, farm equipment news`;

  return {
    title: t('metaTitle', { articleTitle: article.title }),
    description: article.summary || article.title,
    keywords: keywords,
    // 移除硬编码的 alternates 对象，使用 layout 中的全局 hreflang 设置
    openGraph: {
      title: article.title,
      description: article.summary || article.title,
      type: 'article',
      publishedTime: article.date,
      modifiedTime: article.updatedAt || article.date,
      authors: [article.author || article.source || 'TractorData'],
      section: 'News',
      tags: article.tags,
      images: hasImages ? [{
        url: mainImage.url,
        alt: mainImage.alt || article.title,
        width: 1200,
        height: 630
      }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.summary || article.title,
      images: hasImages ? [mainImage.url] : [],
      creator: article.author || article.source || '@TractorData'
    }
  };
}

export default async function NewsArticlePage({ params }: { params: { slug: string; locale: string } }) {
  // 确保 params 已被解析
  const resolvedParams = await Promise.resolve(params);

  // 获取翻译
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'NewsPage' });

  // 获取文章详情
  const article = await getNewsBySlug(resolvedParams.slug, resolvedParams.locale);

  // 如果文章未找到，返回404
  if (!article) {
    notFound();
  }

  // 获取相关文章
  const relatedArticles = await getRelatedArticles(article, resolvedParams.locale);

  // 面包屑导航路径
  const breadcrumbItems = [
    { label: t('breadcrumb.home'), href: '/' },
    { label: t('breadcrumb.news'), href: '/news' },
    { label: article.source || t('breadcrumb.article'), href: `/news/source/${article.source}` },
    { label: article.title, href: `/news/${article.slug}`, current: true }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow">
        {/* 使用新的面包屑组件 */}
        <div className="container mx-auto px-4 pt-4">
          <Breadcrumbs
            items={[
              { label: t('breadcrumb.home'), path: '/' },
              { label: t('breadcrumb.news'), path: '/news' },
              { label: article.title, path: `/news/${article.slug}`, isLast: true }
            ]}
          />
        </div>

        <article className="py-6 md:py-10">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-sm overflow-hidden">
              <ArticleHeader article={article} />
              <ArticleContent article={article} />
              <div className="mt-8 pt-6 border-t border-gray-200 text-sm text-gray-500 flex flex-col sm:flex-row justify-between items-start sm:items-center px-6 pb-6">
                <div className="mb-2 sm:mb-0">{t('articleDetail.source')}: {article.source}</div>
                <div>{t('articleDetail.publishDate')}: {article.date}</div>
              </div>
            </div>
          </div>
        </article>

        {/* 新闻相关产品推荐 */}
        <NewsPageAds
          newsSlug={resolvedParams.slug}
          newsTitle={article.title}
          locale={resolvedParams.locale}
        />

        {relatedArticles.length > 0 && (
          <RelatedArticles articles={relatedArticles} title={t('articleDetail.relatedArticles')} />
        )}
      </main>

      <Footer />

      {/* 添加文章结构化数据 */}
      <JsonLd
        data={{
          '@context': 'https://schema.org',
          '@type': 'NewsArticle',
          'headline': article.title,
          'description': article.summary || '',
          'image': article.images?.length > 0 ? [article.images[0].url] : [],
          'datePublished': article.date,
          'dateModified': article.updatedAt || article.date,
          'author': article.author ? {
            '@type': 'Person',
            'name': article.author
          } : {
            '@type': 'Organization',
            'name': article.source || 'TractorData'
          },
          'publisher': {
            '@type': 'Organization',
            'name': 'TractorData',
            'logo': {
              '@type': 'ImageObject',
              'url': 'https://tractordata.site/logo.svg'
            }
          },
          'mainEntityOfPage': {
            '@type': 'WebPage',
            '@id': `https://tractordata.site/news/${article.slug}`
          },
          'keywords': article.tags?.join(', ') || '',
          'articleSection': 'News',
          'inLanguage': resolvedParams.locale
        }}
      />
    </div>
  );
}
