import { Link } from '@/i18n/navigation';
import { NewsArticle } from '@/services/newsService';

interface RelatedArticlesProps {
  articles: NewsArticle[];
  title: string;
}

export default function RelatedArticles({ articles, title }: RelatedArticlesProps) {
  if (articles.length === 0) return null;

  return (
    <section className="py-8 md:py-12 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-6">{title}</h2>
          
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <ul className="divide-y divide-gray-200">
              {articles.map(article => (
                <li key={article._id} className="p-4 md:p-6 transition-colors hover:bg-gray-50">
                  <Link
                    href={`/news/${article.slug}`}
                    className="group block"
                  >
                    <h3 className="text-base md:text-lg font-medium text-gray-900 group-hover:text-tractor transition-colors">
                      {article.title}
                    </h3>
                    
                    {article.date && (
                      <p className="text-sm text-gray-500 mt-1">{article.date}</p>
                    )}
                    
                    {article.summary && (
                      <p className="text-gray-600 mt-2 line-clamp-2 text-sm md:text-base">
                        {article.summary}
                      </p>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
} 