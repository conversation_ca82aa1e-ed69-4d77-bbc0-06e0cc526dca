import { NewsArticle } from '@/services/newsService';

interface ArticleContentProps {
  article: NewsArticle;
}

export default function ArticleContent({ article }: ArticleContentProps) {
  return (
    <div className="px-6 md:px-8 py-6">
      {/* 文章主图 */}
      {article.images && article.images.length > 0 && (
        <div className="mb-8">
          <img 
            src={article.images[0].url}
            alt={article.images[0].alt || article.title}
            className="w-full rounded-lg shadow-sm object-cover"
            loading="eager"
            width={800}
            height={450}
          />
          {article.images[0].alt && (
            <p className="text-sm text-gray-500 mt-2 text-center">
              {article.images[0].alt}
            </p>
          )}
        </div>
      )}

      {/* 文章正文 */}
      <div className="prose prose-lg max-w-none mt-6">
        {article.content_text.split('\n').map((paragraph, index) => (
          paragraph.trim() ? (
            <p 
              key={index} 
              className="mb-6 text-gray-800 indent-8 leading-relaxed text-base md:text-lg"
            >
              {paragraph}
            </p>
          ) : <div key={index} className="h-4"></div>
        ))}
      </div>
    </div>
  );
} 