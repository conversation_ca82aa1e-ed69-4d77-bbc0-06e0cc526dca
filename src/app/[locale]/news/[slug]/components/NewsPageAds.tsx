import { AffiliateAdSection } from '../../../components/AffiliateAdSection';
import { PageContext } from '@/lib/types';
import { ProductMatcher } from '@/lib/productMatcher';

interface NewsPageAdsProps {
  newsSlug: string;
  newsTitle: string;
  locale: string;
  className?: string;
}

export async function NewsPageAds({ 
  newsSlug, 
  newsTitle, 
  locale, 
  className = '' 
}: NewsPageAdsProps) {
  // 创建页面上下文
  const pageContext: PageContext = {
    pageType: 'news',
    newsSlug,
    locale
  };

  // 获取与新闻相关的产品
  const newsProducts = await ProductMatcher.getProductsForPage(pageContext, 4);

  // 如果没有相关产品，不显示广告区域
  if (newsProducts.length === 0) {
    return null;
  }

  return (
    <section className={`py-8 bg-gray-50 border-t border-gray-200 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              相关产品推荐
            </h2>
            <p className="text-sm text-gray-600">
              基于本文内容为您推荐的拖拉机相关产品
            </p>
          </div>
          
          <AffiliateAdSection
            products={newsProducts}
            locale={locale}
            pageContext={pageContext}
            className="!py-0 !bg-transparent"
          />
        </div>
      </div>
    </section>
  );
}