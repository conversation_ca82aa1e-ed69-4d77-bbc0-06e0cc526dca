import { NewsArticle } from '@/services/newsService';

interface ArticleHeaderProps {
  article: NewsArticle;
}

export default function ArticleHeader({ article }: ArticleHeaderProps) {
  return (
    <header className="px-6 md:px-8 pt-6 md:pt-8 pb-6 border-b border-gray-200">
      <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">{article.title}</h1>
      
      <div className="flex flex-wrap items-center text-gray-600 text-sm gap-2">
        {article.source && (
          <span className="bg-tractor/10 text-tractor px-2 py-1 rounded text-xs font-medium">
            {article.source}
          </span>
        )}
        
        {article.date && (
          <div className="flex items-center">
            {article.source && <span className="mx-2 hidden sm:inline">•</span>}
            <time dateTime={article.date} className="text-gray-500">{article.date}</time>
          </div>
        )}
      </div>
      
      {/* 文章摘要 */}
      {article.summary && (
        <div className="mt-6 bg-gray-50 p-4 rounded-lg border-l-4 border-tractor italic text-gray-700 text-sm md:text-base">
          {article.summary}
        </div>
      )}
    </header>
  );
} 