import { notFound } from 'next/navigation';
import { Header } from '../../components/Header';
import { Footer } from '../../components/Footer';
import { getTranslations } from 'next-intl/server';
import { getBrandBySlug } from '@/services/brandService';
import {
  BrandHeader,
  BrandModels,
  RelatedBrands,
  BrandPageAds
} from './components';
import Breadcrumbs from '@/components/Breadcrumbs';
import JsonLd from '@/components/JsonLd';
// import BreadcrumbSchema, { generateBreadcrumbItems } from '@/components/BreadcrumbSchema';
// import OrganizationSchema, { generateBrandOrganizationSchema } from '@/components/OrganizationSchema';
import { getCachedBrands } from '@/lib/cache';
import { routing } from '@/i18n/routing';

// 静态生成配置 - 品牌信息相对稳定，设置较长的重新验证时间
export const revalidate = 86400; // 24小时重新验证

// 生成所有品牌和语言组合的静态参数
export async function generateStaticParams() {
  // 在构建时，如果无法连接数据库，返回空数组
  // 这样页面会使用动态渲染
  if (process.env.NODE_ENV === 'production' && process.env.BUILD_TIME === 'true') {
    console.log('构建时跳过品牌静态参数生成');
    return [];
  }

  try {
    const params = [];

    // 全量静态化：生成所有品牌页面
    const isFullBuild = true;
    const maxBrands = Infinity; // 生成所有品牌

    // 为每种语言生成品牌参数
    for (const locale of routing.locales) {
      try {
        const brands = await getCachedBrands(locale);

        // 限制品牌数量以避免超时
        const limitedBrands = brands.slice(0, maxBrands);

        for (const brand of limitedBrands) {
          if (brand.brand_key) {
            params.push({
              locale: locale,
              slug: brand.brand_key
            });
          }
        }
      } catch (localeError) {
        console.warn(`跳过 ${locale} 语言的品牌参数生成:`, localeError.message);
        continue;
      }
    }

    console.log(`生成 ${params.length} 个品牌详情页静态参数 (${isFullBuild ? '完整构建' : '测试构建'})`);
    return params;
  } catch (error) {
    console.error('生成品牌静态参数失败:', error);
    return [];
  }
}

export async function generateMetadata({ params }: { params: { slug: string; locale: string } }) {
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'BrandDetailPage.metadata' });
  const brandData = await getBrandBySlug(resolvedParams.slug, resolvedParams.locale);

  if (!brandData) {
    return {
      title: t('notFound.title'),
      description: t('notFound.description'),
      robots: 'noindex, nofollow'
    };
  }

  // 使用新的SEO工具生成元数据
  const { generateBrandSEOMetadata } = await import('@/lib/seo');

  // 获取品牌信息用于翻译
  const brandName = brandData.brand.name;
  const brandType = brandData.brand.type || t('tractors');

  // 准备翻译对象，传递必要的参数
  const translations = {
    title: t('title', { brandName }),
    description: t('description', { brandName, type: brandType })
  };

  // 添加模型数量到品牌数据
  const enhancedBrandData = {
    ...brandData.brand,
    model_count: brandData.models?.length || 0
  };

  return generateBrandSEOMetadata(enhancedBrandData, resolvedParams.locale, translations);
}

export default async function BrandDetailPage({ params }: { params: { slug: string; locale: string } }) {
  // 确保 params 已被解析
  const resolvedParams = await Promise.resolve(params);

  // 获取翻译内容
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'BrandDetailPage' });

  // 获取品牌数据
  const brandData = await getBrandBySlug(resolvedParams.slug, resolvedParams.locale);

  if (!brandData) {
    notFound();
  }

  // 获取翻译
  const tPage = await getTranslations({ locale: resolvedParams.locale, namespace: 'BrandDetailPage' });

  const { brand, models } = brandData;
  const hasModels = models && models.length > 0;

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />

      <main className="flex-grow">
        {/* 面包屑导航 */}
        <div className="container mx-auto px-4 pt-4">
          <Breadcrumbs
            items={[
              { label: tPage('breadcrumb.home'), path: '/' },
              { label: tPage('breadcrumb.brands'), path: '/brands' },
              { label: brand.name, path: `/brands/${brand.slug}`, isLast: true }
            ]}
          />
        </div>

        {/* 品牌头部 */}
        <BrandHeader
          brand={brand}
          locale={resolvedParams.locale}
          hasModels={hasModels}
          t={t}
        />

        {/* 拖拉机型号区 */}
        {hasModels && (
          <BrandModels
            models={models}
            brand={brand}
            locale={resolvedParams.locale}
            t={t}
          />
        )}

        {/* 品牌相关产品推荐 */}
        <BrandPageAds
          brandSlug={brand.brand_key || resolvedParams.slug}
          brandName={brand.name}
          locale={resolvedParams.locale}
        />

        {/* 相关品牌区域 */}
        {brand.related_brands && brand.related_brands.length > 0 && (
          <RelatedBrands
            relatedBrands={brand.related_brands}
            locale={resolvedParams.locale}
            t={t}
          />
        )}
      </main>

      <Footer />

      {/* 添加组织结构化数据 (暂时禁用) */}
      {/* 面包屑导航结构化数据 */}
      {/* <BreadcrumbSchema
        items={generateBreadcrumbItems(`/brands/${brand.brand_key}`, resolvedParams.locale, {
          home: tPage('breadcrumb.home'),
          brands: tPage('breadcrumb.brands')
        })}
      /> */}

      {/* 品牌组织结构化数据 */}
      {/* <OrganizationSchema
        {...generateBrandOrganizationSchema(brand, resolvedParams.locale)}
      /> */}
    </div>
  );
}
