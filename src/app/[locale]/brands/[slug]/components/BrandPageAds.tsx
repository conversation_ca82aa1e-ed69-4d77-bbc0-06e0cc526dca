import { AffiliateAdSection } from '../../../components/AffiliateAdSection';
import { PageContext } from '@/lib/types';
import { ProductMatcher } from '@/lib/productMatcher';

interface BrandPageAdsProps {
  brandSlug: string;
  brandName: string;
  locale: string;
  className?: string;
}

export async function BrandPageAds({ 
  brandSlug, 
  brandName, 
  locale, 
  className = '' 
}: BrandPageAdsProps) {
  // 创建页面上下文
  const pageContext: PageContext = {
    pageType: 'brand',
    brandSlug,
    locale
  };

  // 获取与品牌相关的产品
  const brandProducts = await ProductMatcher.getProductsForPage(pageContext, 4);

  // 如果没有相关产品，不显示广告区域
  if (brandProducts.length === 0) {
    return null;
  }

  return (
    <section className={`py-8 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {brandName} 相关产品推荐
          </h2>
          <p className="text-sm text-gray-600">
            为 {brandName} 拖拉机用户精选的配件和工具
          </p>
        </div>
        
        <AffiliateAdSection
          products={brandProducts}
          locale={locale}
          pageContext={pageContext}
          className="!py-0 !bg-transparent"
        />
      </div>
    </section>
  );
}