interface HistoryEvent {
  year: string;
  title: string;
  description: string;
}

interface HistoryTimelineProps {
  events: HistoryEvent[];
}

export function HistoryTimeline({ events }: HistoryTimelineProps) {
  return (
    <div className="relative border-l border-tractor/30 pl-6 ml-6 mt-8">
      {events.map((event, index) => (
        <div key={index} className="mb-10 relative">
          <div className="absolute w-4 h-4 bg-tractor rounded-full -left-8 top-1"></div>
          <div className="text-sm font-bold text-tractor mb-1">{event.year}</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">{event.title}</h3>
          <p className="text-gray-600">{event.description}</p>
        </div>
      ))}
    </div>
  );
} 