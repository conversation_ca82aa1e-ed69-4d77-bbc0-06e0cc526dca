import { HistoryTimeline } from './HistoryTimeline';

interface BrandHistoryProps {
  history: Array<{ year: string; title: string; description: string }>;
  brandName: string;
  t: any;
}

export function BrandHistory({ history, brandName, t }: BrandHistoryProps) {
  return (
    <section id="history" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('historySection.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{t('historySection.subtitle', { brandName })}</p>
        </div>
        
        <HistoryTimeline events={history} />
      </div>
    </section>
  );
} 