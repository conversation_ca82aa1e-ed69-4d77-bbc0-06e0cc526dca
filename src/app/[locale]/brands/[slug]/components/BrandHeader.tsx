import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface BrandHeaderProps {
  brand: any;
  locale: string;
  hasModels: boolean;
  t: any;
}

export function BrandHeader({ brand, locale, hasModels, t }: BrandHeaderProps) {
  return (
    <section className="bg-white border-b">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row items-start gap-8">
          {/* 品牌logo */}
          <div className="w-full md:w-1/4 flex justify-center items-center bg-gray-50 rounded-lg p-6 border">
            {brand.logo ? (
              <Image
                src={brand.logo}
                alt={`${brand.name} ${t('logo')}`}
                width={200}
                height={150}
                className="object-contain max-h-32"
              />
            ) : (
              <div className="w-20 h-20 bg-tractor/10 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-tractor">{brand.name.charAt(0)}</span>
              </div>
            )}
          </div>

          {/* 品牌信息 */}
          <div className="w-full md:w-3/4">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{brand.name}</h1>
            <p className="text-gray-700 mb-6 text-lg">{brand.description}</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              {brand.power_range && (
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-tractor/10 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-tractor">
                      <path d="m8 13-4 4 4 4"/>
                      <path d="M4 17h16"/>
                      <path d="M16 6 20 2l-4-4"/>
                      <path d="M20 6H4"/>
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">{t('powerRange')}</div>
                    <div className="font-medium">{brand.power_range}</div>
                  </div>
                </div>
              )}

              {brand.years_range && (
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-tractor/10 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-tractor">
                      <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                      <line x1="16" x2="16" y1="2" y2="6"/>
                      <line x1="8" x2="8" y1="2" y2="6"/>
                      <line x1="3" x2="21" y1="10" y2="10"/>
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">{t('productionYears')}</div>
                    <div className="font-medium">{brand.years_range}</div>
                  </div>
                </div>
              )}

              {brand.type && (
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-tractor/10 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-tractor">
                      <rect x="4" y="5" width="16" height="16" rx="2"/>
                      <rect x="9" y="3" width="6" height="4"/>
                      <path d="M10 11h4"/>
                      <path d="M12 9v4"/>
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">{t('tractorType')}</div>
                    <div className="font-medium">{brand.type}</div>
                  </div>
                </div>
              )}

              {brand.website && (
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-tractor/10 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-tractor">
                      <circle cx="12" cy="12" r="10"/>
                      <line x1="2" x2="22" y1="12" y2="12"/>
                      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                    </svg>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">{t('officialWebsite')}</div>
                    <a href={brand.website} target="_blank" rel="noopener noreferrer" className="font-medium text-tractor hover:underline">{t('visitWebsite')}</a>
                  </div>
                </div>
              )}
            </div>

            <div className="flex flex-wrap gap-3">
              {hasModels && (
                <Button asChild>
                  <Link href="#models" className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M7 19h10"/>
                      <path d="M7 13c0-3 2-5 5-5s5 2 5 5a6.97 6.97 0 0 1-2.522 5.376"/>
                      <path d="M12 19v-5"/>
                    </svg>
                    {t('viewModels')}
                  </Link>
                </Button>
              )}

              {brand.history && brand.history.length > 0 && (
                <Button variant="outline" asChild>
                  <Link href="#history" className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0"/>
                      <path d="M12 8v4l2 2"/>
                    </svg>
                    {t('brandHistory')}
                  </Link>
                </Button>
              )}

              {brand.website && (
                <Button variant="outline" asChild>
                  <a href={brand.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15 3 21 3 21 9"/>
                      <line x1="10" x2="21" y1="14" y2="3"/>
                    </svg>
                    {t('visitWebsite')}
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}