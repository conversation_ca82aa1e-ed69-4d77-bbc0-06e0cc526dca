'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Model } from '@/services/brandService'; // Import the Model interface

interface ModelsTableProps {
  models: Model[];
  locale: string;
  brandSlug: string;
}

export function ModelsTable({ models, locale, brandSlug }: ModelsTableProps) {
  const router = useRouter();

  const handleRowClick = (model: Model) => {
    console.log('Clicked row:', model.model_name)
    if (model.model_key) {
      router.push(`/${locale}/tractors/${brandSlug}/${model.model_key}`);
    }
  };

  return (
    <div className="hidden md:block overflow-hidden rounded-lg border border-gray-200 shadow-sm">
      <table className="w-full border-collapse bg-white text-left text-sm">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-4 font-medium text-gray-900 w-1/4">型号名称</th>
            {models.some((model) => model.years_range) && (
              <th scope="col" className="px-6 py-4 font-medium text-gray-900 w-1/6">年份</th>
            )}
            {models.some((model) => model.power) && (
              <th scope="col" className="px-6 py-4 font-medium text-gray-900 w-1/6">动力</th>
            )}
            {models.some((model) => model.series) && (
              <th scope="col" className="px-6 py-4 font-medium text-gray-900 w-1/6">系列</th>
            )}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100 border-t border-gray-100">
          {models.map((model, index) => (
            <tr
              key={index}
              className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${model.model_key ? 'hover:bg-tractor/5 transition-colors cursor-pointer' : ''}`}
              onClick={() => handleRowClick(model)}
            >
              <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
                {model.model_name ? (
                  <Link
                    href={locale === 'en' ? `/tractors/${brandSlug}/${model.model_key}` : `/${locale}/tractors/${brandSlug}/${model.model_key}`}
                    className="hover:text-tractor"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {model.model_name}
                  </Link>
                ) : (
                  model.model_name
                )}
              </td>
              {models.some((m) => m.years_range) && (
                <td className="px-6 py-4 text-gray-700">{model.years_range || '-'}</td>
              )}
              {models.some((m) => m.power) && (
                <td className="px-6 py-4 text-gray-700">{model.power || '-'}</td>
              )}
              {models.some((m) => m.series) && (
                <td className="px-6 py-4 text-gray-700">{model.series || '-'}</td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 