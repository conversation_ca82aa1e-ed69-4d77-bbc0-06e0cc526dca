import Link from 'next/link';
import { ModelsTable } from './ModelsTable';
import { type Model } from '@/services/brandService';

interface BrandModelsProps {
  models: Model[];
  brand: any;
  locale: string;
  t: any;
}

export function BrandModels({ models, brand, locale, t }: BrandModelsProps) {
  return (
    <section id="models" className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('modelSection.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{t('modelSection.subtitle', { brandName: brand.name })}</p>
        </div>
        
        {/* 使用新的客户端组件渲染桌面端表格 */}
        <ModelsTable models={models} locale={locale} brandSlug={brand.slug} />
        
        {/* 移动端卡片列表 - 中等屏幕以下显示 */}
        <div className="grid grid-cols-1 gap-4 md:hidden">
          {models.map((model: Model, index: number) => (
            <div key={index} className={`bg-white rounded-lg border border-gray-200 shadow-sm ${model.model_key ? 'hover:shadow-md transition-shadow' : ''}`}>
              {model.model_key ? (
                <Link
                  href={locale === 'en' ? `/tractors/${brand.slug}/${model.model_key}` : `/${locale}/tractors/${brand.slug}/${model.model_key}`}
                  className="block p-4 hover:bg-tractor/5 transition-colors"
                >
                  <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-tractor">{model.model_name}</h3>
                  <div className="space-y-2 text-sm">
                    {model.years_range && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.years')}</span>
                        <span className="font-medium">{model.years_range}</span>
                      </div>
                    )}
                    {model.power && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.power')}</span>
                        <span className="font-medium">{model.power}</span>
                      </div>
                    )}
                    {model.series && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.series')}</span>
                        <span className="font-medium">{model.series}</span>
                      </div>
                    )}
                  </div>
                </Link>
              ) : (
                <div className="p-4">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{model.model_name}</h3>
                  <div className="space-y-2 text-sm">
                    {model.years_range && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.years')}</span>
                        <span className="font-medium">{model.years_range}</span>
                      </div>
                    )}
                    {model.power && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.power')}</span>
                        <span className="font-medium">{model.power}</span>
                      </div>
                    )}
                    {model.series && (
                      <div className="flex justify-between items-center">
                        <span className="text-gray-500">{t('modelSection.card.series')}</span>
                        <span className="font-medium">{model.series}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 