import Link from 'next/link';
import { createUnderscoreSlug } from '@/lib/utils';

interface RelatedBrandsProps {
  relatedBrands: Array<{ brand_key: string; brand_name: string } | string>;
  locale: string;
  t: any;
}

export function RelatedBrands({ relatedBrands, locale, t }: RelatedBrandsProps) {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('relatedBrandsSection.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">{t('relatedBrandsSection.subtitle')}</p>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {relatedBrands.map((relatedBrand, index) => {
            // 处理品牌键值和名称
            const isObject = typeof relatedBrand === 'object';
            const brandKey = isObject
              ? relatedBrand.brand_key
              : createUnderscoreSlug(relatedBrand); // 字符串时转换为小写下划线格式
            const brandName = isObject
              ? relatedBrand.brand_name
              : relatedBrand; // 保持原始显示名称

            // 生成正确的链接
            const href = locale === 'en' ? `/brands/${brandKey}` : `/${locale}/brands/${brandKey}`;

            return (
              <Link
                key={index}
                href={href}
                className="bg-gray-50 hover:bg-tractor/5 border border-gray-200 rounded-lg p-4 text-center transition-colors flex items-center justify-center h-16"
              >
                <span className="font-medium">{brandName}</span>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
} 