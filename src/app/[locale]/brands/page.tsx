import Link from 'next/link';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Header } from '../components/Header';
import { Footer } from '../components/Footer';
import { Suspense } from 'react';
import { getAllBrands } from '@/services/brandService';
import { getTranslations } from 'next-intl/server';
import { SearchForm, BrandLegend, BrandsResults } from './components';
import { getAllLocaleParams } from '@/lib/cache';

// 静态生成配置
export const revalidate = 3600; // 1小时重新验证

// 生成所有语言版本的静态参数
export async function generateStaticParams() {
  return getAllLocaleParams();
}

// 主页面组件
export default async function BrandsPage({
  searchParams,
  params,
}: {
  searchParams?: {
    search?: string;
    type?: string;
    page?: string;
  };
  params: {
    locale: string;
  };
}) {
  // 获取翻译内容
  const t = await getTranslations('BrandsPage');

  // 确保 searchParams 和 params 已被解析 - 使用 Promise.resolve 来处理
  const resolvedParams = await Promise.resolve(params);
  const resolvedSearchParams = await Promise.resolve(searchParams);
  
  // 获取查询参数
  const search = resolvedSearchParams?.search || '';
  const activeTab = resolvedSearchParams?.type || 'all';
  const page = Number(resolvedSearchParams?.page) || 1;
  const itemsPerPage = 12; // 增加每页显示的品牌数量以提高用户体验
  
  // 从MongoDB获取品牌数据（带分页）
  const { data: currentBrands, pagination } = await getAllBrands(
    activeTab, 
    search,
    resolvedParams.locale,
    page,
    itemsPerPage
  );
  
  // 构建基础URL（用于分页）
  const baseUrl = `?${activeTab !== 'all' ? `type=${activeTab}&` : ''}${search ? `search=${encodeURIComponent(search)}&` : ''}`;
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main>
        {/* 页面标题区 */}
        <section className="bg-gradient-to-r from-tractor to-tractor/80 text-white">
          <div className="container mx-auto px-4 py-12">
            <h1 className="text-4xl font-bold mb-4 text-center">
              {t('title')}
            </h1>
            <p className="text-center max-w-3xl mx-auto opacity-90">
              {t('subtitle')}
            </p>
          </div>
        </section>
        
        {/* 品牌展示区 */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Tabs defaultValue={activeTab} className="w-full">
              <div className="flex justify-center mb-8">
                <TabsList>
                  <TabsTrigger value="all" asChild>
                    <Link href={search ? `?search=${encodeURIComponent(search)}` : '?'}>{t('allBrands')}</Link>
                  </TabsTrigger>
                  <TabsTrigger value="farm" asChild>
                    <Link href={search ? `?type=farm&search=${encodeURIComponent(search)}` : '?type=farm'}>{t('farmTractors')}</Link>
                  </TabsTrigger>
                  <TabsTrigger value="lawn" asChild>
                    <Link href={search ? `?type=lawn&search=${encodeURIComponent(search)}` : '?type=lawn'}>{t('lawnTractorBrands')}</Link>
                  </TabsTrigger>
                </TabsList>
              </div>
              
              {/* 搜索表单 */}
              <SearchForm defaultValue={search} currentType={activeTab} />
              
              <TabsContent value={activeTab} forceMount>
                <h2 className="text-2xl font-semibold mb-6 text-center">
                  {activeTab === 'farm' 
                    ? t('farmTractors') 
                    : activeTab === 'lawn' 
                      ? t('lawnTractorBrands') 
                      : t('allBrands')}
                </h2>
                
                <Suspense fallback={<div className="text-center py-10">{t('loading')}</div>}>
                  <BrandsResults 
                    brands={currentBrands}
                    pagination={pagination}
                    baseUrl={baseUrl}
                    search={search}
                    activeTab={activeTab}
                  />
                </Suspense>
              </TabsContent>
            </Tabs>
            
            {/* 添加字段说明 */}
            <BrandLegend />
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
} 