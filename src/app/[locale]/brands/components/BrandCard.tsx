import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { type Brand } from '@/services/brandService';


export function BrandCard({ brand }: { brand: Brand }) {
  const t = useTranslations('BrandsPage');

  // 生成品牌的首字母或第一个汉字
  const brandInitial = brand.brand_name ? brand.brand_name.charAt(0).toUpperCase() : 'B';
  
  // 使用统一主题色
  const bgColor = 'bg-tractor/10';
  const textColor = 'text-tractor';
  
  return (
    <Link href={`/brands/${brand.brand_key || brand.slug}`} className="group">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-300 h-full flex flex-col">
        <div className="p-5 flex-1">
          {/* 品牌标识 - 优先使用logo，没有时使用文字图标 */}
          {brand.brand_logo ? (
            <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <img 
                src={brand.brand_logo} 
                alt={`${brand.brand_name || t('title')} logo`} 
                className="max-w-full max-h-full object-contain"
              />
            </div>
          ) : (
            <div className={`w-16 h-16 ${bgColor} flex items-center justify-center rounded-full mb-4 mx-auto`}>
              <span className={`text-2xl font-bold ${textColor}`}>{brandInitial}</span>
            </div>
          )}
          
          {/* 品牌名称 */}
          <h3 className="text-lg font-medium text-center text-gray-800 group-hover:text-tractor mb-3">{brand.brand_name}</h3>
          
          {/* 品牌类型标签 */}
          <div className="flex justify-center mb-3">
            <span className="text-xs px-2 py-1 rounded-full bg-tractor/10 text-tractor">
              {brand.tractor_type}
            </span>
          </div>
          
          {/* 分隔线 */}
          <div className="border-t border-gray-100 my-3"></div>
          
          {/* 品牌描述 */}
          {brand.brand_introduction && (
            <p className="text-sm text-gray-600 line-clamp-2 mb-4">{brand.brand_introduction}</p>
          )}
          
          {/* 展示数据字段 */}
          <div className="grid grid-cols-1 gap-2 text-sm">
            {brand.power_range && (
              <div className="flex items-center text-gray-600">
                <img src="/icons/power.svg" alt={t('fieldDescriptions.powerRange')} className="h-4 w-4 mr-2 text-tractor" />
                <span>{t('brandCard.powerRange', { range: brand.power_range })}</span>
              </div>
            )}
            
            {brand.years_range && (
              <div className="flex items-center text-gray-600">
                <img src="/icons/calendar.svg" alt={t('fieldDescriptions.yearsRange')} className="h-4 w-4 mr-2 text-tractor" />
                <span>{t('brandCard.yearsRange', { range: brand.years_range })}</span>
              </div>
            )}
            
            {brand.tractor_count && (
              <div className="flex items-center text-gray-600">
                <img src="/icons/tractor-count.svg" alt={t('fieldDescriptions.tractorCount')} className="h-4 w-4 mr-2 text-tractor" />
                <span>{t('brandCard.tractorCount', { count: brand.tractor_count })}</span>
              </div>
            )}
            
            {brand.brand_website && (
              <div className="flex items-center text-gray-600">
                <img src="/icons/website.svg" alt="website icon" className="h-4 w-4 mr-2 text-tractor" />
                <span>{t('brandCard.website', { url: brand.brand_website })}</span>
              </div>
            )}
          </div>
        </div>
        
        {/* 底部按钮 */}
        <div className="p-3 bg-gray-50 border-t border-gray-200 text-sm text-center text-tractor font-medium group-hover:bg-tractor/5 transition-colors">
          {t('brandCard.viewDetails')}
          <img src="/icons/arrow-right.svg" alt="arrow icon" className="h-4 w-4 inline-block ml-1" />
        </div>
      </div>
    </Link>
  );
} 