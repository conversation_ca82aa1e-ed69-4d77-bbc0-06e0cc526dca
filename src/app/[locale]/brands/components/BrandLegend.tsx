import { useTranslations } from 'next-intl';

export function BrandLegend() {
  const t = useTranslations('BrandsPage');
  
  return (
    <div className="mt-8 bg-gray-50 p-4 rounded-lg border border-gray-200">
      <h3 className="text-lg font-medium mb-2">{t('fieldLegend')}</h3>
      <div className="grid gap-2 grid-cols-1 md:grid-cols-3 text-sm">
        <div className="flex items-center">
          <img src="/icons/power.svg" alt={t('fieldDescriptions.powerRange')} className="h-4 w-4 mr-1 text-tractor" />
          <span>{t('fieldDescriptions.powerRange')}</span>
        </div>
        <div className="flex items-center">
          <img src="/icons/calendar.svg" alt={t('fieldDescriptions.yearsRange')} className="h-4 w-4 mr-1 text-tractor" />
          <span>{t('fieldDescriptions.yearsRange')}</span>
        </div>
        <div className="flex items-center">
          <img src="/icons/tractor-count.svg" alt={t('fieldDescriptions.tractorCount')} className="h-4 w-4 mr-1 text-tractor" />
          <span>{t('fieldDescriptions.tractorCount')}</span>
        </div>
      </div>
    </div>
  );
} 