import { Input } from '@/components/ui/input';
import { useTranslations } from 'next-intl';

export function SearchForm({ 
  defaultValue = "", 
  currentType = "all" 
}: { 
  defaultValue?: string;
  currentType?: string;
}) {
  const t = useTranslations('BrandsPage');

  return (
    <form action="" method="get" className="py-4 mb-6">
      <div className="w-full max-w-xl mx-auto">
        <div className="flex items-center gap-2">
          <Input 
            type="search" 
            name="search" 
            placeholder={t('searchPlaceholder')} 
            defaultValue={defaultValue}
            className="flex-1"
          />
          {/* 隐藏当前类型以维持过滤器状态 */}
          {currentType !== "all" && (
            <input type="hidden" name="type" value={currentType} />
          )}
          <button 
            type="submit" 
            className="bg-tractor hover:bg-tractor-dark text-white px-4 py-2 rounded-md transition duration-200"
          >
            {t('searchButton')}
          </button>
        </div>
      </div>
    </form>
  );
} 