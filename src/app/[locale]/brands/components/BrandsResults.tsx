import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { SimplePaginationControls } from '@/components/ui/pagination-controls';
import { type Brand } from '@/services/brandService';
import { BrandCard } from './BrandCard';

interface BrandsResultsProps {
  brands: Brand[];
  pagination: {
    page: number;
    pages: number;
    limit: number;
    total: number;
  };
  baseUrl: string;
  search: string;
  activeTab: string;
}

export function BrandsResults({ brands, pagination, baseUrl, search, activeTab }: BrandsResultsProps) {
  const t = useTranslations('BrandsPage');

  if (brands.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">{t('noResults')}</p>
        {search && (
          <Link 
            href={activeTab !== 'all' ? `?type=${activeTab}` : '/'}
            className="text-tractor hover:underline mt-2 inline-block"
          >
            {t('clearSearch')}
          </Link>
        )}
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {brands.map((brand: Brand) => (
          <BrandCard key={`${brand.tractor_type}-${brand.slug || brand._id}`} brand={brand} />
        ))}
      </div>
      
      <SimplePaginationControls 
        currentPage={pagination.page}
        totalPages={pagination.pages}
        baseUrl={baseUrl}
        previousText={t('pagination.previous')}
        nextText={t('pagination.next')}
      />
      
      {/* 显示总数量信息 */}
      <div className="text-center text-sm text-gray-500 mt-4">
        {t('showingResults', {
          start: (pagination.page - 1) * pagination.limit + 1, 
          end: Math.min(pagination.page * pagination.limit, pagination.total),
          total: pagination.total
        })}
      </div>
    </>
  );
} 