import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { Header } from "../components/Header";
import { Footer } from "../components/Footer";
import ShowsHero from "./components/ShowsHero";
import ShowsList from "./components/ShowsList";
import TabSwitcher from "./components/TabSwitcher";
import showsData from './shows.json';
import museumsData from './museums.json';
import './components/tabStyles.css';

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  // 先解析params以避免"params should be awaited"错误
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'ShowsPage.metadata' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
  };
}

export default async function Shows({ params }: { params: { locale: string } }) {
  // 确保params已解析
  const resolvedParams = await Promise.resolve(params);
  
  // 获取翻译文本传递给客户端组件
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'ShowsPage.tabs' });
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main>
        <ShowsHero />

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 客户端Tab切换器，仅用于控制UI显示/隐藏 */}
            <TabSwitcher labels={{ 
              shows: t('shows'),
              museums: t('museums')
            }} />
            
            {/* 预渲染所有内容，初始状态时museums是隐藏的 */}
            <div id="shows-content" className="tab-content active">
              <ShowsList shows={showsData} />
            </div>
            
            <div id="museums-content" className="tab-content">
              <ShowsList shows={museumsData} />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
