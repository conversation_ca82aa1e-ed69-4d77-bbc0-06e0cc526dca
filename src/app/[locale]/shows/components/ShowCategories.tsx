import Link from "next/link";

export default function ShowCategories() {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 sticky top-20">
      <h2 className="text-xl font-bold text-gray-900 mb-4">查看方式</h2>
      
      <div className="space-y-4">
        <CategoryLink 
          href="/shows/farm-tractor-shows-by-date" 
          text="按日期 (Date)"
          icon={<CalendarIcon />}
        />
        <CategoryLink 
          href="/shows/farm-tractor-shows-by-location" 
          text="按地点 (Location)"
          icon={<LocationIcon />}
        />
        <CategoryLink 
          href="/shows/farm-tractor-shows-by-feature" 
          text="按特色 (Feature)"
          icon={<FeatureIcon />}
        />
      </div>
      
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">热门地点 (Popular Locations)</h3>
        <ul className="space-y-2">
          <li>
            <Link href="/shows/location/minnesota" className="text-gray-600 hover:text-tractor hover:underline">Minnesota</Link>
          </li>
          <li>
            <Link href="/shows/location/wisconsin" className="text-gray-600 hover:text-tractor hover:underline">Wisconsin</Link>
          </li>
          <li>
            <Link href="/shows/location/illinois" className="text-gray-600 hover:text-tractor hover:underline">Illinois</Link>
          </li>
          <li>
            <Link href="/shows/location/kansas" className="text-gray-600 hover:text-tractor hover:underline">Kansas</Link>
          </li>
          <li>
            <Link href="/shows/farm-tractor-shows-by-location" className="text-tractor font-medium hover:underline">查看所有地点...</Link>
          </li>
        </ul>
      </div>
      
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-3">近期展览 (Upcoming Shows)</h3>
        <ul className="space-y-2">
          <li>
            <Link href="/shows/pope-ffa-antique-tractor-show" className="text-gray-600 hover:text-tractor hover:underline">Pope FFA Antique Tractor Show (4月12日)</Link>
          </li>
          <li>
            <Link href="/shows/pioneer-power-swap-meet" className="text-gray-600 hover:text-tractor hover:underline">Pioneer Power Swap Meet (4月25-27日)</Link>
          </li>
          <li>
            <Link href="/shows/gmtcc-field-days" className="text-gray-600 hover:text-tractor hover:underline">GMTCC Field Days (5月2-3日)</Link>
          </li>
          <li>
            <Link href="/shows/farm-tractor-shows-by-date" className="text-tractor font-medium hover:underline">查看完整日历...</Link>
          </li>
        </ul>
      </div>
    </div>
  );
}

function CategoryLink({ href, text, icon }: { href: string; text: string; icon: React.ReactNode }) {
  return (
    <Link 
      href={href}
      className="flex items-center p-3 rounded-md hover:bg-gray-50 transition-colors group"
    >
      <span className="text-tractor mr-3">{icon}</span>
      <span className="text-gray-700 group-hover:text-tractor font-medium">{text}</span>
    </Link>
  );
}

function CalendarIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.8333 3.33333H4.16667C3.24619 3.33333 2.5 4.07952 2.5 5V16.6667C2.5 17.5871 3.24619 18.3333 4.16667 18.3333H15.8333C16.7538 18.3333 17.5 17.5871 17.5 16.6667V5C17.5 4.07952 16.7538 3.33333 15.8333 3.33333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M13.3333 1.66667V5.00001" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M6.66669 1.66667V5.00001" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M2.5 8.33333H17.5" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M6.66669 11.6667H6.67502" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M10 11.6667H10.0083" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M13.3333 11.6667H13.3417" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M6.66669 15H6.67502" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M10 15H10.0083" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M13.3333 15H13.3417" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
}

function LocationIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 10.8333C11.3807 10.8333 12.5 9.71404 12.5 8.33333C12.5 6.95262 11.3807 5.83333 10 5.83333C8.61929 5.83333 7.5 6.95262 7.5 8.33333C7.5 9.71404 8.61929 10.8333 10 10.8333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M10.0001 18.3333C13.3334 15 16.6667 12.0152 16.6667 8.33333C16.6667 4.65144 13.6821 1.66667 10.0001 1.66667C6.31818 1.66667 3.33342 4.65144 3.33342 8.33333C3.33342 12.0152 6.66675 15 10.0001 18.3333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
}

function FeatureIcon() {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M19.1667 10.8333H15.8333V14.1667H19.1667V10.8333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M9.99992 3.33333H6.66658V6.66667H9.99992V3.33333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M9.99992 13.3333H6.66658V16.6667H9.99992V13.3333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M15.8333 3.33333C17.2141 3.33333 18.3333 4.45262 18.3333 5.83333C18.3333 7.21405 17.2141 8.33333 15.8333 8.33333C14.4526 8.33333 13.3333 7.21405 13.3333 5.83333C13.3333 4.45262 14.4526 3.33333 15.8333 3.33333Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M4.16667 10C5.54738 10 6.66667 11.1193 6.66667 12.5C6.66667 13.8807 5.54738 15 4.16667 15C2.78596 15 1.66667 13.8807 1.66667 12.5C1.66667 11.1193 2.78596 10 4.16667 10Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path d="M4.16667 1.66667C5.54738 1.66667 6.66667 2.78596 6.66667 4.16667C6.66667 5.54738 5.54738 6.66667 4.16667 6.66667C2.78596 6.66667 1.66667 5.54738 1.66667 4.16667C1.66667 2.78596 2.78596 1.66667 4.16667 1.66667Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
}
