import Image from 'next/image';
import { useTranslations } from 'next-intl';

type Show = {
  name: string;
  date_location?: string;
  Organization?: string;
  Event?: string;
  Date?: string;
  Hours?: string;
  Location?: string;
  "Web site"?: string;
  Notes?: string;
  Featuring?: string;
  Admission?: string;
};

type ShowsListProps = {
  shows: Show[];
};

export default function ShowsList({ shows }: ShowsListProps) {
  return (
    <div>
      <div className="space-y-8 mb-10">
        {shows.map((show, index) => (
          <ShowListItem key={index} show={show} />
        ))}
      </div>
    </div>
  );
}

function ShowListItem({ show }: { show: Show }) {
  const t = useTranslations('ShowsPage.details');
  return (
    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
      <div className="flex flex-col gap-2">
        <div className="flex flex-col md:flex-row md:justify-between md:items-baseline">
          <h3 className="text-lg md:text-xl font-medium text-tractor">
            {show.name}
          </h3>
          <div className="text-sm text-gray-500 mt-1 md:mt-0 md:ml-4 flex items-center gap-1">
            <Image src="/icons/location.svg" width={16} height={16} alt={t('location')} className="inline" unoptimized />
            {show.Location || (show.date_location ? show.date_location.split(' in ').pop() : '')}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
          {show.Date && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/calendar.svg" width={20} height={20} alt={t('date')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('date')}</div>
                <div>{show.Date}</div>
              </div>
            </div>
          )}
          
          {show.date_location && !show.Date && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/calendar.svg" width={20} height={20} alt={t('date')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('dateAndLocation')}</div>
                <div>{show.date_location}</div>
              </div>
            </div>
          )}

          {show.Organization && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/feature.svg" width={20} height={20} alt={t('organization')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('organization')}</div>
                <div>{show.Organization}</div>
              </div>
            </div>
          )}

          {show.Event && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/feature.svg" width={20} height={20} alt={t('event')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('event')}</div>
                <div>{show.Event}</div>
              </div>
            </div>
          )}

          {show.Featuring && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/feature.svg" width={20} height={20} alt={t('featuring')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('featuring')}</div>
                <div>{show.Featuring}</div>
              </div>
            </div>
          )}

          {show.Hours && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/calendar.svg" width={20} height={20} alt={t('hours')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('hours')}</div>
                <div>{show.Hours}</div>
              </div>
            </div>
          )}

          {show.Admission && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/feature.svg" width={20} height={20} alt={t('admission')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('admission')}</div>
                <div>{show.Admission}</div>
              </div>
            </div>
          )}

          {show["Web site"] && (
            <div className="flex items-start gap-2">
              <span className="text-tractor mt-0.5">
                <Image src="/icons/feature.svg" width={20} height={20} alt={t('website')} className="inline" />
              </span>
              <div>
                <div className="font-medium text-gray-700">{t('website')}</div>
                <a href={show["Web site"]} target="_blank" rel="noopener noreferrer" className="text-tractor hover:underline">
                  {show["Web site"]}
                </a>
              </div>
            </div>
          )}
        </div>

        {show.Notes && (
          <div className="mt-3 border-t border-gray-100 pt-3">
            <div className="text-sm text-gray-700">{show.Notes}</div>
          </div>
        )}
      </div>
    </div>
  );
}
