'use client';

import { useEffect } from 'react';
import './tabStyles.css';

type TabSwitcherProps = {
  labels: {
    shows: string;
    museums: string;
  };
};

export default function TabSwitcher({ labels }: TabSwitcherProps) {
  // 客户端初始化，处理选项卡的点击事件
  useEffect(() => {
    const showsTab = document.getElementById('shows-tab');
    const museumsTab = document.getElementById('museums-tab');
    const showsContent = document.getElementById('shows-content');
    const museumsContent = document.getElementById('museums-content');

    // 确保初始状态只有一个标签被激活
    const initializeTabs = () => {
      // 默认显示展览和活动标签
      showsTab?.classList.add('active');
      museumsTab?.classList.remove('active');
      
      if (showsContent && museumsContent) {
        showsContent.classList.add('active');
        museumsContent.classList.remove('active');
      }
    };

    const switchToShows = () => {
      showsTab?.classList.add('active');
      museumsTab?.classList.remove('active');
      showsContent?.classList.add('active');
      museumsContent?.classList.remove('active');
    };

    const switchToMuseums = () => {
      museumsTab?.classList.add('active');
      showsTab?.classList.remove('active');
      museumsContent?.classList.add('active');
      showsContent?.classList.remove('active');
    };

    // 初始化标签状态
    initializeTabs();

    showsTab?.addEventListener('click', switchToShows);
    museumsTab?.addEventListener('click', switchToMuseums);

    // 清理事件监听器
    return () => {
      showsTab?.removeEventListener('click', switchToShows);
      museumsTab?.removeEventListener('click', switchToMuseums);
    };
  }, []);

  return (
    <div className="mb-8 border-b border-gray-200">
      <div className="flex space-x-4">
        <button
          id="shows-tab"
          className="py-3 px-4 text-center border-b-2 font-medium text-sm transition-colors border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 tab-button"
        >
          {labels.shows}
        </button>
        <button
          id="museums-tab"
          className="py-3 px-4 text-center border-b-2 font-medium text-sm transition-colors border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 tab-button"
        >
          {labels.museums}
        </button>
      </div>
    </div>
  );
} 