"use client";

import { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { TractorComparisonTable } from './TractorComparisonTable';
import { TractorSelector } from '@/app/[locale]/compare/components/TractorSelector';
import { Brand, TractorDetails } from '@/services/compareService';
import { useTranslations } from 'next-intl';

interface TractorComparisonProps {
  initialBrands: Brand[];
}

export function TractorComparison({ initialBrands }: TractorComparisonProps) {
  const t = useTranslations('ComparePage.comparison');
  
  const [selectedTractors, setSelectedTractors] = useState<TractorDetails[]>([]);
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [tractorsToCompare, setTractorsToCompare] = useState<TractorDetails[]>([]);
  
  // 处理拖拉机选择
  const handleTractorSelected = useCallback(async (index: number, modelId: string) => {
    if (!modelId) return;
    
    try {
      // 使用客户端API获取拖拉机详情
      const response = await fetch(`/api/models/${modelId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch tractor details');
      }
      const tractorDetails = await response.json();
      
      if (tractorDetails) {
        setSelectedTractors(prev => {
          const newTractors = [...prev];
          newTractors[index] = tractorDetails;
          return newTractors;
        });
      }
    } catch (error) {
      console.error('Error fetching tractor details:', error);
    }
  }, []);
  
  const handleCompare = () => {
    const filteredTractors = selectedTractors.filter(tractor => tractor);
    if (filteredTractors.length >= 2) {
      setTractorsToCompare(filteredTractors);
      setShowComparison(true);
      
      // 平滑滚动到结果区域
      setTimeout(() => {
        document.getElementById('comparison-results')?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  };
  
  const hasTractorsSelected = selectedTractors.filter(t => t).length >= 2;
  
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <TractorSelector 
          index={0} 
          brands={initialBrands}
          onTractorSelected={handleTractorSelected} 
        />
        <TractorSelector 
          index={1} 
          brands={initialBrands}
          onTractorSelected={handleTractorSelected} 
        />
        <TractorSelector 
          index={2} 
          brands={initialBrands}
          onTractorSelected={handleTractorSelected} 
        />
      </div>
      
      <div className="flex justify-center">
        <button
          onClick={handleCompare}
          disabled={!hasTractorsSelected}
          className={`px-8 py-3 rounded-md font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 ${
            hasTractorsSelected
              ? 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {hasTractorsSelected ? t('compareButton') : t('selectAtLeast')}
        </button>
      </div>
      
      {showComparison && (
        <motion.div 
          id="comparison-results"
          className="mt-12"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <TractorComparisonTable 
            tractors={tractorsToCompare} 
          />
        </motion.div>
      )}
    </div>
  );
}