"use client"

import Image from 'next/image';
import {TractorDetails} from '@/services/compareService';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from "@/components/ui/table";
import { useTranslations } from 'next-intl';

interface TractorComparisonTableProps {
  tractors: TractorDetails[];
}

export function TractorComparisonTable({ tractors }: TractorComparisonTableProps) {
  const t = useTranslations('ComparePage.table');
  
  if (!tractors || tractors.length === 0) {
    return null;
  }

  // Safely get value, avoid rendering errors due to undefined or null
  const getSafeValue = (value: any) => {
    return value || t('noData');
  };

  // Get the first photo URL or return default image
  const getFirstPhotoUrl = (photos?: {url: string; title: string}[]) => {
    if (photos && photos.length > 0 && photos[0].url) {
      return photos[0].url;
    }
    return 'https://picsum.photos/300/200?grayscale'; // Default image
  };

  return (
    <div className="w-full rounded-lg shadow-md overflow-hidden border border-gray-200">

      
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-100 border-b border-gray-200">
              <TableHead className="w-[180px] sticky left-0 bg-gray-100 z-20 font-semibold text-gray-700">{t('attribute')}</TableHead>
              {tractors.map((tractor, index) => (
                <TableHead key={`header-${tractor._id}-${index}`} className="text-center font-semibold text-gray-700">
                  {t('tractor', { number: index + 1 })}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {/* 图片行 */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('tractorImage')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`image-${tractor._id}-${index}`} className="text-center">
                  {tractor.photos && tractor.photos.length > 0 ? (
                    <div className="flex justify-center">
                      <Image 
                        src={getFirstPhotoUrl(tractor.photos)} 
                        alt={tractor.photos[0].title || tractor.model_name || '拖拉机图片'} 
                        width={150}
                        height={100}
                        className="object-cover rounded-md"
                      />
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm">{t('noImage')}</div>
                  )}
                </TableCell>
              ))}
            </TableRow>
            
            {/* 拖拉机品牌行 - tractor_brand */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('tractorBrand')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`tractor-brand-${tractor._id}-${index}`} className="text-center">
                  {getSafeValue(tractor.tractor_brand)}
                </TableCell>
              ))}
            </TableRow>
            
            {/* 拖拉机型号行 - model_name */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('modelName')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`model-name-${tractor._id}-${index}`} className="text-center">
                  {getSafeValue(tractor.model_name)}
                </TableCell>
              ))}
            </TableRow>
            
            {/* 年份范围行 - years_range */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('yearsRange')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`year-range-${tractor._id}-${index}`} className="text-center">
                  {getSafeValue(tractor.years_range)}
                </TableCell>
              ))}
            </TableRow>
            
            {/* 拖拉机类型行 - tractor_type */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('tractorType')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`tractor-type-${tractor._id}-${index}`} className="text-center">
                  {getSafeValue(tractor.tractor_type)}
                </TableCell>
              ))}
            </TableRow>

            {/* 功率行 - power */}
            <TableRow>
              <TableCell className="font-medium sticky left-0 bg-background z-20">{t('power')}</TableCell>
              {tractors.map((tractor, index) => (
                <TableCell key={`power-${tractor._id}-${index}`} className="text-center">
                  {getSafeValue(tractor.power)}
                </TableCell>
              ))}
            </TableRow>
            

          </TableBody>
        </Table>
      </div>
      
      {/* 移动端提示 */}
      <div className="p-4 bg-blue-50 border-t border-blue-100 md:hidden">
        <p className="text-xs text-blue-600 text-center">
          {t('mobileHint')}
        </p>
      </div>
    </div>
  );
}