"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Brand, Model } from '@/services/compareService';
import { useTranslations } from 'next-intl';

interface TractorSelectorProps {
  index: number;
  brands: Brand[];
  onTractorSelected: (index: number, modelId: string) => void;
}

export function TractorSelector({ index, brands, onTractorSelected }: TractorSelectorProps) {
  const t = useTranslations('ComparePage.comparison');
  
  const [selectedBrandKey, setSelectedBrandKey] = useState<string>('');
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);

  // 当品牌改变时，获取可用的型号
  useEffect(() => {
    async function fetchModels() {
      if (!selectedBrandKey) {
        setAvailableModels([]);
        return;
      }

      setIsLoading(true);
      try {
        // 使用客户端API获取模型数据
        const response = await fetch(`/api/models?brandKey=${selectedBrandKey}`);
        if (!response.ok) {
          throw new Error('Failed to fetch models');
        }
        const models = await response.json();
        setAvailableModels(models);
        
        // 如果有可用型号，自动选择第一个
        if (models && models.length > 0) {
          setSelectedModelId(models[0]._id);
        }
      } catch (error) {
        console.error(`Error fetching models for brand ${selectedBrandKey}:`, error);
        setAvailableModels([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchModels();
    setSelectedModelId('');
    setIsComplete(false);
  }, [selectedBrandKey]);

  // 处理选择完成
  useEffect(() => {
    const complete = selectedBrandKey !== '' && selectedModelId !== '';
    setIsComplete(complete);

    if (complete) {
      onTractorSelected(index, selectedModelId);
    }
  }, [selectedBrandKey, selectedModelId, index, onTractorSelected]);

  return (
    <motion.div 
      className="bg-gray-50 rounded-lg p-6 border border-gray-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
    >
      <h2 className="text-xl font-semibold mb-4">{t('tractor', { number: index + 1 })}</h2>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('selectBrand')}
          </label>
          <select 
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            value={selectedBrandKey}
            onChange={(e) => setSelectedBrandKey(e.target.value)}
          >
            <option value="">{t('selectBrand')}</option>
            {brands.map(brand => (
              <option key={brand._id} value={brand.brand_key}>
                {brand.brand_name}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('selectModel')}
          </label>
          <select 
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            disabled={!selectedBrandKey || isLoading}
            value={selectedModelId}
            onChange={(e) => setSelectedModelId(e.target.value)}
          >
            <option value="">
              {isLoading 
                ? t('loading')
                : !selectedBrandKey 
                  ? t('pleaseSelectBrand')
                  : t('selectModel')}
            </option>
            {availableModels.map(model => (
              <option key={model._id} value={model._id}>
                {model.model_name} ({model.power})
              </option>
            ))}
          </select>
          {isLoading && (
            <div className="mt-2 text-sm text-gray-500 flex items-center">
              <svg className="animate-spin h-4 w-4 mr-2 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('loadingModels')}
            </div>
          )}
        </div>

        {isComplete && (
          <motion.div 
            className="mt-2 text-green-600 text-sm flex items-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {t('selectionComplete')}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
