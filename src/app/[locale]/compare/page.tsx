import { Header } from "../components/Header";
import { Footer } from "../components/Footer";
import { Metadata } from "next";
import { TractorComparison } from "./components/TractorComparison";
import { getAllBrands } from "@/services/compareService";
import { getTranslations, getLocale } from 'next-intl/server';
import { getAllLocaleParams } from '@/lib/cache';

// 静态生成配置
export const revalidate = 3600; // 1小时重新验证

// 生成所有语言版本的静态参数
export async function generateStaticParams() {
  return getAllLocaleParams();
}

export async function generateMetadata(): Promise<Metadata> {
  // Get the current locale
  const locale = await getLocale();
  
  const t = await getTranslations({ locale, namespace: 'ComparePage.metadata' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
  };
}

export default async function ComparePage() {
  // Get the current locale
  const locale = await getLocale();
  
  // Get translations for the page
  const t = await getTranslations({ locale, namespace: 'ComparePage' });
  
  // Load brand data directly from the database on the server side
  // This is more efficient than fetching via API and improves SEO and page load performance
  // Pass the current locale to filter brands by language
  const brands = await getAllBrands(locale);
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold text-center mb-8">{t('title')}</h1>
          <p className="text-gray-600 text-center max-w-3xl mx-auto mb-10">
            {t('description')}
          </p>
          
          {/* Pass preloaded brand data to the client component */}
          <TractorComparison initialBrands={brands} />
          
          <div className="text-center mb-6 mt-12">
            <p className="text-sm text-gray-500">
              {t('disclaimer')}
            </p>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}