<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拉机数据查看器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1, h2, h3, h4 {
            color: #333;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .file-selector {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .file-selector select {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            min-width: 300px;
            font-size: 16px;
        }
        .file-selector button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .file-selector button:hover {
            background-color: #45a049;
        }
        .detail-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .info-row {
            display: flex;
            border-bottom: 1px solid #f5f5f5;
            padding: 12px 0;
        }
        .info-label {
            flex: 0 0 200px;
            font-weight: bold;
            color: #555;
        }
        .info-value {
            flex: 1;
        }
        .tab-container {
            margin-top: 30px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .tab-button {
            padding: 12px 24px;
            background-color: #f1f1f1;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 5px;
            margin-bottom: 5px;
            border-radius: 5px 5px 0 0;
            font-weight: bold;
            color: #666;
        }
        .tab-button:hover {
            background-color: #e0e0e0;
        }
        .tab-button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
            animation: fadeIn 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .tab-content.active {
            display: block;
        }
        .table-container {
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            overflow: hidden;
            border: 1px solid #eaeaea;
        }
        .table-container h4 {
            background-color: #f8f8f8;
            margin: 0;
            padding: 15px;
            border-bottom: 1px solid #eaeaea;
            color: #333;
            font-size: 16px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            table-layout: fixed;
        }
        th, td {
            border: 1px solid #eaeaea;
            padding: 12px 15px;
            text-align: left;
            word-wrap: break-word;
            vertical-align: middle;
            white-space: pre-line;
        }
        th {
            background-color: #f8f8f8;
            font-weight: bold;
            color: #444;
            white-space: nowrap;
            position: sticky;
            top: 0;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        tr:nth-child(even) {
            background-color: #fdfdfd;
        }
        .header-row {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-style: italic;
            color: #666;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        /* 不同类型的表格应用不同样式 */
        #tab-overview .table-container h4 {
            border-left: 4px solid #4CAF50;
        }
        #tab-engine .table-container h4 {
            border-left: 4px solid #2196F3;
        }
        #tab-transmission .table-container h4 {
            border-left: 4px solid #FF9800;
        }
        #tab-dimensions .table-container h4 {
            border-left: 4px solid #9C27B0;
        }
        #tab-photos .table-container h4 {
            border-left: 4px solid #E91E63;
        }
        
        /* 链接样式 */
        a {
            color: #2196F3;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        
        /* 表格为空时的提示 */
        .no-data {
            padding: 20px;
            text-align: center;
            font-style: italic;
            color: #888;
            background-color: #f9f9f9;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .info-row {
                flex-direction: column;
                padding: 8px 0;
            }
            .info-label {
                flex: 0 0 100%;
                margin-bottom: 5px;
            }
            .file-selector {
                flex-direction: column;
                align-items: stretch;
            }
            th, td {
                padding: 8px;
                font-size: 14px;
            }
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>拖拉机数据查看器</h1>
        
        <div class="file-selector">
            <label for="json-file">选择数据文件：</label>
            <select id="json-file">
                <option value="">--请选择--</option>
            </select>
            <button id="load-btn">加载数据</button>
        </div>
        
        <div id="loading" class="loading" style="display: none;">正在加载数据，请稍候...</div>
        
        <div id="tractor-details" style="display: none;">
            <div class="detail-section">
                <h2 id="model-title">拖拉机型号</h2>
                <div class="info-row">
                    <div class="info-label">型号：</div>
                    <div class="info-value" id="model-name"></div>
                </div>
                <div class="info-row">
                    <div class="info-label">数据来源：</div>
                    <div class="info-value" id="model-url"></div>
                </div>
                <div class="info-row">
                    <div class="info-label">爬取时间：</div>
                    <div class="info-value" id="crawled-at"></div>
                </div>
            </div>
            
            <div class="tab-container">
                <div class="tab-buttons">
                </div>
                
                <div id="tab-contents">
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const fileSelector = document.getElementById('json-file');
            const loadBtn = document.getElementById('load-btn');
            const loadingDiv = document.getElementById('loading');
            const tractorDetails = document.getElementById('tractor-details');
            
            // 加载可用的数据文件列表
            loadDataFileList();
            
            // 绑定加载按钮事件
            loadBtn.addEventListener('click', function() {
                if (fileSelector.value) {
                    loadJsonData(fileSelector.value);
                } else {
                    alert('请先选择一个数据文件');
                }
            });
        });
        
        // 加载可用的数据文件列表
        async function loadDataFileList() {
            try {
                const fileSelector = document.getElementById('json-file');
                
                // 清空选择框中的现有选项（保留第一个）
                while (fileSelector.options.length > 1) {
                    fileSelector.remove(1);
                }
                
                // 从API获取文件列表
                const response = await fetch('/api/list-data-files');
                if (!response.ok) {
                    throw new Error('获取文件列表失败');
                }
                
                const files = await response.json();
                
                // 添加从API获取的文件
                files.forEach(file => {
                    const option = document.createElement('option');
                    option.value = file;
                    option.textContent = file.replace(/_/g, ' ').replace('.json', '');
                    fileSelector.appendChild(option);
                });
                
                console.log('成功加载文件列表:', files);
            } catch (error) {
                console.error('加载数据文件列表失败:', error);
                alert('加载数据文件列表失败，请刷新页面重试');
            }
        }
        
        // 加载JSON数据
        async function loadJsonData(fileName) {
            // 显示加载中状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('tractor-details').style.display = 'none';
            
            try {
                // 直接从data目录加载文件，而不是通过API
                const response = await fetch(`data/${fileName}`);
                if (!response.ok) {
                    throw new Error(`无法加载数据文件: ${fileName}`);
                }
                
                const data = await response.json();
                // 使用新的动态标签页加载函数
                document.getElementById('model-title').textContent = data.brand ? `${data.brand} ${data.model}` : (data.model || '未知型号');
                document.getElementById('model-name').textContent = data.model || '未知';
                document.getElementById('model-url').textContent = data.url || '未知';
                document.getElementById('crawled-at').textContent = data.crawledAt ? new Date(data.crawledAt).toLocaleString() : '未知';
                
                // 显示拖拉机详情区域
                document.getElementById('tractor-details').style.display = 'block';
                
                // 检查loadTractorData函数是否可用
                if (typeof loadTractorData === 'undefined') {
                    console.error('loadTractorData 函数未定义！');
                    alert('加载失败：系统脚本未正确加载，请刷新页面重试');
                    return;
                }
                
                console.log('loadTractorData 可用，正在处理数据...');
                console.log('数据结构:', Object.keys(data));
                
                // 直接将原始数据传递给loadTractorData，让它处理各种数据结构
                loadTractorData(data);
            } catch (error) {
                console.error('加载数据失败:', error);
                alert('加载数据失败，请重试: ' + error.message);
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
            }
        }
    </script>
    <!-- 确保在所有脚本之后引入JS文件 -->
    <script src="/src/detail-viewer.js"></script>
</body>
</html> 