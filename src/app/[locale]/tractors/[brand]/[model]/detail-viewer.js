function generateTabs(data) {
    const tabButtons = document.querySelector('.tab-buttons');
    const tabContents = document.querySelector('#tab-contents');
    
    // 清空现有内容
    tabButtons.innerHTML = '';
    tabContents.innerHTML = '';
    
    // 从数据对象中获取可用的标签页
    const availableTabs = Object.keys(data).filter(key => 
        data[key] && (Array.isArray(data[key]) || typeof data[key] === 'object')
    );
    
    console.log('可用的标签页:', availableTabs);
    
    // 如果没有可用的标签页，显示提示信息
    if (availableTabs.length === 0) {
        tabContents.innerHTML = '<div class="no-data">没有可用的数据</div>';
        return;
    }
    
    // 为每个可用的标签页创建按钮和内容区域
    availableTabs.forEach((tabKey, index) => {
        // 标签页ID和显示标题
        const tabId = tabKey.toLowerCase();
        
        // 创建标签按钮
        const button = document.createElement('button');
        button.className = `tab-button ${index === 0 ? 'active' : ''}`;
        button.setAttribute('data-tab', `tab-${tabId}`);
        button.textContent = tabKey; // 直接使用JSON中的键名作为标题
        tabButtons.appendChild(button);
        
        // 创建标签内容区域
        const content = document.createElement('div');
        content.id = `tab-${tabId}`;
        content.className = `tab-content ${index === 0 ? 'active' : ''}`;
        
        const title = document.createElement('h3');
        title.textContent = tabKey; // 直接使用JSON中的键名作为标题
        content.appendChild(title);
        
        const dataContainer = document.createElement('div');
        dataContainer.id = `${tabId}-data`;
        content.appendChild(dataContainer);
        
        tabContents.appendChild(content);
    });
    
    // 重新绑定点击事件
    bindTabEvents();
    
    // 填充数据到各个标签页
    availableTabs.forEach(tabKey => {
        const tabId = tabKey.toLowerCase();
        const displayFunction = window[`display${tabKey.charAt(0).toUpperCase() + tabKey.slice(1)}Data`] || displayGenericData;
        
        try {
            displayFunction(data[tabKey], tabId);
        } catch (e) {
            console.error(`显示${tabKey}数据时出错:`, e);
            displayGenericData(data[tabKey], tabId);
        }
    });
}

function bindTabEvents() {
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // 设置当前标签页为活动状态
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// 修改加载数据的函数
function loadTractorData(tractorId) {
    let data;
    
    // 判断参数是否已经是数据对象
    if (typeof tractorId === 'object') {
        data = tractorId;
        processData(data);
    } else {
        // 如果是字符串ID，则需要获取数据
        fetch(`data/${tractorId}.json`)
            .then(response => response.json())
            .then(loadedData => {
                processData(loadedData);
            })
            .catch(error => {
                console.error('Error loading tractor data:', error);
            });
    }
}

// 处理数据并生成标签页
function processData(data) {
    // 准备处理的数据对象
    let processedData = {};
    
    // 检查数据结构
    if (data.data) {
        // 新的数据结构：data属性下的分类数据
        processedData = data.data;
        console.log('使用data属性下的数据:', Object.keys(processedData));
    } else if (data.tables && Array.isArray(data.tables)) {
        // 旧的数据结构：按类型整理表格数据
        processedData = {};
        
        // 将表格数据按类型分组
        data.tables.forEach(table => {
            // 忽略空表格
            if (!table.rows || table.rows.length === 0) {
                return;
            }
            
            const type = table.type || 'overview';
            if (!processedData[type]) {
                processedData[type] = [];
            }
            processedData[type].push(table);
        });
    } else {
        // 直接使用传入的数据，可能已经分类整理好
        processedData = data;
    }
    
    // 调试：输出所有可用键
    console.log('可用数据键: ', Object.keys(processedData));
    
    // 生成标签页
    generateTabs(processedData);
}

// 显示基本信息数据
function displayOverviewData(data) {
    const container = document.getElementById('overview-data');
    
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">没有基本信息数据</div>';
        return;
    }
    
    displayTablesForType('overview', data);
}

// 显示发动机数据
function displayEngineData(data) {
    const container = document.getElementById('engine-data');
    
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">没有发动机数据</div>';
        return;
    }
    
    displayTablesForType('engine', data);
}

// 显示变速箱数据
function displayTransmissionData(data) {
    const container = document.getElementById('transmission-data');
    
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">没有变速箱数据</div>';
        return;
    }
    
    displayTablesForType('transmission', data);
}

// 显示尺寸规格数据
function displayDimensionsData(data) {
    const container = document.getElementById('dimensions-data');
    
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">没有尺寸规格数据</div>';
        return;
    }
    
    displayTablesForType('dimensions', data);
}

// 显示照片数据
function displayPhotosData(data) {
    const container = document.getElementById('photos-data');
    
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="no-data">没有照片数据</div>';
        return;
    }
    
    // 清空现有内容
    container.innerHTML = '';
    
    // 创建照片画廊容器
    const galleryContainer = document.createElement('div');
    galleryContainer.className = 'photo-gallery';
    
    // 添加照片画廊标题
    const galleryTitle = document.createElement('h4');
    galleryTitle.textContent = '拖拉机照片';
    galleryContainer.appendChild(galleryTitle);
    
    // 检查是否有占位信息（没有照片）
    if (data.length === 1 && data[0].isPlaceholder) {
        const noPhotoContainer = document.createElement('div');
        noPhotoContainer.className = 'no-photo-container';
        
        // 创建占位图片
        if (data[0].src) {
            const img = document.createElement('img');
            img.src = data[0].src;
            img.alt = data[0].alt || '没有可用照片';
            img.className = 'no-photo-img';
            noPhotoContainer.appendChild(img);
        }
        
        // 创建消息文本
        const message = document.createElement('div');
        message.className = 'no-photo-message';
        message.textContent = data[0].message || '当前没有该拖拉机的照片';
        noPhotoContainer.appendChild(message);
        
        // 如果有联系邮箱相关信息，添加邮箱链接
        if (data[0].message && data[0].message.includes('email it to')) {
            const emailText = document.createElement('div');
            emailText.innerHTML = '您可以将照片发送到 <a href="mailto:<EMAIL>"><EMAIL></a>';
            noPhotoContainer.appendChild(emailText);
        }
        
        galleryContainer.appendChild(noPhotoContainer);
        
        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent += `
        .no-photo-container {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            margin-top: 15px;
        }
        
        .no-photo-img {
            max-width: 200px;
            height: auto;
            margin-bottom: 15px;
        }
        
        .no-photo-message {
            font-size: 16px;
            margin-bottom: 10px;
            color: #666;
        }
        `;
        
        if (!document.querySelector('style[data-photo-gallery-styles]')) {
            style.setAttribute('data-photo-gallery-styles', 'true');
            document.head.appendChild(style);
        }
        
        container.appendChild(galleryContainer);
        return;
    }
    
    // 创建照片网格
    const photoGrid = document.createElement('div');
    photoGrid.className = 'photo-grid';
    
    // 处理每一张照片
    data.forEach(photo => {
        // 跳过无效的照片
        if (!photo.src && !photo.fullSizeUrl) {
            return;
        }
        
        // 创建照片项容器
        const photoItem = document.createElement('div');
        photoItem.className = 'photo-item';
        
        // 创建图片链接
        const photoLink = document.createElement('a');
        photoLink.href = photo.fullSizeUrl || photo.src;
        photoLink.target = '_blank';
        photoLink.title = photo.description || photo.alt || '点击查看大图';
        
        // 创建图片元素
        const img = document.createElement('img');
        img.loading = 'lazy'; // 懒加载
        img.src = photo.src || '/assets/image-placeholder.png'; // 如果没有缩略图就使用占位图
        img.alt = photo.alt || '拖拉机照片';
        
        // 添加图片到链接中
        photoLink.appendChild(img);
        
        // 创建图片说明
        if (photo.description) {
            const photoCaption = document.createElement('div');
            photoCaption.className = 'photo-caption';
            photoCaption.textContent = photo.description;
            photoItem.appendChild(photoCaption);
        }
        
        // 添加链接到照片项中
        photoItem.appendChild(photoLink);
        
        // 添加照片项到网格中
        photoGrid.appendChild(photoItem);
    });
    
    // 添加网格到画廊容器
    galleryContainer.appendChild(photoGrid);
    
    // 添加画廊到主容器
    container.appendChild(galleryContainer);
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
    .photo-gallery {
        margin-bottom: 20px;
    }
    
    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .photo-item {
        position: relative;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    
    .photo-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .photo-item img {
        width: 100%;
        height: auto;
        display: block;
        object-fit: cover;
        aspect-ratio: 4/3;
    }
    
    .photo-caption {
        padding: 8px;
        background-color: rgba(0,0,0,0.7);
        color: white;
        font-size: 12px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .photo-item:hover .photo-caption {
        opacity: 1;
    }
    `;
    
    // 只有在页面上还没有这个样式时才添加
    if (!document.querySelector('style[data-photo-gallery-styles]')) {
        style.setAttribute('data-photo-gallery-styles', 'true');
        document.head.appendChild(style);
    }
}

// 为指定类型显示表格数据
function displayTablesForType(type, tables) {
    const containerElement = document.getElementById(`${type}-data`);
    
    if (!tables || tables.length === 0) {
        containerElement.innerHTML = `<div class="no-data">没有找到${getTypeDisplayName(type)}数据</div>`;
        return;
    }
    
    tables.forEach((table, index) => {
        // 创建表格容器
        const tableContainer = document.createElement('div');
        tableContainer.className = 'table-container';
        
        // 如果表格有标题，则显示
        if (table.title) {
            const titleElement = document.createElement('h4');
            titleElement.textContent = table.title;
            tableContainer.appendChild(titleElement);
        } else if (index === 0 && type === 'overview') {
            // 为第一个主表格添加默认标题
            const titleElement = document.createElement('h4');
            titleElement.textContent = '概览';
            tableContainer.appendChild(titleElement);
        }
        
        // 处理表格数据 - 智能格式化和合并单元格
        const processedTable = processTableData(table);
        
        // 创建表格元素
        const tableElement = document.createElement('table');
        
        // 计算表格的最大列数
        let maxColumns = 0;
        if (processedTable.headers && processedTable.headers.length > 0) {
            maxColumns = processedTable.headers.length;
        }
        if (processedTable.rows && processedTable.rows.length > 0) {
            processedTable.rows.forEach(row => {
                if (row.length > maxColumns) {
                    maxColumns = row.length;
                }
            });
        }
        
        // 添加表头（如果有）
        if (processedTable.headers && processedTable.headers.length > 0) {
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            
            processedTable.headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            
            thead.appendChild(headerRow);
            tableElement.appendChild(thead);
        }
        
        // 添加表格内容
        const tbody = document.createElement('tbody');
        
        if (processedTable.rows && processedTable.rows.length > 0) {
            processedTable.rows.forEach((row, rowIndex) => {
                const tr = document.createElement('tr');
                
                // 如果只有一列，第一列就是标题行
                if (row.length === 1 && row[0].text && row[0].text.trim()) {
                    tr.className = 'header-row';
                    
                    // 创建单元格
                    const td = document.createElement('td');
                    const cell = row[0];
                    
                    // 设置单元格跨列
                    if (maxColumns > 1) {
                        td.setAttribute('colspan', maxColumns);
                    }
                    
                    // 设置单元格文本
                    if (cell.text) {
                        // 使用innerHTML设置内容，确保换行符能正确显示
                        // 先将换行符替换为<br>标签，然后设置
                        const formattedText = cell.text.replace(/\n/g, '<br>');
                        td.innerHTML = formattedText;
                    }
                    
                    // 添加链接（如果有）
                    if (cell.links && cell.links.length > 0) {
                        // 如果存在既有文本又有链接的情况，清空td内容并重新构建
                        if (cell.text && cell.links.some(link => link.text)) {
                            td.innerHTML = '';
                        }
                        
                        cell.links.forEach(link => {
                            // 只有在链接有文本时才添加链接
                            if (link.text) {
                                const a = document.createElement('a');
                                a.href = link.href;
                                a.textContent = link.text;
                                
                                // 检查是否为内部链接（切换标签页）
                                const isEngineLink = link.text.toLowerCase().includes('engine') || 
                                                  link.href.toLowerCase().includes('-engine.html');
                                const isTransmissionLink = link.text.toLowerCase().includes('transmission') || 
                                                         link.href.toLowerCase().includes('-transmission.html');
                                const isDimensionsLink = link.text.toLowerCase().includes('dimension') || 
                                                      link.href.toLowerCase().includes('-dimensions.html');
                                const isOverviewLink = link.text.toLowerCase().includes('overview') ||
                                                    !link.href.includes('-');
                                                      
                                if (isEngineLink || isTransmissionLink || isDimensionsLink || isOverviewLink) {
                                    // 内部链接 - 切换标签页
                                    a.addEventListener('click', function(e) {
                                        e.preventDefault(); // 阻止默认链接行为
                                        
                                        // 确定要切换到哪个标签页
                                        let tabId = '';
                                        if (isEngineLink) tabId = 'tab-engine';
                                        else if (isTransmissionLink) tabId = 'tab-transmission';
                                        else if (isDimensionsLink) tabId = 'tab-dimensions';
                                        else if (isOverviewLink) tabId = 'tab-overview';
                                        
                                        // 切换标签页
                                        if (tabId) {
                                            // 移除所有选项卡和内容区域的活动状态
                                            document.querySelectorAll('.tab-button').forEach(btn => 
                                                btn.classList.remove('active'));
                                            document.querySelectorAll('.tab-content').forEach(content => 
                                                content.classList.remove('active'));
                                            
                                            // 设置目标标签页为活动状态
                                            document.querySelector(`.tab-button[data-tab="${tabId}"]`).classList.add('active');
                                            document.getElementById(tabId).classList.add('active');
                                            
                                            // 平滑滚动到标签页内容
                                            document.getElementById(tabId).scrollIntoView({behavior: 'smooth'});
                                        }
                                    });
                                    a.style.cursor = 'pointer';
                                    a.title = "点击切换到相关标签页";
                                } else {
                                    // 外部链接 - 正常打开
                                    a.target = '_blank';
                                }
                                
                                // 清空单元格并添加链接
                                td.textContent = '';
                                td.appendChild(a);
                            }
                        });
                    }
                    
                    // 添加图片（如果有）
                    if (cell.images && cell.images.length > 0) {
                        // 如果单元格有文本和图片，先保存文本内容
                        let textContent = '';
                        if (cell.text && cell.text.trim()) {
                            textContent = cell.text.replace(/\n/g, '<br>');
                            td.innerHTML = '';
                        }
                        
                        // 如果有文本，先添加文本内容
                        if (textContent) {
                            const textDiv = document.createElement('div');
                            textDiv.innerHTML = textContent;
                            td.appendChild(textDiv);
                        }
                        
                        cell.images.forEach(image => {
                            const img = document.createElement('img');
                            img.src = image.src;
                            img.alt = image.alt || '';
                            img.loading = 'lazy'; // 懒加载图片
                            
                            // 为图片添加点击放大功能
                            img.onclick = function() {
                                window.open(image.src, '_blank');
                            };
                            img.style.cursor = 'pointer';
                            
                            td.appendChild(img);
                        });
                    }
                    
                    tr.appendChild(td);
                } else {
                    // 多列的行，正常处理每个单元格
                    row.forEach((cell, cellIndex) => {
                        const td = document.createElement('td');
                        
                        // 设置单元格文本
                        if (cell.text) {
                            // 使用innerHTML设置内容，确保换行符能正确显示
                            // 先将换行符替换为<br>标签，然后设置
                            const formattedText = cell.text.replace(/\n/g, '<br>');
                            td.innerHTML = formattedText;
                        }
                        
                        // 添加链接（如果有）
                        if (cell.links && cell.links.length > 0) {
                            // 如果存在既有文本又有链接的情况，清空td内容并重新构建
                            if (cell.text && cell.links.some(link => link.text)) {
                                td.innerHTML = '';
                            }
                            
                            cell.links.forEach(link => {
                                // 只有在链接有文本时才添加链接
                                if (link.text) {
                                    const a = document.createElement('a');
                                    a.href = link.href;
                                    a.textContent = link.text;
                                    
                                    // 检查是否为内部链接（切换标签页）
                                    const isEngineLink = link.text.toLowerCase().includes('engine') || 
                                                      link.href.toLowerCase().includes('-engine.html');
                                    const isTransmissionLink = link.text.toLowerCase().includes('transmission') || 
                                                             link.href.toLowerCase().includes('-transmission.html');
                                    const isDimensionsLink = link.text.toLowerCase().includes('dimension') || 
                                                          link.href.toLowerCase().includes('-dimensions.html');
                                    const isOverviewLink = link.text.toLowerCase().includes('overview') ||
                                                        !link.href.includes('-');
                                                          
                                    if (isEngineLink || isTransmissionLink || isDimensionsLink || isOverviewLink) {
                                        // 内部链接 - 切换标签页
                                        a.addEventListener('click', function(e) {
                                            e.preventDefault(); // 阻止默认链接行为
                                            
                                            // 确定要切换到哪个标签页
                                            let tabId = '';
                                            if (isEngineLink) tabId = 'tab-engine';
                                            else if (isTransmissionLink) tabId = 'tab-transmission';
                                            else if (isDimensionsLink) tabId = 'tab-dimensions';
                                            else if (isOverviewLink) tabId = 'tab-overview';
                                            
                                            // 切换标签页
                                            if (tabId) {
                                                // 移除所有选项卡和内容区域的活动状态
                                                document.querySelectorAll('.tab-button').forEach(btn => 
                                                    btn.classList.remove('active'));
                                                document.querySelectorAll('.tab-content').forEach(content => 
                                                    content.classList.remove('active'));
                                                
                                                // 设置目标标签页为活动状态
                                                document.querySelector(`.tab-button[data-tab="${tabId}"]`).classList.add('active');
                                                document.getElementById(tabId).classList.add('active');
                                                
                                                // 平滑滚动到标签页内容
                                                document.getElementById(tabId).scrollIntoView({behavior: 'smooth'});
                                            }
                                        });
                                        a.style.cursor = 'pointer';
                                        a.title = "点击切换到相关标签页";
                                    } else {
                                        // 外部链接 - 正常打开
                                        a.target = '_blank';
                                    }
                                    
                                    // 清空单元格并添加链接
                                    td.textContent = '';
                                    td.appendChild(a);
                                }
                            });
                        }
                        
                        // 添加图片（如果有）
                        if (cell.images && cell.images.length > 0) {
                            // 如果单元格有文本和图片，先保存文本内容
                            let textContent = '';
                            if (cell.text && cell.text.trim()) {
                                textContent = cell.text.replace(/\n/g, '<br>');
                                td.innerHTML = '';
                            }
                            
                            // 如果有文本，先添加文本内容
                            if (textContent) {
                                const textDiv = document.createElement('div');
                                textDiv.innerHTML = textContent;
                                td.appendChild(textDiv);
                            }
                            
                            cell.images.forEach(image => {
                                const img = document.createElement('img');
                                img.src = image.src;
                                img.alt = image.alt || '';
                                img.loading = 'lazy'; // 懒加载图片
                                
                                // 为图片添加点击放大功能
                                img.onclick = function() {
                                    window.open(image.src, '_blank');
                                };
                                img.style.cursor = 'pointer';
                                
                                td.appendChild(img);
                            });
                        }
                        
                        tr.appendChild(td);
                    });
                }
                
                tbody.appendChild(tr);
            });
        }
        
        tableElement.appendChild(tbody);
        tableContainer.appendChild(tableElement);
        containerElement.appendChild(tableContainer);
    });
}

// 处理表格数据 - 智能格式化
function processTableData(table) {
    // 深拷贝表格数据，避免修改原始数据
    const processedTable = JSON.parse(JSON.stringify(table));
    
    // 如果表格没有标题行，尝试找出标题行
    if (!processedTable.headers || processedTable.headers.length === 0) {
        // 如果第一行只有一个单元格，可能是标题行
        if (processedTable.rows && processedTable.rows.length > 0) {
            const firstRow = processedTable.rows[0];
            
            // 如果第一行有两列，且第一列没有值或第二列有值，可能是键值对表格
            if (processedTable.rows.length > 1 && 
                firstRow.length === 2 && 
                (firstRow[0].text === '' || firstRow[1].text)) {
                
                // 设置表头
                processedTable.headers = ['属性', '值'];
            }
        }
    }
    
    return processedTable;
}

// 获取类型的显示名称
function getTypeDisplayName(type) {
    const typeNames = {
        'overview': '基本',
        'engine': '发动机',
        'transmission': '变速箱',
        'dimensions': '尺寸',
        'photos': '照片'
    };
    
    return typeNames[type] || type;
}

// 通用数据显示函数，用于处理没有专门显示函数的标签页
function displayGenericData(data, tabId) {
    const container = document.getElementById(`${tabId}-data`);
    
    if (!data || (Array.isArray(data) && data.length === 0)) {
        container.innerHTML = `<div class="no-data">没有${tabId}数据</div>`;
        return;
    }
    
    if (Array.isArray(data)) {
        displayTablesForType(tabId, data);
    } else {
        // 对于非数组类型的数据，创建一个基本的表格显示
        const table = {
            type: tabId,
            rows: []
        };
        
        // 将对象属性转换为表格行
        Object.entries(data).forEach(([key, value]) => {
            table.rows.push([
                { text: key },
                { text: JSON.stringify(value) }
            ]);
        });
        
        displayTablesForType(tabId, [table]);
    }
} 