"use client";

import React from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { useCallback } from 'react';
import { cn } from '@/lib/utils';

// 表格数据类型
export interface TableCell {
  text?: string;
  links?: { href: string; text: string }[];
  images?: { src: string; alt?: string }[];
}

export interface TableData {
  index?: number;
  type?: string;
  title?: string;
  headers?: string[];
  rows: TableCell[][];
}

export interface PhotoData {
  isPlaceholder?: boolean;
  message?: string;
  src: string;
  alt?: string;
  description?: string;
  fullSizeUrl?: string;
}

interface TractorContentProps {
  brand: string;
  model: string;
  availableTabs: string[];
  tabData: Record<string, any[]>;
}

// 显示表格组件
interface DisplayTableProps {
  table: TableData;
  type: string;
  className?: string;
}

function DisplayTable({ table, type, className = '' }: DisplayTableProps) {
  // 处理HTML实体编码
  const decodeHtmlEntities = (text: string | undefined): string => {
    if (!text) return '';
    return String(text)
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'")
      .replace(/&#39;/g, "'");
  };

  return (
    <div className={cn("bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden", className)}>
      {table.title && (
        <div className="p-4 border-b border-gray-100 bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900">{decodeHtmlEntities(table.title)}</h3>
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <tbody className="divide-y divide-gray-200">
            {table.rows.map((row, rowIndex) => (
              <tr 
                key={rowIndex} 
                className={cn(
                  "hover:bg-gray-50",
                  rowIndex === 0 && "bg-gray-50" // 只有第一行有背景色
                )}
              >
                {row.map((cell, cellIndex) => {
                  // 确定是否为表头单元格（只在第一行）
                  const isHeaderCell = rowIndex === 0;
                  const cellTag = isHeaderCell ? 'th' : 'td';
                  
                  // 创建Cell元素 - 表头或普通单元格
                  const CellComponent = React.createElement(
                    cellTag,
                    {
                      key: cellIndex,
                      className: cn(
                        "p-3 text-sm",
                        isHeaderCell 
                          ? "font-medium text-gray-700 text-left" 
                          : "text-gray-600",
                        cell.images?.length && "space-y-2"
                      ),
                      colSpan: row.length === 1 ? 2 : undefined
                    },
                    <>
                      {cell.text && (
                        <div className="whitespace-pre-line">
                          {decodeHtmlEntities(cell.text)}
                        </div>
                      )}
                      
                      {/* 完全不渲染链接部分 */}
                      
                      {cell.images?.map((image, imageIndex) => (
                        <div key={imageIndex} className="flex justify-center">
                          <Image
                            src={image.src}
                            alt={decodeHtmlEntities(image.alt) || ""}
                            width={180}
                            height={120}
                            className="object-contain"
                          />
                        </div>
                      ))}
                    </>
                  );
                  
                  return CellComponent;
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// 显示照片组件
function DisplayPhotos({ photos }: { photos: PhotoData[] }) {
  if (!photos.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No photos available</p>
      </div>
    );
  }
  
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {photos.map((photo, index) => (
        <div 
          key={index}
          className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-all duration-300"
        >
          {photo.isPlaceholder ? (
            <div className="aspect-video bg-gray-100 flex items-center justify-center">
              <p className="text-gray-500 text-center p-4">{photo.message || "No image available"}</p>
            </div>
          ) : (
            <div className="aspect-video bg-gray-50 flex items-center justify-center">
              <a 
                href={photo.fullSizeUrl || photo.src} 
                target="_blank" 
                rel="noopener noreferrer"
                className="block w-full h-full"
              >
                <Image
                  src={photo.src}
                  alt={photo.alt || ""}
                  width={400}
                  height={300}
                  className="object-contain w-full h-full"
                />
              </a>
            </div>
          )}
          {photo.description && (
            <div className="p-3 border-t border-gray-100 text-center">
              <p className="text-sm text-gray-600">{photo.description}</p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

// 动态检测数据类型并渲染相应的组件
function detectDataTypeAndRender(data: any[]) {
  // 检查是否是照片数据
  if (data.length > 0 && 'src' in data[0]) {
    return <DisplayPhotos photos={data as PhotoData[]} />;
  }
  
  // 检查是否是表格数据
  if (data.length > 0 && 'rows' in data[0]) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {(data as TableData[]).map((table, index) => (
          <DisplayTable
            key={index}
            table={table}
            type="dynamic"
            className=""
          />
        ))}
      </div>
    );
  }
  
  // 如果是普通数据对象数组，尝试将其转换为表格数据
  if (data.length > 0 && typeof data[0] === 'object') {
    const tableData: TableData = {
      rows: []
    };
    
    // 获取所有键名（用作表头）
    const allKeys = Array.from(new Set(
      data.flatMap(item => Object.keys(item))
    ));
    
    // 将每个对象转换为一行
    data.forEach(item => {
      const row: TableCell[] = allKeys.map(key => {
        const value = item[key];
        if (!value) return { text: '' };
        
        // 处理不同类型的值
        if (typeof value === 'string') {
          // 检查是否是图片URL
          if (value.match(/\.(jpeg|jpg|gif|png)$/i)) {
            return { 
              images: [{ src: value, alt: key }] 
            };
          }
          // 检查是否是链接
          if (value.startsWith('http')) {
            return { 
              links: [{ href: value, text: key }] 
            };
          }
          // 普通文本
          return { text: value };
        }
        
        // 其他类型转为字符串
        return { text: String(value) };
      });
      
      tableData.rows.push(row);
    });
    
    // 如果有数据，添加表头行
    if (tableData.rows.length > 0) {
      tableData.rows.unshift(
        allKeys.map(key => ({ text: key }))
      );
    }
    
    return (
      <DisplayTable
        table={tableData}
        type="dynamic"
      />
    );
  }
  
  // 默认情况：返回无数据提示
  return (
    <div className="py-4 text-center text-gray-500">
      No data available
    </div>
  );
}

export function TractorContent({
  brand,
  model,
  availableTabs,
  tabData
}: TractorContentProps) {
  const searchParams = useSearchParams();
  
  // 获取当前激活的标签页
  const currentTab = searchParams.get('tab') || '';
  const activeTab = availableTabs.includes(currentTab) ? currentTab : availableTabs[0];
  
  // 渲染特定类型的内容
  const renderTabContent = (tabId: string) => {
    const data = tabData[tabId];
    
    if (!data || !Array.isArray(data) || data.length === 0) {
      return (
        <div className="py-4 text-center text-gray-500">
          No data available
        </div>
      );
    }
    
    // 对Photos标签做特殊处理
    if (tabId === 'photos') {
      return <DisplayPhotos photos={data as PhotoData[]} />;
    }
    
    // 其他标签使用动态检测
    return detectDataTypeAndRender(data);
  };
  
  return (
    <Tabs value={activeTab} className="w-full">
      {availableTabs.map(tabId => (
        <TabsContent key={tabId} value={tabId} className="mt-0">
          {renderTabContent(tabId)}
        </TabsContent>
      ))}
    </Tabs>
  );
}
