"use client";

import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

interface TractorTabsNavProps {
  availableTabs: string[];
}

export function TractorTabsNav({ availableTabs }: TractorTabsNavProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  // 使用翻译
  const t = useTranslations('TractorPage.tabs');

  // 获取当前激活的标签页
  const currentTab = searchParams.get('tab') || '';
  const activeTab = availableTabs.includes(currentTab) ? currentTab : availableTabs[0];

  // 创建URL生成器函数（设置tab查询参数）
  const createTabUrl = (tabId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tabId);
    return `${pathname}?${params.toString()}`;
  };

  // 处理HTML实体编码
  const decodeHtmlEntities = (text: string): string => {
    if (!text) return '';
    return String(text)
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'")
      .replace(/&#39;/g, "'");
  };

  // 获取标签的翻译文本
  const getTabTranslation = (tabId: string): string => {
    try {
      // 尝试获取翻译，如果没有对应的翻译键，则使用原始tabId
      return t(tabId.toLowerCase());
    } catch (error) {
      // 如果翻译键不存在，返回原始tabId并进行HTML实体解码
      return decodeHtmlEntities(tabId);
    }
  };

  return (
    <div className="mb-6 border-b border-gray-200">
      <div className="flex items-center overflow-x-auto gap-2 scrollbar-hide">
        {availableTabs.map((tabId) => {
          const isActive = tabId === activeTab;

          return (
            <Link
              key={tabId}
              href={createTabUrl(tabId)}
              className={`
                py-3 px-4 border-b-2 font-medium text-sm whitespace-nowrap transition-colors
                ${isActive
                  ? 'border-tractor text-tractor'
                  : 'border-transparent text-gray-500 hover:text-gray-900 hover:border-gray-300'}
              `}
            >
              {getTabTranslation(tabId)}
            </Link>
          );
        })}
      </div>
    </div>
  );
}
