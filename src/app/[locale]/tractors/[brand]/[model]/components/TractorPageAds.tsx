import { AffiliateAdSection } from '../../../../components/AffiliateAdSection';
import { PageContext } from '@/lib/types';
import { ProductMatcher } from '@/lib/productMatcher';

interface TractorPageAdsProps {
  brandSlug: string;
  tractorModel: string;
  brandName: string;
  modelName: string;
  locale: string;
  className?: string;
}

export async function TractorPageAds({ 
  brandSlug, 
  tractorModel, 
  brandName, 
  modelName, 
  locale, 
  className = '' 
}: TractorPageAdsProps) {
  // 创建页面上下文
  const pageContext: PageContext = {
    pageType: 'tractor',
    brandSlug,
    tractorModel,
    locale
  };

  // 获取与拖拉机相关的产品
  const tractorProducts = await ProductMatcher.getProductsForPage(pageContext, 4);

  // 如果没有相关产品，不显示广告区域
  if (tractorProducts.length === 0) {
    return null;
  }

  return (
    <section className={`py-8 bg-white border-t border-gray-200 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-2">
            {brandName} {modelName} 相关产品推荐
          </h2>
          <p className="text-sm text-gray-600">
            为您的 {brandName} {modelName} 拖拉机精选的零件、工具和配件
          </p>
        </div>
        
        <AffiliateAdSection
          products={tractorProducts}
          locale={locale}
          pageContext={pageContext}
          className="!py-0 !bg-transparent"
        />
      </div>
    </section>
  );
}