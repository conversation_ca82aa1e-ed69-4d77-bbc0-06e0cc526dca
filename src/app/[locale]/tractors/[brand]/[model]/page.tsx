import Link from 'next/link';
import { Header } from '@/app/[locale]/components/Header';
import { Footer } from '@/app/[locale]/components/Footer';
import { Metadata } from 'next';
import { TractorTabsNav } from './components/TractorTabsNav';
import { TractorContent } from './components/TractorContent';
import { TractorPageAds } from './components/TractorPageAds';
import { cache } from 'react';
import { getTranslations } from 'next-intl/server';
import Breadcrumbs from '@/components/Breadcrumbs';
import JsonLd from '@/components/JsonLd';
// import BreadcrumbSchema, { generateBreadcrumbItems } from '@/components/BreadcrumbSchema';
// import ProductSchema, { generateTractorProductSchema } from '@/components/ProductSchema';

// 引入拖拉机数据类型和服务函数
import { getTractorData, type TractorDetailData } from '@/services/tractorService';
// 导入TractorContent组件使用的类型，该类型用于组件间的传递
import type { TableData, PhotoData } from './components/TractorContent';
import { getCachedModels } from '@/lib/cache';
import { routing } from '@/i18n/routing';

// ISR配置 - 拖拉机型号页面使用增量静态再生成
export const revalidate = 86400; // 24小时重新验证
export const dynamicParams = true; // 允许动态生成未预生成的页面

// 生成静态参数 - 预生成热门的拖拉机型号页面
export async function generateStaticParams() {
  // 在构建时，如果无法连接数据库，返回空数组
  if (process.env.NODE_ENV === 'production' && process.env.BUILD_TIME === 'true') {
    console.log('构建时跳过拖拉机型号静态参数生成');
    return [];
  }

  try {
    const buildMode = process.env.BUILD_MODE || 'full';
    const params = [];

    // 全量生成所有拖拉机型号页面
    const maxModels = Infinity; // 生成所有型号

    for (const locale of routing.locales) {
      try {
        // 获取所有型号数据
        const models = await getCachedModels();

        // 按品牌分组并限制数量
        const brandGroups: Record<string, any[]> = {};
        models.forEach(model => {
          if (model.brand_key && model.model_key) {
            if (!brandGroups[model.brand_key]) {
              brandGroups[model.brand_key] = [];
            }
            brandGroups[model.brand_key].push(model);
          }
        });

        // 为每个品牌选择最受欢迎的型号
        let totalAdded = 0;
        for (const [brandKey, brandModels] of Object.entries(brandGroups)) {
          if (totalAdded >= maxModels) break;

          // 每个品牌生成所有型号
          const maxPerBrand = Infinity;
          const selectedModels = brandModels.slice(0, maxPerBrand);

          for (const model of selectedModels) {
            if (totalAdded >= maxModels) break;

            params.push({
              locale: locale,
              brand: brandKey,
              model: model.model_key
            });
            totalAdded++;
          }
        }
      } catch (error) {
        console.warn(`跳过 ${locale} 语言拖拉机型号参数生成:`, error.message);
        continue;
      }
    }

    console.log(`生成 ${params.length} 个拖拉机型号页面静态参数 (${buildMode} 模式)`);
    return params;
  } catch (error) {
    console.error('生成拖拉机型号静态参数失败:', error);
    return [];
  }
}



// 缓存包装后的拖拉机数据获取函数
const fetchTractorData = cache(async (brand: string, model: string, locale: string = 'en'): Promise<TractorDetailData | null> => {
  return getTractorData(brand, model, locale);
});

/**
 * 将models表中的Photos转换为组件所需的PhotoData格式
 */
const convertPhotoData = (modelPhotos: any[] | undefined): PhotoData[] => {
  if (!modelPhotos || !Array.isArray(modelPhotos)) return [];

  const photoDataArray: PhotoData[] = [];

  modelPhotos.forEach(photo => {
    if (photo.url) {
      photoDataArray.push({
        src: photo.url,
        alt: photo.title || '',
        description: photo.description || photo.title || '',
      });
    }
  });

  return photoDataArray;
};

/**
 * 将一般数据转换为表格格式
 */
const convertToTableData = (data: any[]): TableData[] => {
  if (!Array.isArray(data)) return [];

  // 检查数据是否已经是TableData格式
  if (data.length > 0 && 'rows' in data[0]) {
    // 过滤掉包含"Page information"的表格
    return data.filter(table => {
      // 检查标题是否包含"Page information"或"TractorData.com"
      if (table.title && (String(table.title).includes('Page information') || String(table.title).includes('TractorData.com'))) {
        return false;
      }

      // 检查表格内容是否包含"Page information"或"TractorData.com"
      if (table.rows && Array.isArray(table.rows)) {
        for (const row of table.rows) {
          for (const cell of row) {
            if (cell.text && (String(cell.text).includes('Page information') || String(cell.text).includes('TractorData.com'))) {
              return false;
            }
          }
        }
      }

      return true;
    }) as TableData[];
  }

  // 将通用对象数组转换为表格数据
  const tableData: TableData = {
    rows: []
  };

  // 如果是普通对象数组，将其转换为表格
  if (typeof data[0] === 'object') {
    // 获取所有键名作为表头
    const allKeys = Array.from(new Set(
      data.flatMap(item => Object.keys(item))
    ));

    // 将键名转换为表头行
    tableData.rows.push(
      allKeys.map(key => ({ text: key }))
    );

    // 将每个对象转换为表格行
    data.forEach(item => {
      const row = allKeys.map(key => {
        const value = item[key];
        if (!value) return { text: '' };

        // 处理不同类型的值
        if (typeof value === 'string') {
          // 检查是否是图片URL
          if (value.match(/\.(jpeg|jpg|gif|png)$/i)) {
            return { images: [{ src: value, alt: key }] };
          }
          // 检查是否是链接
          if (value.startsWith('http')) {
            return { links: [{ href: value, text: key }] };
          }
          // 普通文本
          return { text: value };
        }

        // 其他类型转为字符串
        return { text: String(value) };
      });

      tableData.rows.push(row);
    });
  }

  // 检查生成的表格是否包含"Page information"或"TractorData.com"
  const containsPageInfo = tableData.rows.some(row =>
    row.some(cell => cell.text && (String(cell.text).includes('Page information') || String(cell.text).includes('TractorData.com')))
  );

  if (containsPageInfo) {
    return []; // 如果包含Page information则返回空数组
  }

  return [tableData];
};

/**
 * 处理HTML实体编码
 */
const decodeHtmlEntities = (text: string): string => {
  if (!text) return text;
  return String(text)
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'")
    .replace(/&#39;/g, "'");
};

/**
 * 处理并规范化所有数据
 */
const processAllData = (tractorData: TractorDetailData | null): Record<string, TableData[] | PhotoData[]> => {
  if (!tractorData || !tractorData.data) return {};

  const processedData: Record<string, TableData[] | PhotoData[]> = {};

  // 处理所有可能的数据类型
  Object.entries(tractorData.data as Record<string, any>).forEach(([key, value]) => {
    // 解码key中的HTML实体
    const decodedKey = decodeHtmlEntities(key);

    if (Array.isArray(value) && value.length > 0) {
      // 其他所有非照片数据都作为表格处理
      if (decodedKey !== 'photos') {
        processedData[decodedKey] = convertToTableData(value);
      }
    }
  });

  // 处理照片数据（优先使用 modelPhotos）
  processedData['photos'] = convertPhotoData(tractorData.modelPhotos);

  return processedData;
};

// 动态生成元数据
export async function generateMetadata(
  { params }: {
    params: { brand: string; model: string; locale: string }
  }): Promise<Metadata> {
  // 需要先await params对象
  const paramObj = await params;
  const brand = paramObj.brand;
  const model = paramObj.model;
  const locale = paramObj.locale;

  // 获取拖拉机数据
  const tractorData = await fetchTractorData(brand, model, locale);

  // 获取翻译
  const t = await getTranslations({ locale, namespace: 'TractorPage' });

  if (!tractorData) {
    return {
      title: t('notFound.title'),
      description: t('notFound.description'),
      robots: 'noindex, nofollow'
    };
  }

  // 使用新的SEO工具生成元数据
  const { generateTractorSEOMetadata } = await import('@/lib/seo');

  // 获取拖拉机信息用于翻译
  const brandName = tractorData.brand || brand;
  const modelName = tractorData.model_key || model;
  const fullName = `${brandName} ${modelName}`;

  // 准备翻译对象，传递必要的参数
  const translations = {
    title: t('title', { fullName }),
    description: t('description', { fullName })
  };

  // 准备拖拉机数据
  const enhancedTractorData = {
    brand_name: brandName,
    model_name: modelName,
    brand_key: brand,
    model_key: model,
    description: tractorData.description,
    image_url: tractorData.modelPhotos?.[0]?.url,
    ...tractorData // 包含所有其他规格数据
  };

  return generateTractorSEOMetadata(enhancedTractorData, locale, translations);
}

// 主页面组件
export default async function TractorDetailPage({
  params
}: {
  params: { brand: string; model: string; locale: string }
}) {
  // 在 Next.js App Router 中，访问 params 属性前需要先 await
  const paramObj = await params;
  const brand = paramObj.brand;
  const model = paramObj.model;
  const locale = paramObj.locale;

  // 获取翻译
  const tPage = await getTranslations({ locale, namespace: 'TractorPage' });

  // 服务端获取数据
  const tractorData = await fetchTractorData(brand, model, locale);

  // 如果没有获取到数据，显示错误信息
  if (!tractorData) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <h1 className="text-2xl font-bold text-gray-800 mb-4">Tractor Data Not Available</h1>
              <p className="text-gray-600 mb-6">Sorry, we could not find data for {brand} {model}.</p>
              <Link href="/tractors" className="inline-flex items-center justify-center bg-tractor hover:bg-tractor/90 text-white py-2 px-4 rounded-lg font-medium transition-colors text-sm md:text-base">
                Back to Tractors
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // 处理所有数据
  const processedData = processAllData(tractorData);

  // 获取可用标签
  const availableTabs = Object.keys(processedData).filter(key =>
    Array.isArray(processedData[key]) && processedData[key].length > 0
  );

  // 确保至少有overview标签
  if (availableTabs.length === 0) {
    availableTabs.push('overview');
    processedData['overview'] = [];
  }

  // 主页面内容
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* 使用新的面包屑导航组件 */}
          <Breadcrumbs
            items={[
              { label: 'Home', path: '/' },
              { label: 'Tractors', path: '/tractors' },
              { label: brand.toUpperCase(), path: `/brands/${brand}` },
              { label: model, path: `/tractors/${brand}/${model}`, isLast: true }
            ]}
          />

          {/* Display model as h1 heading with underscores removed */}
          <h1 className="text-3xl font-bold mt-4 mb-6">{model.replace(/_/g, ' ')}</h1>

          {/* 详细数据标签页 - 使用客户端组件处理标签页交互 */}
          <div>
            <TractorTabsNav
              availableTabs={availableTabs}
            />

            <TractorContent
              brand={brand}
              model={model}
              availableTabs={availableTabs}
              tabData={processedData}
            />
          </div>

          {/* 拖拉机相关产品推荐 */}
          <TractorPageAds
            brandSlug={brand}
            tractorModel={model}
            brandName={tractorData.brand || brand}
            modelName={tractorData.model_key || model}
            locale={locale}
          />
        </div>
      </main>

      <Footer />

      {/* 添加产品结构化数据 (暂时禁用) */}
      {/* {tractorData && (
        <>
          <BreadcrumbSchema
            items={generateBreadcrumbItems(`/tractors/${brand}/${model}`, locale, {
              home: tPage('breadcrumb.home'),
              tractors: tPage('breadcrumb.tractors'),
              brands: tPage('breadcrumb.brands')
            })}
          />

          <ProductSchema
            {...generateTractorProductSchema({
              brand_name: tractorData.brand,
              model_name: tractorData.model_key,
              brand_key: brand,
              model_key: model,
              description: tractorData.description,
              image_url: tractorData.modelPhotos?.[0]?.url,
              ...tractorData
            }, locale)}
          />
        </>
      )} */}
    </div>
  );
}
