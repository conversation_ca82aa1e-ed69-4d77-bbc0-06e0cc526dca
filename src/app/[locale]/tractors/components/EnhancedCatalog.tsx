"use client";

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import Image from 'next/image';
import Link from 'next/link';
import type { TractorModel, PaginatedResult } from '@/services/modelService';
import { useTranslations } from 'next-intl';

interface EnhancedCatalogProps {
  categoryTitle?: string;
  tractorsData: PaginatedResult;
  initialFilters: {
    tractor_type?: string;
    tractor_brand?: string;
    brand_key?: string;
    power_min?: string;
    power_max?: string;
    year_start?: number | undefined;
    year_end?: number | undefined;
    factory?: string;
    search?: string;
    sort?: string;
    page?: number;
    limit?: number;
  };
  locale: string;
}

export function EnhancedCatalog({ 
  tractorsData,
  initialFilters,
  locale
}: EnhancedCatalogProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');
  const t = useTranslations('TractorsPage.catalog');
  
  // 排序选项
  const sortOptions = [
    { label: t('sortOptions.default'), value: 'default' },
    { label: t('sortOptions.power_asc'), value: 'power_asc' },
    { label: t('sortOptions.power_desc'), value: 'power_desc' },
    { label: t('sortOptions.year_desc'), value: 'year_desc' },
    { label: t('sortOptions.year_asc'), value: 'year_asc' }
  ];
  
  // 从URL获取初始排序值
  const initialSortValue = searchParams?.get('sort') || 'default';
  const [sortBy, setSortBy] = useState(initialSortValue);
  
  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    if (searchQuery) {
      params.set('search', searchQuery);
    } else {
      params.delete('search');
    }
    
    // 重置页码
    params.delete('page');
    
    router.push(`?${params.toString()}`);
  };
  
  // 处理排序变化
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortValue = e.target.value;
    setSortBy(newSortValue);
    
    // 更新URL
    const params = new URLSearchParams(searchParams?.toString() || '');
    if (newSortValue !== 'default') {
      params.set('sort', newSortValue);
    } else {
      params.delete('sort');
    }
    router.push(`?${params.toString()}`);
  };
  
  // 根据筛选条件构建标题
  const buildFilterTitle = () => {
    let title = t('tractorTypes.all');
    const suffix = t('filterTitles.suffix');
    
    if (initialFilters.tractor_type === 'farm') {
      title = t('tractorTypes.farm');
    } else if (initialFilters.tractor_type === 'lawn') {
      title = t('tractorTypes.lawn');
    }
    
    if (initialFilters.tractor_brand) {
      title = `${initialFilters.tractor_brand} ${title}`;
    }
    
    if (initialFilters.power_min || initialFilters.power_max) {
      let powerRange = '';
      if (initialFilters.power_min && initialFilters.power_max) {
        powerRange = t('filterTitles.powerRange', {
          min: initialFilters.power_min,
          max: initialFilters.power_max
        });
      } else if (initialFilters.power_min) {
        powerRange = t('filterTitles.powerMin', {
          min: initialFilters.power_min
        });
      } else if (initialFilters.power_max) {
        powerRange = t('filterTitles.powerMax', {
          max: initialFilters.power_max
        });
      }
      
      title = `${t('filterTitles.prefix')}${powerRange} ${title}`;
    }
    
    if (initialFilters.search) {
      title = `${t('filterTitles.searchQuery', { query: initialFilters.search })} ${title}`;
    }
    
    return title;
  };

  return (
    <div>
      {/* 标题和搜索栏 */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4 md:mb-0">
          {buildFilterTitle()}
        </h1>
        
        <form onSubmit={handleSearch} className="w-full md:w-auto flex">
          <div className="relative flex-grow max-w-md">
            <input
              type="text"
              placeholder={t('searchPlaceholder')}
              className="w-full py-2 pl-10 pr-4 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-tractor focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <button
            type="submit"
            className="bg-tractor hover:bg-tractor/90 text-white py-2 px-4 rounded-r-md"
          >
            {t('searchButton')}
          </button>
        </form>
      </div>

      {/* 结果统计和排序控件 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <p className="text-sm text-gray-500 mb-4 sm:mb-0">
          <span dangerouslySetInnerHTML={{
            __html: t.raw('showingResults').replace(
              '{count}', 
              `<span class="font-medium">${tractorsData.tractors.length}</span>`
            ).replace(
              '{total}', 
              `<span class="font-medium">${tractorsData.pagination.total}</span>`
            )
          }} />
        </p>
        
        <div className="flex items-center">
          <label htmlFor="sort" className="mr-2 text-sm text-gray-600">{t('sortLabel')}</label>
          <select
            id="sort"
            className="border border-gray-300 rounded py-1 px-2 text-sm focus:outline-none focus:ring-2 focus:ring-tractor focus:border-transparent"
            value={sortBy}
            onChange={handleSortChange}
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 拖拉机列表 */}
      {tractorsData.tractors.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tractorsData.tractors.map((tractor: TractorModel) => (
            <TractorCard 
              key={tractor.model_key} 
              tractor={tractor} 
              locale={locale} 
              brandSlug={initialFilters.brand_key || tractor.brand_key || ''} 
            />
          ))}
        </div>
      ) : (
        <div className="bg-gray-50 p-8 rounded-lg text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('noResults')}</h3>
          <p className="text-gray-500">
            {t('noResultsHelp')}
          </p>
        </div>
      )}

      {/* 分页 */}
      {tractorsData.pagination.pages > 1 && (
        <div className="mt-8">
          <Pagination className="mt-10">
            <PaginationContent>
              {/* 上一页按钮 */}
              <PaginationItem>
                {tractorsData.pagination.page > 1 ? (
                  <PaginationPrevious 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: (tractorsData.pagination.page - 1).toString()
                    })}`} 
                  />
                ) : (
                  <PaginationPrevious className="pointer-events-none opacity-50" />
                )}
              </PaginationItem>
              
              {/* 第一页 */}
              {tractorsData.pagination.page > 2 && (
                <PaginationItem>
                  <PaginationLink 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: '1'
                    })}`}
                  >
                    1
                  </PaginationLink>
                </PaginationItem>
              )}
              
              {/* 省略号 */}
              {tractorsData.pagination.page > 3 && (
                <PaginationItem>
                  <span className="px-4 py-2">...</span>
                </PaginationItem>
              )}
              
              {/* 当前页前一页 */}
              {tractorsData.pagination.page > 1 && (
                <PaginationItem>
                  <PaginationLink 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: (tractorsData.pagination.page - 1).toString()
                    })}`}
                  >
                    {tractorsData.pagination.page - 1}
                  </PaginationLink>
                </PaginationItem>
              )}
              
              {/* 当前页 */}
              <PaginationItem>
                <PaginationLink 
                  href={`?${new URLSearchParams({
                    ...Object.fromEntries(
                      Object.entries(initialFilters)
                        .filter(([key, value]) => value !== undefined && key !== 'page')
                    ),
                    page: tractorsData.pagination.page.toString()
                  })}`} 
                  isActive
                >
                  {tractorsData.pagination.page}
                </PaginationLink>
              </PaginationItem>
              
              {/* 当前页后一页 */}
              {tractorsData.pagination.page < tractorsData.pagination.pages && (
                <PaginationItem>
                  <PaginationLink 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: (tractorsData.pagination.page + 1).toString()
                    })}`}
                  >
                    {tractorsData.pagination.page + 1}
                  </PaginationLink>
                </PaginationItem>
              )}
              
              {/* 省略号 */}
              {tractorsData.pagination.page < tractorsData.pagination.pages - 2 && (
                <PaginationItem>
                  <span className="px-4 py-2">...</span>
                </PaginationItem>
              )}
              
              {/* 最后一页 */}
              {tractorsData.pagination.page < tractorsData.pagination.pages - 1 && (
                <PaginationItem>
                  <PaginationLink 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: tractorsData.pagination.pages.toString()
                    })}`}
                  >
                    {tractorsData.pagination.pages}
                  </PaginationLink>
                </PaginationItem>
              )}
              
              {/* 下一页按钮 */}
              <PaginationItem>
                {tractorsData.pagination.page < tractorsData.pagination.pages ? (
                  <PaginationNext 
                    href={`?${new URLSearchParams({
                      ...Object.fromEntries(
                        Object.entries(initialFilters)
                          .filter(([key, value]) => value !== undefined && key !== 'page')
                      ),
                      page: (tractorsData.pagination.page + 1).toString()
                    })}`} 
                  />
                ) : (
                  <PaginationNext className="pointer-events-none opacity-50" />
                )}
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}

// 拖拉机卡片组件
function TractorCard({ 
  tractor, 
  locale, 
  brandSlug 
}: { 
  tractor: TractorModel; 
  locale: string; 
  brandSlug: string;
}) {
  const t = useTranslations('TractorsPage.catalog');
  
  // 提取年份范围
  const yearMatch = tractor.years_range?.match(/(\d{4})\s*-\s*(\d{4})/) || [];
  const startYear = yearMatch[1] || '';
  const endYear = yearMatch[2] || '';
  
  // 判断是否有图片
  const hasImage = tractor.photos && Array.isArray(tractor.photos) && tractor.photos.length > 0;
  const imageUrl = hasImage ? tractor.photos![0].url : '';
  
  // 构建详情页链接
  const detailUrl = `/${locale}/tractors/${tractor.brand_key}/${tractor.model_key}`;
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow overflow-hidden">
      <Link href={detailUrl}>
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-100">
          {hasImage ? (
            <Image
              src={imageUrl}
              alt={tractor.model_name}
              className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
              width={600}
              height={400}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-300">
              <Image
                  src="/logo.svg"
                  alt={t('noImage')}
                  width={200}
                  height={120}
                  className="w-1/2 h-auto"
              />
            </div>
          )}
          <div className="absolute top-0 right-0 bg-tractor text-white px-2 py-1 text-xs font-medium">
            {tractor.tractor_type === 'farm' ? t('tractorTypes.farm') : t('tractorTypes.lawn')}
          </div>
        </div>
        
        <div className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">
              {tractor.tractor_brand} {tractor.model_name}
              </h3>
              <p className="text-sm text-gray-500 mb-2">
                {tractor.tractor_brand}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-tractor">
                {tractor.power} HP
              </p>
              <p className="text-xs text-gray-500">
                {startYear}{endYear ? `-${endYear}` : ''}
              </p>
            </div>
          </div>
          
          <div className="mt-3 flex flex-wrap gap-1">
            {tractor.factory && (
              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
                {tractor.factory}
              </span>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
}

// 搜索图标
function SearchIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
      className={className}
    >
      <path
        fillRule="evenodd"
        d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
        clipRule="evenodd"
      />
    </svg>
  );
}
