"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// Types for our data
type TractorBrand = {
  id: string;
  name: string;
  tractorCount: number;
  yearRange: string;
  hpRange: string;
  imageUrl: string;
};

// Custom components for our catalog
function SortControls({
  sortOption,
  setSortOption,
  viewMode,
  setViewMode
}: {
  sortOption: string;
  setSortOption: (option: string) => void;
  viewMode: 'grid' | 'list';
  setViewMode: (mode: 'grid' | 'list') => void;
}) {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div className="flex items-center space-x-2">
        <label htmlFor="sort" className="text-sm font-medium text-gray-700">
          Sort by:
        </label>
        <select
          id="sort"
          value={sortOption}
          onChange={(e) => setSortOption(e.target.value)}
          className="rounded-md border-gray-300 py-1.5 text-gray-700 focus:ring-green-500 focus:border-green-500 text-sm"
        >
          <option value="name">Name (A-Z)</option>
          <option value="name-desc">Name (Z-A)</option>
          <option value="tractors">Number of Tractors</option>
          <option value="hp">Horsepower Range</option>
          <option value="year">Year Range</option>
        </select>
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={() => setViewMode('grid')}
          className={`p-2 rounded-md ${viewMode === 'grid' 
            ? 'bg-green-100 text-green-700' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
          aria-label="Grid view"
        >
          <GridIcon />
        </button>
        <button
          onClick={() => setViewMode('list')}
          className={`p-2 rounded-md ${viewMode === 'list' 
            ? 'bg-green-100 text-green-700' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}
          aria-label="List view"
        >
          <ListIcon />
        </button>
      </div>
    </div>
  );
}

// Tractor brand card component
function TractorBrandCard({
  brand,
  viewMode
}: {
  brand: TractorBrand;
  viewMode: 'grid' | 'list';
}) {
  return (
    <Link
      href={`/brands/${brand.id}`}
      className={`block bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow overflow-hidden ${
        viewMode === 'grid' ? '' : 'flex'
      }`}
    >
      <div className={`relative overflow-hidden ${viewMode === 'grid' ? 'h-48' : 'w-1/3'}`}>
        <BrandTractorIcon
          className="w-full h-full"
          color={brand.id === 'john-deere' ? '#367C2B' :
                 brand.id === 'case-ih' ? '#CC0000' :
                 brand.id === 'new-holland' ? '#003399' :
                 brand.id === 'kubota' ? '#EF5122' :
                 brand.id === 'massey-ferguson' ? '#CC0000' :
                 brand.id === 'ford' ? '#0066CC' :
                 brand.id === 'international' ? '#CC0000' :
                 brand.id === 'agco' ? '#333333' :
                 '#22A730'}
        />
        <div className="absolute bottom-0 right-0 bg-green-600 text-white px-2 py-1 text-xs font-semibold">
          {brand.tractorCount} models
        </div>
      </div>

      <div className={`p-4 ${viewMode === 'list' ? 'w-2/3' : ''}`}>
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{brand.name}</h3>

        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
          <div className="flex items-center">
            <CalendarIcon className="w-4 h-4 mr-1 text-gray-500" />
            <span>{brand.yearRange}</span>
          </div>

          <div className="flex items-center">
            <GaugeIcon className="w-4 h-4 mr-1 text-gray-500" />
            <span>{brand.hpRange}</span>
          </div>
        </div>

        <div className={`mt-4 ${viewMode === 'grid' ? '' : 'flex items-center justify-between'}`}>
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
            brand.tractorCount > 100 
              ? 'bg-green-100 text-green-800' 
              : brand.tractorCount > 50 
                ? 'bg-yellow-100 text-yellow-800' 
                : 'bg-blue-100 text-blue-800'
          }`}>
            {brand.tractorCount} {brand.tractorCount === 1 ? 'tractor' : 'tractors'}
          </span>

          {viewMode === 'list' && (
            <span className="text-green-600 text-sm font-medium flex items-center">
              View details
              <ChevronRightIcon className="w-4 h-4 ml-1" />
            </span>
          )}
        </div>
      </div>
    </Link>
  );
}

// Pagination component
function Pagination({
  currentPage,
  totalPages,
  onPageChange
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) {
  return (
    <div className="flex items-center justify-between mt-8 border-t border-gray-200 pt-6">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`px-4 py-2 text-sm font-medium rounded-md ${
          currentPage === 1
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        <div className="flex items-center">
          <ChevronLeftIcon className="w-4 h-4 mr-1" />
          Previous
        </div>
      </button>

      <div className="hidden sm:flex items-center space-x-1">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              currentPage === page
                ? 'bg-green-50 text-green-600 border border-green-200'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            {page}
          </button>
        ))}
      </div>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`px-4 py-2 text-sm font-medium rounded-md ${
          currentPage === totalPages
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        <div className="flex items-center">
          Next
          <ChevronRightIcon className="w-4 h-4 ml-1" />
        </div>
      </button>
    </div>
  );
}

// Icon components
function GridIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3 3H7.5V7.5H3V3ZM3 10.5H7.5V15H3V10.5ZM10.5 3H15V7.5H10.5V3ZM10.5 10.5H15V15H10.5V10.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function ListIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3 4.5H15M3 9H15M3 13.5H15"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function CalendarIcon({ className = "" }) {
  return (
    <svg className={className} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 2.5H3C2.44772 2.5 2 2.94772 2 3.5V13.5C2 14.0523 2.44772 14.5 3 14.5H13C13.5523 14.5 14 14.0523 14 13.5V3.5C14 2.94772 13.5523 2.5 13 2.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M11 1.5V3.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M5 1.5V3.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M2 5.5H14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
}

function GaugeIcon({ className = "" }) {
  return (
    <svg className={className} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M8 5V8L10 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
}

function ChevronRightIcon({ className = "" }) {
  return (
    <svg className={className} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 12L10 8L6 4"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function ChevronLeftIcon({ className = "" }) {
  return (
    <svg className={className} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 12L6 8L10 4"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

// Mock data for tractor brands
// TractorIcon component for brand image placeholders
function BrandTractorIcon({ className = "", color = "#22c55e" }) {
  return (
    <div className={`${className} flex items-center justify-center bg-gray-100`}>
      <svg
        width="80%"
        height="80%"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7 12H17M17 12C17 13.6569 15.6569 15 14 15C12.3431 15 11 13.6569 11 12M17 12V8H14M11 12C11 10.3431 9.65685 9 8 9C6.34315 9 5 10.3431 5 12M11 12H5M5 12C5 13.6569 6.34315 15 8 15C9.65685 15 11 13.6569 11 12M14 8L12 4H6L7 8M14 8H7"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}

const mockTractorBrands: TractorBrand[] = [
  {
    id: 'john-deere',
    name: 'John Deere',
    tractorCount: 215,
    yearRange: '1918-2023',
    hpRange: '14-640 hp',
    imageUrl: '/brand-john-deere.svg'
  },
  {
    id: 'case-ih',
    name: 'Case IH',
    tractorCount: 176,
    yearRange: '1985-2023',
    hpRange: '24-620 hp',
    imageUrl: '/brand-case.svg'
  },
  {
    id: 'new-holland',
    name: 'New Holland',
    tractorCount: 142,
    yearRange: '1895-2023',
    hpRange: '18-620 hp',
    imageUrl: '/brand-new-holland.svg'
  },
  {
    id: 'kubota',
    name: 'Kubota',
    tractorCount: 128,
    yearRange: '1969-2023',
    hpRange: '13-170 hp',
    imageUrl: '/brand-kubota.svg'
  },
  {
    id: 'massey-ferguson',
    name: 'Massey Ferguson',
    tractorCount: 112,
    yearRange: '1953-2023',
    hpRange: '19-400 hp',
    imageUrl: '/brand-massey.svg'
  },
  {
    id: 'ford',
    name: 'Ford',
    tractorCount: 98,
    yearRange: '1917-2000',
    hpRange: '9-360 hp',
    imageUrl: '/brand-ford.svg'
  },
  {
    id: 'international',
    name: 'International',
    tractorCount: 87,
    yearRange: '1906-1985',
    hpRange: '10-350 hp',
    imageUrl: '/brand-international.svg'
  },
  {
    id: 'agco',
    name: 'AGCO',
    tractorCount: 76,
    yearRange: '1990-2023',
    hpRange: '50-646 hp',
    imageUrl: '/brand-agco.svg'
  },
  {
    id: 'mahindra',
    name: 'Mahindra',
    tractorCount: 68,
    yearRange: '1963-2023',
    hpRange: '19-155 hp',
    imageUrl: '/brand-mahindra.svg'
  },
];

// Main catalog component
export function FarmTractorCatalog() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortOption, setSortOption] = useState('name');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const totalPages = Math.ceil(mockTractorBrands.length / itemsPerPage);

  // Sort brands based on selected option
  const sortedBrands = [...mockTractorBrands].sort((a, b) => {
    switch (sortOption) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'name-desc':
        return b.name.localeCompare(a.name);
      case 'tractors':
        return b.tractorCount - a.tractorCount;
      case 'hp':
        return parseInt(b.hpRange.split('-')[1]) - parseInt(a.hpRange.split('-')[1]);
      case 'year':
        return parseInt(b.yearRange.split('-')[1]) - parseInt(a.yearRange.split('-')[1]);
      default:
        return 0;
    }
  });

  // Paginate brands
  const paginatedBrands = sortedBrands.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div>
      <SortControls
        sortOption={sortOption}
        setSortOption={setSortOption}
        viewMode={viewMode}
        setViewMode={setViewMode}
      />

      <div className={`grid ${viewMode === 'grid' 
        ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' 
        : 'grid-cols-1'} gap-6`}
      >
        {paginatedBrands.map((brand) => (
          <TractorBrandCard
            key={brand.id}
            brand={brand}
            viewMode={viewMode}
          />
        ))}
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />
    </div>
  );
}
