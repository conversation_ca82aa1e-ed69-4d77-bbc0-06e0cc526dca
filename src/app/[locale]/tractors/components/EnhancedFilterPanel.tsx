"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import type { FilterOptions } from '@/services/modelService';

// 筛选组类型定义
type FilterGroup = {
  id: string;
  name: string;
  type: 'checkbox' | 'radio' | 'range';
  options?: { id: string; label: string; value: string }[];
  rangeMin?: number;
  rangeMax?: number;
  rangeStep?: number;
};

interface EnhancedFilterPanelProps {
  initialFilters: {
    tractor_type?: string;
    tractor_brand?: string;
    brand_key?: string;
    power_min?: string;
    power_max?: string;
    year_start?: number | undefined;
    year_end?: number | undefined;
    factory?: string;
    search?: string;
    sort?: string;
    page?: number;
    limit?: number;
  };
  filterOptions: FilterOptions;
}

export function EnhancedFilterPanel({ initialFilters, filterOptions }: EnhancedFilterPanelProps) {
  const t = useTranslations('Filter');
  const tractorT = useTranslations('TractorsPage');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [activeCount, setActiveCount] = useState<number>(0);
  const [tractorFilters, setTractorFilters] = useState<FilterGroup[]>([]);
  
  // 从URL初始化筛选器状态和筛选组
  useEffect(() => {
    // 构建动态筛选选项数据
    const buildFilterGroups = (): FilterGroup[] => {
      const filters: FilterGroup[] = [
        {
          id: 'tractor_type',
          name: tractorT('filters.tractorType'),
          type: 'radio',
          options: [
            { id: 'all', label: tractorT('catalog.tractorTypes.all'), value: 'all' },
            ...filterOptions.tractor_types.map(type => ({
              id: type,
              label: type === 'farm' ? tractorT('catalog.tractorTypes.farm') : tractorT('catalog.tractorTypes.lawn'),
              value: type
            }))
          ]
        }
      ];
      
      // 功率范围筛选
      filters.push({
        id: 'power',
        name: tractorT('filters.powerRange'),
        type: 'range',
        rangeMin: parseInt(filterOptions.power_range.min) || 0,
        rangeMax: parseInt(filterOptions.power_range.max) || 650,
        rangeStep: 10
      });
      
      // 年份范围筛选
      filters.push({
        id: 'year',
        name: tractorT('filters.yearRange'),
        type: 'range',
        rangeMin: filterOptions.years_range.min,
        rangeMax: filterOptions.years_range.max,
        rangeStep: 1
      });
      
      return filters;
    };
    
    // 设置筛选组
    const generatedFilters = buildFilterGroups();
    setTractorFilters(generatedFilters);
    
    // 初始化筛选状态
    const initialState: Record<string, any> = {};
    let count = 0;
    
    // 设置初始值
    if (initialFilters.tractor_type) {
      initialState.tractor_type = initialFilters.tractor_type;
      if (initialFilters.tractor_type !== 'all') {
        count++;
      }
    }
    
    // 功率范围
    if (initialFilters.power_min || initialFilters.power_max) {
      const powerFilter = generatedFilters.find(f => f.id === 'power');
      initialState.power = {
        min: initialFilters.power_min ? parseInt(initialFilters.power_min) : powerFilter?.rangeMin,
        max: initialFilters.power_max ? parseInt(initialFilters.power_max) : powerFilter?.rangeMax
      };
      count++;
    }
    
    // 年份范围
    if (initialFilters.year_start || initialFilters.year_end) {
      const yearFilter = generatedFilters.find(f => f.id === 'year');
      initialState.year = {
        min: initialFilters.year_start || yearFilter?.rangeMin,
        max: initialFilters.year_end || yearFilter?.rangeMax
      };
      count++;
    }
    
    setFilters(initialState);
    setActiveCount(count);
  }, [initialFilters, filterOptions, tractorT]);
  
  // 处理筛选器变化
  const handleFilterChange = (filterId: string, value: any) => {
    const newFilters = { ...filters };
    
    // 如果值为空或默认值，则删除该筛选器
    if (value === undefined || value === null || 
        (Array.isArray(value) && value.length === 0)) {
      delete newFilters[filterId];
    } else {
      newFilters[filterId] = value;
      
      // 如果是拖拉机类型且值为"all"，则删除此筛选器
      if (filterId === 'tractor_type' && value === 'all') {
        delete newFilters[filterId];
      }
    }
    
    // 应用筛选器
    applyFilters(newFilters);
  };
  
  // 将筛选器应用到URL
  const applyFilters = (filterValues: Record<string, any>) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    // 清除所有现有筛选参数
    params.delete('tractor_type');
    params.delete('tractor_brand');
    params.delete('power_min');
    params.delete('power_max');
    params.delete('year_start');
    params.delete('year_end');
    params.delete('factory');
    
    // 类型筛选
    if (filterValues.tractor_type) {
      params.set('tractor_type', filterValues.tractor_type);
    }
    
    // 品牌筛选
    if (filterValues.tractor_brand) {
      params.set('tractor_brand', filterValues.tractor_brand);
    }
    
    // 功率范围
    if (filterValues.power) {
      const powerFilter = tractorFilters.find(f => f.id === 'power');
      if (filterValues.power.min !== powerFilter?.rangeMin) {
        params.set('power_min', filterValues.power.min.toString());
      }
      if (filterValues.power.max !== powerFilter?.rangeMax) {
        params.set('power_max', filterValues.power.max.toString());
      }
    }
    
    // 年份范围
    if (filterValues.year) {
      const yearFilter = tractorFilters.find(f => f.id === 'year');
      if (filterValues.year.min !== yearFilter?.rangeMin) {
        params.set('year_start', filterValues.year.min.toString());
      }
      if (filterValues.year.max !== yearFilter?.rangeMax) {
        params.set('year_end', filterValues.year.max.toString());
      }
    }
    
    // 保留搜索参数
    if (searchParams?.has('search')) {
      params.set('search', searchParams.get('search') || '');
    }
    
    // 重置页码
    params.delete('page');
    
    // 导航到新URL
    router.push(`?${params.toString()}`);
  };
  
  // 清除所有筛选器
  const clearAllFilters = () => {
    applyFilters({});
  };
  
  // 渲染筛选器组
  const renderFilterGroup = (filter: FilterGroup) => {
    switch (filter.type) {
      case 'checkbox':
        return (
          <CheckboxFilterGroup
            key={filter.id}
            id={filter.id}
            name={filter.name}
            options={filter.options || []}
            selectedValues={filters[filter.id] || []}
            onChange={(values) => handleFilterChange(filter.id, values)}
          />
        );
      case 'radio':
        return (
          <RadioFilterGroup
            key={filter.id}
            id={filter.id}
            name={filter.name}
            options={filter.options || []}
            selectedValue={filters[filter.id] || 'all'}
            onChange={(value) => handleFilterChange(filter.id, value)}
          />
        );
      case 'range':
        return (
          <RangeFilterGroup
            key={filter.id}
            id={filter.id}
            name={filter.name}
            min={filter.rangeMin || 0}
            max={filter.rangeMax || 100}
            step={filter.rangeStep || 1}
            values={filters[filter.id] || { min: filter.rangeMin, max: filter.rangeMax }}
            onChange={(values) => handleFilterChange(filter.id, values)}
          />
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900">{t('filters')}</h2>
        {activeCount > 0 && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-tractor hover:text-tractor/80 font-medium"
          >
            {t('clearAll')} ({activeCount})
          </button>
        )}
      </div>
      
      <div className="divide-y divide-gray-200">
        {tractorFilters.map(filter => (
          <div key={filter.id} className="px-4 py-4">
            {renderFilterGroup(filter)}
          </div>
        ))}
      </div>
    </div>
  );
}

// 复选框筛选组件
interface CheckboxFilterGroupProps {
  id: string;
  name: string;
  options: { id: string; label: string; value: string }[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
}

function CheckboxFilterGroup({ id, name, options, selectedValues, onChange }: CheckboxFilterGroupProps) {
  const t = useTranslations('Filter');
  // 如果选项太多，限制初始显示数量
  const [expanded, setExpanded] = useState(false);
  const visibleOptions = expanded ? options : options.slice(0, 5);
  
  const handleChange = (value: string, checked: boolean) => {
    const newValues = checked
      ? [...selectedValues, value]
      : selectedValues.filter(val => val !== value);
    
    onChange(newValues);
  };
  
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-900 mb-2">{name}</h3>
      <div className="space-y-1">
        {visibleOptions.map(option => (
          <div key={option.id} className="flex items-center">
            <input
              id={`${id}-${option.id}`}
              name={`${id}-option`}
              type="checkbox"
              className="h-4 w-4 rounded border-gray-300 text-tractor focus:ring-tractor"
              checked={selectedValues.includes(option.value)}
              onChange={(e) => handleChange(option.value, e.target.checked)}
            />
            <label
              htmlFor={`${id}-${option.id}`}
              className="ml-2 text-sm text-gray-600"
            >
              {option.label}
            </label>
          </div>
        ))}
        
        {options.length > 5 && (
          <button
            type="button"
            className="mt-1 text-xs text-tractor hover:text-tractor/80 font-medium flex items-center"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? (
              <>
                <span>{t('showLess')}</span>
                <ChevronUpIcon className="ml-1 h-3 w-3" />
              </>
            ) : (
              <>
                <span>{t('showAll', { count: options.length })}</span>
                <ChevronDownIcon className="ml-1 h-3 w-3" />
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
}

// 单选按钮筛选组件
interface RadioFilterGroupProps {
  id: string;
  name: string;
  options: { id: string; label: string; value: string }[];
  selectedValue: string;
  onChange: (value: string) => void;
}

function RadioFilterGroup({ id, name, options, selectedValue, onChange }: RadioFilterGroupProps) {
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-900 mb-2">{name}</h3>
      <div className="space-y-1">
        {options.map(option => (
          <div key={option.id} className="flex items-center">
            <input
              id={`${id}-${option.id}`}
              name={id}
              type="radio"
              className="h-4 w-4 border-gray-300 text-tractor focus:ring-tractor"
              checked={selectedValue === option.value}
              onChange={() => onChange(option.value)}
            />
            <label
              htmlFor={`${id}-${option.id}`}
              className="ml-2 text-sm text-gray-600"
            >
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
}

// 范围筛选组件
interface RangeFilterGroupProps {
  id: string;
  name: string;
  min: number;
  max: number;
  step: number;
  values: { min: number; max: number };
  onChange: (values: { min: number; max: number }) => void;
}

function RangeFilterGroup({ id, name, min, max, step, values, onChange }: RangeFilterGroupProps) {
  const t = useTranslations('Filter');
  const [localValues, setLocalValues] = useState(values);
  const [isEditing, setIsEditing] = useState(false);
  const [inputValues, setInputValues] = useState({
    min: values.min.toString(),
    max: values.max.toString()
  });
  
  // 当外部值变化时更新本地状态
  useEffect(() => {
    setLocalValues(values);
    setInputValues({
      min: values.min.toString(),
      max: values.max.toString()
    });
  }, [values]);
  
  // 处理输入变更
  const handleInputChange = (type: 'min' | 'max', value: string) => {
    setIsEditing(true);
    setInputValues(prev => ({
      ...prev,
      [type]: value
    }));
  };
  
  // 处理数值变更（用于验证和应用值）
  const handleValueChange = (type: 'min' | 'max', inputValue: string) => {
    // 如果为空值，则使用默认范围值
    if (inputValue === '') {
      return type === 'min' ? min : max;
    }
    
    // 转换为数字
    const numValue = Number(inputValue);
    
    // 如果不是有效数字，则使用当前值
    if (isNaN(numValue)) {
      return localValues[type];
    }
    
    // 基本验证
    let newValue = Math.max(min, Math.min(max, numValue));
    
    // 确保最小值不超过最大值，最大值不小于最小值
    if (type === 'min' && newValue > localValues.max) {
      newValue = localValues.max;
    } else if (type === 'max' && newValue < localValues.min) {
      newValue = localValues.min;
    }
    
    return newValue;
  };
  
  // 应用筛选
  const handleApply = () => {
    const validatedValues = {
      min: handleValueChange('min', inputValues.min),
      max: handleValueChange('max', inputValues.max)
    };
    
    setLocalValues(validatedValues);
    setInputValues({
      min: validatedValues.min.toString(),
      max: validatedValues.max.toString()
    });
    setIsEditing(false);
    onChange(validatedValues);
  };
  
  // 处理失焦事件
  const handleBlur = () => {
    // 在失焦时自动应用更改
    handleApply();
  };
  
  // 处理回车键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApply();
    }
  };
  
  // 重置为默认范围
  const handleReset = () => {
    const defaultValues = { min, max };
    setLocalValues(defaultValues);
    setInputValues({
      min: min.toString(),
      max: max.toString()
    });
    setIsEditing(false);
    onChange(defaultValues);
  };
  
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-900 mb-2">{name}</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <label htmlFor={`${id}-min`} className="block text-xs text-gray-500">
            {t('minValue')}
          </label>
          <input
            type="text"
            inputMode="numeric"
            id={`${id}-min`}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-tractor focus:ring-tractor sm:text-sm px-3 py-2"
            placeholder={min.toString()}
            value={inputValues.min}
            onChange={(e) => handleInputChange('min', e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
          />
        </div>
        <div>
          <label htmlFor={`${id}-max`} className="block text-xs text-gray-500">
            {t('maxValue')}
          </label>
          <input
            type="text"
            inputMode="numeric"
            id={`${id}-max`}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-tractor focus:ring-tractor sm:text-sm px-3 py-2"
            placeholder={max.toString()}
            value={inputValues.max}
            onChange={(e) => handleInputChange('max', e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
          />
        </div>
      </div>
      
      {isEditing && (
        <div className="flex space-x-2">
          <button
            type="button"
            className="flex-1 py-1 px-2 bg-tractor text-white text-xs font-medium rounded hover:bg-tractor/90"
            onClick={handleApply}
          >
            {t('apply')}
          </button>
          <button
            type="button"
            className="flex-1 py-1 px-2 bg-gray-100 text-gray-600 text-xs font-medium rounded hover:bg-gray-200"
            onClick={handleReset}
          >
            {t('reset')}
          </button>
        </div>
      )}
    </div>
  );
}

// 图标组件
function ChevronDownIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
      className={className}
    >
      <path
        fillRule="evenodd"
        d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
        clipRule="evenodd"
      />
    </svg>
  );
}

function ChevronUpIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
      className={className}
    >
      <path
        fillRule="evenodd"
        d="M14.77 12.79a.75.75 0 01-1.06-.02L10 8.832 6.29 12.77a.75.75 0 11-1.08-1.04l4.25-4.5a.75.75 0 011.08 0l4.25 4.5a.75.75 0 01-.02 1.06z"
        clipRule="evenodd"
      />
    </svg>
  );
}
