"use client";

import { useState } from 'react';

// Custom filter section component for reusability
function FilterSection({ 
  title, 
  children 
}: { 
  title: string;
  children: React.ReactNode;
}) {
  const [isOpen, setIsOpen] = useState(true);
  
  return (
    <div className="mb-6 border-b pb-4">
      <div 
        className="flex justify-between items-center mb-3 cursor-pointer" 
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="font-semibold text-gray-800">{title}</h3>
        <ChevronIcon direction={isOpen ? 'down' : 'right'} />
      </div>
      {isOpen && <div className="space-y-2">{children}</div>}
    </div>
  );
}

// Custom checkbox component for consistent styling
function FilterCheckbox({ 
  id, 
  label, 
  count 
}: { 
  id: string;
  label: string;
  count: number;
}) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center">
        <input
          type="checkbox"
          id={id}
          className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"
        />
        <label htmlFor={id} className="ml-2 text-sm text-gray-600 cursor-pointer hover:text-green-700">
          {label}
        </label>
      </div>
      <span className="text-xs text-gray-500">{count}</span>
    </div>
  );
}

// Range slider component for horsepower selection
function RangeSlider({
  min,
  max,
  value,
  onChange
}: {
  min: number;
  max: number;
  value: [number, number];
  onChange: (value: [number, number]) => void;
}) {
  return (
    <div className="px-2 pt-6 pb-2">
      <div className="relative">
        <div className="h-1 bg-gray-200 rounded-full">
          <div 
            className="absolute h-1 bg-green-500 rounded-full"
            style={{
              left: `${((value[0] - min) / (max - min)) * 100}%`,
              right: `${100 - ((value[1] - min) / (max - min)) * 100}%`
            }}
          ></div>
        </div>
        
        <input
          type="range"
          min={min}
          max={max}
          value={value[0]}
          onChange={(e) => onChange([parseInt(e.target.value), value[1]])}
          className="absolute w-full h-1 appearance-none bg-transparent pointer-events-none"
        />
        
        <input
          type="range"
          min={min}
          max={max}
          value={value[1]}
          onChange={(e) => onChange([value[0], parseInt(e.target.value)])}
          className="absolute w-full h-1 appearance-none bg-transparent pointer-events-none"
        />
      </div>
      
      <div className="flex justify-between mt-4 text-sm text-gray-600">
        <span>{value[0]} hp</span>
        <span>{value[1]} hp</span>
      </div>
    </div>
  );
}

// ChevronIcon component
function ChevronIcon({ direction = 'down' }: { direction: 'down' | 'right' }) {
  return (
    <svg 
      width="16" 
      height="16" 
      viewBox="0 0 16 16" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={`transition-transform ${direction === 'down' ? 'rotate-0' : '-rotate-90'}`}
    >
      <path 
        d="M4 6L8 10L12 6" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
}

export function FilterPanel() {
  const [hpRange, setHpRange] = useState<[number, number]>([20, 500]);
  const [search, setSearch] = useState('');
  
  return (
    <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Filter Tractors</h2>
      
      {/* Search input */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search brands..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <SearchIcon />
          </div>
        </div>
      </div>
      
      {/* Horsepower range */}
      <FilterSection title="Horsepower">
        <RangeSlider 
          min={10} 
          max={600} 
          value={hpRange}
          onChange={setHpRange}
        />
      </FilterSection>
      
      {/* Years */}
      <FilterSection title="Year Range">
        <FilterCheckbox id="year-2020" label="2020 - Present" count={42} />
        <FilterCheckbox id="year-2010" label="2010 - 2019" count={78} />
        <FilterCheckbox id="year-2000" label="2000 - 2009" count={112} />
        <FilterCheckbox id="year-1990" label="1990 - 1999" count={94} />
        <FilterCheckbox id="year-1980" label="1980 - 1989" count={87} />
        <FilterCheckbox id="year-1970" label="1970 - 1979" count={65} />
        <FilterCheckbox id="year-older" label="Before 1970" count={156} />
      </FilterSection>
      
      {/* Popular brands */}
      <FilterSection title="Popular Brands">
        <FilterCheckbox id="brand-john-deere" label="John Deere" count={215} />
        <FilterCheckbox id="brand-case-ih" label="Case IH" count={176} />
        <FilterCheckbox id="brand-new-holland" label="New Holland" count={142} />
        <FilterCheckbox id="brand-kubota" label="Kubota" count={128} />
        <FilterCheckbox id="brand-massey-ferguson" label="Massey Ferguson" count={112} />
        <FilterCheckbox id="brand-ford" label="Ford" count={98} />
        <FilterCheckbox id="brand-international" label="International" count={87} />
      </FilterSection>
      
      {/* Reset button */}
      <button className="w-full py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
        Reset Filters
      </button>
    </div>
  );
}

// SearchIcon component
function SearchIcon() {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path 
        d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
      <path 
        d="M16 16L12.5 12.5" 
        stroke="currentColor" 
        strokeWidth="1.5" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      />
    </svg>
  );
}
