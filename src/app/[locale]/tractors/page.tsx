import { Metadata } from 'next';
import { Suspense } from 'react';
import { Header } from '../components/Header';
import { Footer } from '../components/Footer';
import { EnhancedCatalog } from './components/EnhancedCatalog';
import { EnhancedFilterPanel } from './components/EnhancedFilterPanel';
import { getTractorModels, getFilterOptions } from '@/services/modelService';
import { getTranslations } from 'next-intl/server';

type Props = {
  params: { locale: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // 等待params解析
  const resolvedParams = await Promise.resolve(params);
  const t = await getTranslations({ locale: resolvedParams.locale, namespace: 'TractorsPage.metadata' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords')
    // 移除硬编码的 alternates 配置，使用 layout 中的全局 hreflang 设置
  };
}

export interface TractorsPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
  params: { locale: string };
}

export default async function TractorsPage({ 
  searchParams,
  params 
}: TractorsPageProps) {
  // 等待params解析
  const resolvedParams = await Promise.resolve(params);
  const locale = resolvedParams.locale;
  
  // 获取翻译
  const t = await getTranslations('TractorsPage');
  
  // 确保 searchParams 是已解析的
  const resolvedSearchParams = await Promise.resolve(searchParams);
  
  // 解析查询参数
  const filters = {
    tractor_type: resolvedSearchParams.tractor_type?.toString(),
    tractor_brand: resolvedSearchParams.tractor_brand?.toString(),
    brand_key: resolvedSearchParams.brand_key?.toString(),
    power_min: resolvedSearchParams.power_min?.toString(),
    power_max: resolvedSearchParams.power_max?.toString(),
    year_start: resolvedSearchParams.year_start ? parseInt(resolvedSearchParams.year_start.toString()) : undefined,
    year_end: resolvedSearchParams.year_end ? parseInt(resolvedSearchParams.year_end.toString()) : undefined,
    search: resolvedSearchParams.search?.toString(),
    sort: resolvedSearchParams.sort?.toString(),
    page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page.toString()) : 1,
    limit: 20
  };

  // 获取拖拉机数据和筛选选项
  const tractorsData = await getTractorModels(filters);
  const filterOptions = await getFilterOptions();
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main>
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* 筛选面板 */}
            <div className="w-full lg:w-80 flex-shrink-0">
              <Suspense fallback={<div className="p-4 border rounded animate-pulse bg-gray-100">{t('loading.filterPanel')}</div>}>
                <EnhancedFilterPanel 
                  initialFilters={filters} 
                  filterOptions={filterOptions} 
                />
              </Suspense>
            </div>
            
            {/* 主要内容区域 */}
            <div className="flex-1">
              <Suspense fallback={<div className="p-4 border rounded animate-pulse bg-gray-100">{t('loading.catalog')}</div>}>
                <EnhancedCatalog 
                  categoryTitle={t('title')} 
                  tractorsData={tractorsData}
                  initialFilters={filters}
                  locale={locale}
                />
              </Suspense>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
