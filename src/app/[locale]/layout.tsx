import type { Metadata, Viewport } from "next";
import "./globals.css";
import {NextIntlClientProvider, hasLocale} from 'next-intl';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import { headers } from 'next/headers';
import JsonLd from '@/components/JsonLd';
import WebVitals from '@/components/WebVitals';
import Script from 'next/script';

const BASE_URL = 'https://tractordata.site';


// 生成本地化的元数据
export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;

  // 确保locale有效
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // 使用中间件捕获的当前路径
  const headersList = await headers();
  const currentPath = headersList.get('x-current-path') || '/';

  // 使用中间件捕获的准确路径来提取语言无关的部分
  let pathWithoutLocale: string;

  // 如果是默认语言，路径已经不包含语言前缀 (localePrefix: 'as-needed')
  if (locale === routing.defaultLocale) {
    // 对于默认语言，路径已经是正确的格式
    if (currentPath === '/') {
      pathWithoutLocale = '';
    } else {
      pathWithoutLocale = currentPath;
    }
  } else {
    // 对于非默认语言，需要去除语言前缀
    const localePrefix = `/${locale}`;

    if (currentPath === localePrefix) {
      // 如果当前路径就是语言前缀，返回根路径
      pathWithoutLocale = '';
    } else if (currentPath.startsWith(`${localePrefix}/`)) {
      // 如果当前路径包含语言前缀，移除该前缀
      pathWithoutLocale = currentPath.substring(localePrefix.length);
    } else {
      // 意外情况，保留原始路径
      pathWithoutLocale = currentPath;
    }
  }

  // 加载对应语言的翻译文件
  const messages = (await import(`../../../messages/${locale}.json`)).default;

  // 从翻译文件中获取首页元数据
  const homePageMetadata = messages.HomePage || {};

  // --- 生成 hreflang 元标签 ---
  const alternates: Metadata['alternates'] = {
    // 根据当前语言和 'as-needed' 策略设置规范链接
    canonical: locale === routing.defaultLocale
      ? `${BASE_URL}${pathWithoutLocale}`
      : `${BASE_URL}/${locale}${pathWithoutLocale}`,
    languages: {},
  };

  // 生成所有语言的替代链接
  for (const loc of routing.locales) {
    // 按 'as-needed' 策略处理 URL 格式
    if (loc === routing.defaultLocale) {
      // 默认语言不包含语言前缀
      alternates.languages![loc] = `${BASE_URL}${pathWithoutLocale}`;
    } else {
      // 非默认语言包含语言前缀
      alternates.languages![loc] = `${BASE_URL}/${loc}${pathWithoutLocale}`;
    }
  }

  // 添加 x-default 指向默认语言版本
  alternates.languages!['x-default'] = `${BASE_URL}${pathWithoutLocale}`;
  // --- 结束生成 hreflang ---

  return {
    // 设置 metadataBase 对解析相对路径很重要
    metadataBase: new URL(BASE_URL),
    title: homePageMetadata.metaTitle || "TractorData | Comprehensive Tractor Specifications and Information",
    description: homePageMetadata.metaDescription || "Find detailed specifications, information, and comparisons for all makes and models of farm tractors and lawn tractors.",
    keywords: homePageMetadata.metaKeywords || "tractors, farm equipment, agricultural machinery, tractor specs, John Deere, Kubota, farm tractors, lawn tractors",
    icons: {
      icon: "/logo.svg",
      apple: "/logo.svg",
      shortcut: "/logo.svg"
    },
    // 添加 alternates 对象
    alternates: alternates,
  };
}

// 定义视口设置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#2a5234'
};

export default async function LocaleLayout(
  {
    children,
    params
  }: {
    children: React.ReactNode;
    params: Promise<{locale: string}>;
}) {
    // Ensure that the incoming `locale` is valid
    const {locale} = await params;
    if (!hasLocale(routing.locales, locale)) {
        notFound();
    }

    // 获取对应语言的翻译消息
    const messages = (await import(`../../../messages/${locale}.json`)).default;

    // 网站结构化数据
    const websiteSchema = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "TractorData",
      "url": BASE_URL,
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${BASE_URL}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      },
      "inLanguage": locale
    };

    // 语言选择器结构化数据 (暂时禁用)
    // const languageSelectorSchema = generateLanguageSelectorData(currentPath || '/', locale);

    return (
        <html lang={locale}>
        <head>
          {/* Google Analytics */}
          <Script
            src="https://www.googletagmanager.com/gtag/js?id=G-D2VTNZCKD1"
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-D2VTNZCKD1');
            `}
          </Script>
          <Script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7932958206248396"
            crossOrigin="anonymous">
          </Script>
        </head>
        <body className="antialiased">
          <NextIntlClientProvider locale={locale} messages={messages}>
            {children}
          </NextIntlClientProvider>
          <JsonLd data={websiteSchema} />
          {/* 暂时禁用语言选择器结构化数据 */}
          {/* <JsonLd data={languageSelectorSchema} /> */}
          <WebVitals debug={process.env.NODE_ENV === 'development'} />
        </body>
        </html>
    );
}
