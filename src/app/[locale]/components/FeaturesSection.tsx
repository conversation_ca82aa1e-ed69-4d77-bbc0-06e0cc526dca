export function FeaturesSection() {
  const features = [
    {
      icon: <SearchIcon />,
      title: "全面搜索",
      description: "快速查找任何拖拉机型号的详细规格和信息"
    },
    {
      icon: <CompareIcon />,
      title: "规格对比",
      description: "并排比较不同拖拉机型号的性能指标和特性"
    },
    {
      icon: <PhotoIcon />,
      title: "高清图片库",
      description: "浏览各种拖拉机型号的详细高清图片集"
    },
    {
      icon: <DataIcon />,
      title: "技术数据",
      description: "访问发动机、液压系统等详细技术参数"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-3">为什么选择我们的拖拉机数据库</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">全面的拖拉机资源，助您快速找到所需的技术规格和信息</p>
          <div className="w-16 h-1 bg-green-600 mx-auto mt-4"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="bg-gray-50 rounded-xl p-6 text-center hover:shadow-md transition-shadow border border-gray-100"
            >
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-5 text-green-700">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Icons
function SearchIcon() {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-7 w-7" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
      />
    </svg>
  );
}

function CompareIcon() {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-7 w-7" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" 
      />
    </svg>
  );
}

function PhotoIcon() {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-7 w-7" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
      />
    </svg>
  );
}

function DataIcon() {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-7 w-7" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
      />
    </svg>
  );
}
