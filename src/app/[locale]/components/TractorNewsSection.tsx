import { Link } from '@/i18n/navigation';
import type { NewsArticle } from '@/services/newsService';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { memo } from 'react';

interface TractorNewsSectionProps {
  news: NewsArticle[];
}

// 使用 memo 优化组件，防止不必要的重渲染
export const TractorNewsSection = memo(function TractorNewsSection({ news }: TractorNewsSectionProps) {
  const t = useTranslations('TractorNewsSection');
  
  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">{t('subtitle')}</p>
          <div className="w-16 h-1 bg-tractor mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {news.map(article => (
            <article
              key={article._id}
              className="group bg-white border border-gray-200 rounded-lg hover:border-tractor hover:shadow-md transition-all duration-300 p-6 flex flex-col h-full"
            >
              <div className="flex-grow">
                {article.date && (
                  <div className="text-xs text-gray-500 mb-2">{article.date}</div>
                )}
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-tractor transition-colors line-clamp-2">
                  <Link href={`/news/${article.slug}`} className="hover:underline" prefetch={false}>
                    {article.title}
                  </Link>
                </h3>
                <p className="text-gray-700 text-sm line-clamp-3 mb-4">{article.summary}</p>
              </div>
              
              {article.images && article.images.length > 0 && (
                <div className="mt-3 mb-3 overflow-hidden rounded-md relative h-40">
                  <Image 
                    src={article.images[0].url} 
                    alt={article.images[0].alt || article.title}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                    loading="lazy"
                    quality={65}
                    unoptimized
                  />
                </div>
              )}
              
              <Link
                href={`/news/${article.slug}`}
                className="inline-flex items-center text-tractor font-medium hover:underline text-sm mt-2"
                prefetch={false}
              >
                {t('readMore')}
                <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Link>
            </article>
          ))}
        </div>

        <div className="text-center mt-8">
          <Link
            href="/news"
            className="inline-flex items-center justify-center bg-white border border-tractor hover:bg-tractor/10 text-tractor font-medium px-6 py-3 rounded-lg transition-colors shadow-sm hover:shadow"
            prefetch={true}
          >
            {t('viewAllNews')}
            <svg className="ml-2 w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
});
