import Link from 'next/link';

export function CompareFeature() {
  return (
    <section className="py-12 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Compare Tractors Side by Side</h2>
          <p className="text-gray-300 mb-8">
            Get detailed comparisons of specifications, features, and performance metrics to help you make informed decisions.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <CompareIcon className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <h3 className="font-semibold text-xl mb-2">Farm Tractors</h3>
              <p className="text-gray-400 mb-4">
                Compare horsepower, dimensions, hydraulics, PTO, and more across all farm tractor brands.
              </p>
              <Link 
                href="/compare/farm-tractors"
                className="inline-block bg-green-700 hover:bg-green-600 transition-colors px-5 py-2 rounded-lg font-medium"
              >
                Compare Farm Tractors
              </Link>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <CompareIcon className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <h3 className="font-semibold text-xl mb-2">Lawn Tractors</h3>
              <p className="text-gray-400 mb-4">
                Compare engine specs, cutting width, transmission, and features across all lawn tractor brands.
              </p>
              <Link 
                href="/compare/lawn-tractors"
                className="inline-block bg-green-700 hover:bg-green-600 transition-colors px-5 py-2 rounded-lg font-medium"
              >
                Compare Lawn Tractors
              </Link>
            </div>
          </div>
          
          <Link 
            href="/compare"
            className="text-green-400 hover:text-green-300 font-medium inline-flex items-center"
          >
            Learn more about our comparison tools
            <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}

function CompareIcon({ className = "w-6 h-6" }) {
  return (
    <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path 
        d="M9 15H3M15 15H21M15 15V3M15 15V21M9 15V21M9 15V9M9 9H3M9 9V3" 
        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
      />
    </svg>
  );
}
