"use client";

import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  maxVisibleButtons?: number;
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  maxVisibleButtons = 5,
}: PaginationProps) {
  // 如果只有一页，不显示分页
  if (totalPages <= 1) return null;

  // 确保当前页码在有效范围内
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));

  // 计算需要显示的页码按钮
  const getVisiblePageNumbers = () => {
    // 如果总页数小于等于最大可见按钮数，直接显示所有页码
    if (totalPages <= maxVisibleButtons) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // 计算显示范围
    const halfVisibleButtons = Math.floor(maxVisibleButtons / 2);
    let startPage = Math.max(validCurrentPage - halfVisibleButtons, 1);
    let endPage = Math.min(startPage + maxVisibleButtons - 1, totalPages);

    // 如果结束页码超出总页数，调整开始页码
    if (endPage - startPage + 1 < maxVisibleButtons) {
      startPage = Math.max(endPage - maxVisibleButtons + 1, 1);
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  };

  const visiblePageNumbers = getVisiblePageNumbers();

  // 页码按钮样式
  const getButtonStyle = (page: number) => {
    return page === validCurrentPage
      ? 'z-10 bg-tractor text-white hover:bg-tractor/90'
      : 'bg-white text-gray-500 hover:bg-gray-50';
  };

  return (
    <nav className="flex justify-center">
      <ul className="inline-flex -space-x-px shadow-sm">
        {/* 上一页按钮 */}
        <li>
          <button
            onClick={() => onPageChange(validCurrentPage - 1)}
            disabled={validCurrentPage === 1}
            className={`relative px-3 py-2 rounded-l-md border ${
              validCurrentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-500 hover:bg-gray-50'
            } text-sm font-medium`}
            aria-label="上一页"
          >
            <ChevronLeftIcon className="h-5 w-5" />
          </button>
        </li>

        {/* 第一页按钮（当不显示时） */}
        {visiblePageNumbers[0] > 1 && (
          <>
            <li>
              <button
                onClick={() => onPageChange(1)}
                className={`relative px-3 py-2 border bg-white text-gray-500 hover:bg-gray-50 text-sm font-medium`}
              >
                1
              </button>
            </li>
            {visiblePageNumbers[0] > 2 && (
              <li>
                <span className="relative px-3 py-2 inline-flex items-center justify-center border bg-white text-gray-500 text-sm">
                  ...
                </span>
              </li>
            )}
          </>
        )}

        {/* 页码按钮 */}
        {visiblePageNumbers.map((page) => (
          <li key={page}>
            <button
              onClick={() => onPageChange(page)}
              className={`relative px-3 py-2 border ${getButtonStyle(
                page
              )} text-sm font-medium`}
            >
              {page}
            </button>
          </li>
        ))}

        {/* 最后一页按钮（当不显示时） */}
        {visiblePageNumbers[visiblePageNumbers.length - 1] < totalPages && (
          <>
            {visiblePageNumbers[visiblePageNumbers.length - 1] < totalPages - 1 && (
              <li>
                <span className="relative px-3 py-2 inline-flex items-center justify-center border bg-white text-gray-500 text-sm">
                  ...
                </span>
              </li>
            )}
            <li>
              <button
                onClick={() => onPageChange(totalPages)}
                className={`relative px-3 py-2 border bg-white text-gray-500 hover:bg-gray-50 text-sm font-medium`}
              >
                {totalPages}
              </button>
            </li>
          </>
        )}

        {/* 下一页按钮 */}
        <li>
          <button
            onClick={() => onPageChange(validCurrentPage + 1)}
            disabled={validCurrentPage === totalPages}
            className={`relative px-3 py-2 rounded-r-md border ${
              validCurrentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-500 hover:bg-gray-50'
            } text-sm font-medium`}
            aria-label="下一页"
          >
            <ChevronRightIcon className="h-5 w-5" />
          </button>
        </li>
      </ul>
    </nav>
  );
}

function ChevronLeftIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
      className={className}
    >
      <path
        fillRule="evenodd"
        d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
        clipRule="evenodd"
      />
    </svg>
  );
}

function ChevronRightIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
      className={className}
    >
      <path
        fillRule="evenodd"
        d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
        clipRule="evenodd"
      />
    </svg>
  );
}
