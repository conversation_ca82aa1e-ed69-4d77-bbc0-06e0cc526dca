import { TractorBrandCard } from './TractorBrandCard';
import { ViewAllCard } from './ViewAllCard';
import { SimpleBrand } from '@/services/brandService';
import { useTranslations } from 'next-intl';

interface TractorBrandsSectionProps {
  title: string;
  subtitle?: string;
  brands: SimpleBrand[];
  type: 'farm' | 'lawn';
}

export function TractorBrandsSection({ title, subtitle, brands, type }: TractorBrandsSectionProps) {
  const t = useTranslations('TractorBrandsSection');
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-tractor/10 overflow-hidden">
      <div className="px-6 py-4 border-b border-tractor/10">
        <h2 className="text-xl font-semibold text-tractor">{t(title)}</h2>
      </div>
      
      <div className="divide-y divide-tractor/10 px-6">
        {brands.map(brand => (
          <TractorBrandCard 
            key={brand.slug}
            name={brand.brand_name || ''}
            slug={brand.slug}
            imageSrc={brand.brand_logo || `https://picsum.photos/id/${Math.abs(brand.slug.split('').reduce((a, b) => {
              a = ((a << 5) - a) + b.charCodeAt(0);
              return a & a;
            }, 0)) % 1000}/100/80`}
            type={type}
          />
        ))}
      </div>
      
      <div className="px-6">
        <ViewAllCard type={type} />
      </div>
    </div>
  );
}
