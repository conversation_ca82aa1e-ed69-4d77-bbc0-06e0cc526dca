import React from 'react';

interface StatItemProps {
  count: string;
  label: string;
}

function StatItem({ count, label }: StatItemProps) {
  return (
    <div className="text-center px-4">
      <div className="text-3xl md:text-4xl font-bold text-white mb-1">{count}</div>
      <div className="text-sm md:text-base text-green-100">{label}</div>
    </div>
  );
}

export function StatsSection() {
  return (
    <section className="bg-green-800 py-8 border-t border-green-700">
      <div className="container mx-auto">
        <div className="flex flex-wrap justify-center">
          <StatItem count="8,500+" label="Tractor Models" />
          <StatItem count="285" label="Manufacturers" />
          <StatItem count="145,000+" label="Specifications" />
          <StatItem count="1.2M+" label="Monthly Visitors" />
        </div>
      </div>
    </section>
  );
}
