import React from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';

export function HeroSection() {
  const t = useTranslations('HeroSection');

  return (
    <section className="relative bg-gradient-to-r from-[#2a5234] to-[rgba(42,82,52,0.9)] text-white py-12 md:py-16">
      {/* Background Image with Overlay - 优化加载 */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <Image
          src="https://picsum.photos/id/1045/1200/600" 
          alt={t('backgroundAlt')}
          fill
          sizes="100vw"
          quality={70}
          className="object-cover opacity-40"
          priority
          unoptimized
        />
      </div>
      <div className="container mx-auto px-4 flex flex-col items-center text-center z-10 relative">
        <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 leading-tight">
          {t('title')}
        </h1>
        <p className="text-lg md:text-xl max-w-2xl mb-8 md:mb-10">
          {t('subtitle')}
        </p>

        <div className="w-full max-w-2xl">
          <Link href="/tractors" className="block" prefetch={true}>
            <div className="relative flex cursor-pointer">
              <div className="w-full h-10 sm:h-12 px-3 py-2 rounded-l-md border-0 bg-white/90 text-black text-sm flex items-center">
                {t('searchPlaceholder')}
              </div>
              <div className="flex items-center justify-center whitespace-nowrap h-10 sm:h-12 px-3 sm:px-4 bg-[#ffc107] text-black font-medium rounded-r-md hover:bg-[#e6af06] transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-1 flex-shrink-0"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.3-4.3"></path>
                </svg>
                <span className="inline-block">{t('searchButton')}</span>
              </div>
            </div>
          </Link>
        </div>

        <div className="grid grid-cols-2 gap-6 md:gap-8 mt-8 md:mt-12">
          <div className="p-3 bg-[#2a5234]/40 rounded-lg backdrop-blur-sm">
            <div className="text-2xl md:text-3xl font-bold">{t('tractorCount')}</div>
            <div className="text-white/80 text-sm md:text-base">{t('tractorLabel')}</div>
          </div>
          <div className="p-3 bg-[#2a5234]/40 rounded-lg backdrop-blur-sm">
            <div className="text-2xl md:text-3xl font-bold">{t('brandCount')}</div>
            <div className="text-white/80 text-sm md:text-base">{t('brandLabel')}</div>
          </div>
        </div>
      </div>
    </section>
  );
}
