"use client";

import { useState, useTransition } from 'react';
import { usePathname, Link } from '@/i18n/navigation';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { LanguageSwitcher } from './LanguageSwitcher';
import { MobileMenu } from './MobileMenu';

export function Header() {
  const t = useTranslations('Navigation');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  return (
    <header className="bg-white sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          <Link href="/" className="text-2xl font-bold tracking-tight flex items-center text-tractor">
            <div className="w-8 h-8 mr-2">
              <Image 
                src="/logo.svg" 
                width={32} 
                height={32} 
                alt="TractorData Logo" 
                className="w-full h-full [filter:brightness(0)_saturate(100%)_invert(17%)_sepia(63%)_saturate(2329%)_hue-rotate(117deg)_brightness(99%)_contrast(101%)]" 
                priority 
                unoptimized
              />
            </div>
            <span>TractorData</span>
          </Link>

          <nav className="hidden md:flex space-x-6">
            <NavLink href="/">{t('home')}</NavLink>
            <NavLink href="/tractors">{t('tractors')}</NavLink>
            <NavLink href="/brands">{t('brands')}</NavLink>
            <NavLink href="/compare">{t('compare')}</NavLink>
            <NavLink href="/news">{t('news')}</NavLink>
            <NavLink href="/shows">{t('shows')}</NavLink>
          </nav>

          <div className="flex items-center space-x-4">
            <div className="hidden md:block">
              <LanguageSwitcher />
            </div>
            
            <div className="md:hidden">
              <button 
                className="p-2" 
                aria-label="Menu"
                onClick={() => setMobileMenuOpen(true)}
              >
                <MenuIcon />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <MobileMenu isOpen={mobileMenuOpen} onClose={() => setMobileMenuOpen(false)} />
    </header>
  );
}

function NavLink({ href, children }: { href: string; children: React.ReactNode }) {
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  // For localized paths, the pathnames will be properly normalized by the internationalized usePathname
  const isActive = pathname === href || (href !== '/' && pathname?.startsWith(href));

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // 使用useTransition包装导航操作以避免阻塞UI
    if (pathname !== href) {
      startTransition(() => {
        // 导航逻辑会通过Link组件自动处理
      });
    }
  };

  return (
    <Link
      href={href}
      prefetch={true}
      onClick={handleClick}
      className={`hover:text-tractor/80 text-tractor font-bold transition-colors relative ${
        isActive ? 'text-tractor underline underline-offset-4' : ''
      } ${isPending ? 'opacity-70' : ''}`}
    >
      {children}
      {isPending && (
        <span className="absolute bottom-0 left-0 h-0.5 bg-tractor animate-pulse w-full"></span>
      )}
    </Link>
  );
}

function MenuIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-tractor">
      <path d="M4 6H20M4 12H20M4 18H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
    </svg>
  );
}
