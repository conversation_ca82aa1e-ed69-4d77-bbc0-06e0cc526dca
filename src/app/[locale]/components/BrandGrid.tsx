import Link from 'next/link';

interface Brand {
  name: string;
  slug: string;
  featured?: boolean;
}

interface BrandGridProps {
  title: string;
  description?: string;
  brands: Brand[];
  viewAllLink: string;
  type: 'farm' | 'lawn';
}

export function BrandGrid({ title, description, brands, viewAllLink, type }: BrandGridProps) {
  // Sort brands to put featured ones first
  const sortedBrands = [...brands].sort((a, b) => {
    if (a.featured && !b.featured) return -1;
    if (!a.featured && b.featured) return 1;
    return 0;
  });

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-end mb-8">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900">{title}</h2>
            {description && <p className="mt-2 text-gray-600 max-w-2xl">{description}</p>}
          </div>
          <Link 
            href={viewAllLink} 
            className="text-green-700 hover:text-green-800 font-medium flex items-center"
          >
            View all
            <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6">
          {sortedBrands.map(brand => (
            <Link
              key={brand.slug}
              href={`/brands/${brand.slug}`}
              className={`
                flex items-center justify-center p-6 rounded-lg transition-all
                ${brand.featured 
                  ? 'bg-green-50 hover:bg-green-100 border-2 border-green-200' 
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'}
              `}
            >
              <span className={`
                font-semibold text-center
                ${brand.featured ? 'text-green-800' : 'text-gray-800'}
              `}>
                {brand.name}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
