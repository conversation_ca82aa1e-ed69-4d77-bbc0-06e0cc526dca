import Link from 'next/link';
import type { NewsArticle } from '@/services/newsService';

interface NewsSectionProps {
  news: NewsArticle[];
}

export function NewsSection({ news }: NewsSectionProps) {
  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900">Latest Tractor News</h2>
          <Link
            href="/news"
            className="text-tractor hover:text-tractor/80 font-medium flex items-center"
          >
            View all news
            <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
              <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news.map(item => (
            <article key={item._id} className="border border-gray-200 rounded-lg p-6 hover:border-tractor/30 transition-colors">
              <span className="text-sm text-gray-500 block mb-2">{item.date}</span>
              <h3 className="font-bold text-xl mb-3 text-gray-900">{item.title}</h3>
              <p className="text-gray-600 mb-4 line-clamp-3">{item.summary}</p>
              
              {item.images && item.images.length > 0 && (
                <div className="mb-4">
                  <img 
                    src={item.images[0].url} 
                    alt={item.images[0].alt || item.title}
                    className="w-full h-40 object-cover rounded-md"
                  />
                </div>
              )}
              
              <Link
                href={`/news/${item.slug}`}
                className="text-tractor hover:text-tractor/80 font-medium flex items-center text-sm"
              >
                Read more
                <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
                  <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Link>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}
