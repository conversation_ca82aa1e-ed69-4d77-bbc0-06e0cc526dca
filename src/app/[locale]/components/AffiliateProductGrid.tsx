'use client';

import { memo, useState, useEffect } from 'react';
import { AffiliateProduct, PageContext } from '@/lib/types';
import { EnhancedAffiliateProductCard } from './EnhancedAffiliateProductCard';
import { useTranslations } from 'next-intl';
import { Loader2, RefreshCw } from 'lucide-react';

interface AffiliateProductGridProps {
  products: AffiliateProduct[];
  locale: string;
  pageContext?: PageContext;
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'compact' | 'featured';
  columns?: 2 | 3 | 4;
  showAnimation?: boolean;
  isLoading?: boolean;
  onRefresh?: () => void;
}

export const AffiliateProductGrid = memo(function AffiliateProductGrid({
  products,
  locale,
  pageContext,
  title,
  subtitle,
  variant = 'default',
  columns = 3,
  showAnimation = true,
  isLoading = false,
  onRefresh
}: AffiliateProductGridProps) {
  const t = useTranslations('AffiliateAds');
  const [visibleCount, setVisibleCount] = useState(products.length); // 默认全部可见，避免水合错误

  // 交错显示动画
  useEffect(() => {
    if (!showAnimation || products.length === 0) return;

    // 在客户端挂载后，先设置为不可见，然后触发动画
    setVisibleCount(0);
    const timer = setTimeout(() => {
      setVisibleCount(products.length);
    }, 100);

    return () => clearTimeout(timer);
  }, [products.length, showAnimation]);

  // 获取网格样式类
  const getGridClasses = () => {
    const baseClasses = "grid gap-4 md:gap-6";
    
    switch (columns) {
      case 2:
        return `${baseClasses} grid-cols-1 sm:grid-cols-2`;
      case 3:
        return `${baseClasses} grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`;
      case 4:
        return `${baseClasses} grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4`;
      default:
        return `${baseClasses} grid-cols-1 sm:grid-cols-2 lg:grid-cols-3`;
    }
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="space-y-6">
        {(title || subtitle) && (
          <div className="text-center">
            {title && (
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
            )}
            {subtitle && (
              <p className="text-gray-600">{subtitle}</p>
            )}
          </div>
        )}
        
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-tractor mx-auto mb-4" />
            <p className="text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  // 空状态
  if (products.length === 0) {
    return (
      <div className="space-y-6">
        {(title || subtitle) && (
          <div className="text-center">
            {title && (
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
            )}
            {subtitle && (
              <p className="text-gray-600">{subtitle}</p>
            )}
          </div>
        )}
        
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products available</h3>
          <p className="text-gray-500 mb-4">There are currently no products to display.</p>
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-tractor hover:bg-tractor/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 标题区域 */}
      {(title || subtitle) && (
        <div className="text-center">
          {title && (
            <h2 className="text-2xl font-bold text-gray-900 mb-2 animate-fade-in">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-gray-600 animate-fade-in animation-delay-100">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* 产品网格 */}
      <div className={getGridClasses()}>
        {products.map((product, index) => (
          <div
            key={product.id}
            className={`transition-all duration-500 ${
              showAnimation && index >= visibleCount 
                ? 'opacity-0 translate-y-4' 
                : 'opacity-100 translate-y-0'
            }`}
            style={{
              transitionDelay: showAnimation ? `${index * 100}ms` : '0ms'
            }}
          >
            <EnhancedAffiliateProductCard
              product={product}
              locale={locale}
              pageContext={pageContext}
              variant={variant}
              showAnimation={showAnimation}
            />
          </div>
        ))}
      </div>

      {/* 刷新按钮 */}
      {onRefresh && (
        <div className="text-center">
          <button
            onClick={onRefresh}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tractor transition-colors duration-200"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Products
          </button>
        </div>
      )}
    </div>
  );
});