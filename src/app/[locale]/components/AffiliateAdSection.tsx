'use client';

import { AffiliateProductCard } from './AffiliateProductCard';
import { AffiliateProduct, PageContext } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface AffiliateAdSectionProps {
  products: AffiliateProduct[];
  locale: string;
  pageContext?: PageContext;
  className?: string;
}

export function AffiliateAdSection({ 
  products, 
  locale,
  pageContext,
  className = '' 
}: AffiliateAdSectionProps) {
  const t = useTranslations('AffiliateAds');
  
  // 只显示启用的产品
  const enabledProducts = products.filter(product => product.enabled);
  
  if (enabledProducts.length === 0) {
    return null;
  }

  return (
    <section className={`py-12 bg-white ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-1">
              {t('title')}
            </h2>
            <p className="text-sm text-gray-500">
              {t('subtitle')}
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {enabledProducts.map((product) => (
            <AffiliateProductCard
              key={product.id}
              product={product}
              locale={locale}
              pageContext={pageContext}
            />
          ))}
        </div>
        
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-400">
            {t('disclaimer')}
          </p>
        </div>
      </div>
    </section>
  );
}