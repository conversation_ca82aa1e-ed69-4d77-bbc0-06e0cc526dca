'use client';

import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';

export function Footer() {
  const t = useTranslations('Footer');
  const navT = useTranslations('Navigation');
  const [currentYear, setCurrentYear] = useState(2025); // 默认年份，避免水合错误
  
  useEffect(() => {
    // 在客户端设置实际年份
    setCurrentYear(new Date().getFullYear());
  }, []);
  
  return (
    <footer className="bg-tractor border-t border-tractor-yellow/30">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-tractor-yellow">TractorData</h3>
            <p className="text-white/90 text-base leading-relaxed">
              {t('description')}
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-bold text-tractor-yellow">{t('quickLinks')}</h3>
            <nav className="grid grid-cols-2 gap-x-8 gap-y-2">
              <FooterLink href="/">{navT('home')}</FooterLink>
              <FooterLink href="/tractors">{navT('tractors')}</FooterLink>
              <FooterLink href="/brands">{navT('brands')}</FooterLink>
              <FooterLink href="/compare">{navT('compare')}</FooterLink>
              <FooterLink href="/news">{navT('news')}</FooterLink>
              <FooterLink href="/shows">{navT('shows')}</FooterLink>
            </nav>
          </div>
        </div>

        <div className="border-t border-tractor-yellow/30 mt-16 pt-8 text-center text-white/80 text-sm">
          <p>{t('copyright', { year: currentYear })}</p>
        </div>
      </div>
    </footer>
  );
}

function FooterLink({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <Link
      href={href}
      className="text-white/90 hover:text-tractor-yellow transition-colors text-base"
    >
      {children}
    </Link>
  );
}