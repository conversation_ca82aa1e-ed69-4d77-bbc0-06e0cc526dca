'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Link, usePathname } from '@/i18n/navigation';
import { LanguageSwitcher } from './LanguageSwitcher';

type MobileMenuProps = {
  isOpen: boolean;
  onClose: () => void;
};

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const t = useTranslations('Navigation');
  const pathname = usePathname();

  // 关闭菜单的Escape键监听
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // 当菜单打开时，禁止页面滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // 当菜单关闭时，恢复页面滚动
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50" onClick={onClose}>
      <div
        className="absolute right-0 top-0 h-full w-4/5 max-w-sm bg-white shadow-lg transform transition-transform duration-300"
        onClick={(e) => e.stopPropagation()} // 防止点击菜单区域时关闭菜单
      >
        <div className="p-4 flex justify-between items-center border-b border-gray-200">
          <h2 className="text-lg font-semibold text-tractor">{t('menu')}</h2>
          <button onClick={onClose} className="p-2 text-gray-500 hover:text-gray-700">
            <CloseIcon />
          </button>
        </div>

        <div className="p-4">
          <nav className="space-y-4">
            <MobileNavLink href="/" isActive={pathname === '/'} onClick={onClose}>
              {t('home')}
            </MobileNavLink>
            <MobileNavLink
              href="/tractors"
              isActive={pathname?.startsWith('/tractors')}
              onClick={onClose}
            >
              {t('tractors')}
            </MobileNavLink>
            <MobileNavLink
              href="/brands"
              isActive={pathname?.startsWith('/brands')}
              onClick={onClose}
            >
              {t('brands')}
            </MobileNavLink>
            <MobileNavLink
              href="/compare"
              isActive={pathname?.startsWith('/compare')}
              onClick={onClose}
            >
              {t('compare')}
            </MobileNavLink>
            <MobileNavLink
              href="/news"
              isActive={pathname?.startsWith('/news')}
              onClick={onClose}
            >
              {t('news')}
            </MobileNavLink>
            <MobileNavLink
              href="/shows"
              isActive={pathname?.startsWith('/shows')}
              onClick={onClose}
            >
              {t('shows')}
            </MobileNavLink>
          </nav>

          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="mb-2">
              <div className="text-sm font-medium text-gray-600 mb-3">{t('language')}</div>
              <LanguageSwitcher variant="mobile" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function MobileNavLink({
  href,
  children,
  isActive,
  onClick
}: {
  href: string;
  children: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
}) {
  return (
    <Link
      href={href}
      className={`block py-2 px-3 rounded-md text-tractor font-medium ${
        isActive ? 'bg-tractor/10 font-semibold' : 'hover:bg-gray-50'
      }`}
      onClick={onClick}
    >
      {children}
    </Link>
  );
}

function CloseIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 18L18 6M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}
