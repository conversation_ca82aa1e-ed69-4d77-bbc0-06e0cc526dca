import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export function ViewAllCard({ type }: { type: 'farm' | 'lawn' }) {
  const t = useTranslations('TractorBrandsSection');
  const titleKey = type === 'farm' ? 'viewAllFarm' : 'viewAllLawn';
  
  return (
    <Link 
      href={`/brands?type=${type}`}
      className="block py-3 mt-1 text-center text-tractor hover:text-tractor/80 font-medium"
    >
      {t(titleKey)}
    </Link>
  );
} 