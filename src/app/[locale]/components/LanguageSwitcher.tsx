'use client';

import { useLocale } from 'next-intl';
import { useState, useEffect, useRef } from 'react';
import { routing } from '@/i18n/routing';
import { Link, usePathname } from '@/i18n/navigation';
import { Globe } from 'lucide-react';

interface LanguageSwitcherProps {
  // 可选参数，用于移动端菜单中的语言切换器
  variant?: 'default' | 'mobile';
}

export function LanguageSwitcher({ variant = 'default' }: LanguageSwitcherProps = {}) {
  const locale = useLocale();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // 定义语言显示名称
  const languageNames: Record<string, string> = {
    en: 'English',
    fr: 'Français',
    zh: '中文',
    es: 'Español',
    de: 'Deutsch',
    pt: 'Português'
  };

  // 移除了语言国旗图标

  // 关闭菜单的点击外部事件监听器
  useEffect(() => {
    if (!isOpen) return;

    const handleOutsideClick = (e: MouseEvent) => {
      setIsOpen(false);
    };

    document.addEventListener('click', handleOutsideClick);
    return () => document.removeEventListener('click', handleOutsideClick);
  }, [isOpen]);

  // 创建一个引用，用于阻止点击事件冒泡
  const containerRef = useRef<HTMLDivElement>(null);

  // 防止点击事件冒泡
  const handleContainerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 切换菜单状态
  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  // 移动端样式
  if (variant === 'mobile') {
    return (
      <div className="w-full">
        <div className="flex flex-col space-y-2">
          {routing.locales.map((l) => (
            <Link
              key={l}
              href={pathname}
              locale={l}
              className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm ${l === locale ? 'bg-tractor/10 text-tractor font-semibold' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              <span>{languageNames[l]}</span>
              {l === locale && (
                <svg className="ml-auto h-4 w-4 text-tractor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </Link>
          ))}
        </div>
      </div>
    );
  }

  // 默认样式 (桌面端)
  return (
    <div className="relative" ref={containerRef} onClick={handleContainerClick}>
      <button
        onClick={toggleMenu}
        className="flex items-center gap-1.5 text-tractor font-medium hover:text-tractor/80 bg-tractor/5 px-3 py-1.5 rounded-md border border-tractor/10 transition-all hover:bg-tractor/10"
        aria-label="Switch language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Globe className="h-4 w-4" aria-hidden="true" />
        <span className="flex items-center gap-1">
          <span>{languageNames[locale]}</span>
        </span>
        <svg
          className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute mt-2 right-0 bg-white rounded-md shadow-lg py-1 z-50 min-w-40 border border-gray-200 animate-in fade-in slide-in-from-top-5 duration-200">
          {routing.locales.map((l) => (
            <Link
              key={l}
              href={pathname}
              locale={l}
              className={`flex items-center gap-2 px-4 py-2.5 text-sm ${l === locale ? 'bg-tractor/10 text-tractor font-semibold' : 'text-gray-700 hover:bg-gray-50'}`}
              onClick={() => setIsOpen(false)}
            >
              <span>{languageNames[l]}</span>
              {l === locale && (
                <svg className="ml-auto h-4 w-4 text-tractor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
