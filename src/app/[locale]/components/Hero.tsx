import Link from 'next/link';

export function Hero() {
  return (
    <div className="relative bg-gradient-to-r from-green-800 to-green-600 text-white">
      <div className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            The Complete Tractor Database
          </h1>
          <p className="text-lg md:text-xl opacity-90 mb-8">
            Access detailed specifications and information for all makes and models of tractors. 
            From vintage classics to modern machines.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href="/brands?type=farm"
              className="bg-white text-green-700 hover:bg-green-50 transition-colors px-6 py-3 rounded-lg font-semibold text-center"
            >
              Farm Tractors
            </Link>
            <Link
              href="/brands?type=lawn"
              className="bg-green-900 bg-opacity-40 hover:bg-opacity-60 transition-colors px-6 py-3 rounded-lg font-semibold text-center"
            >
              Lawn Tractors
            </Link>
          </div>
        </div>
      </div>
      
      {/* Decorative tractor silhouette */}
      <div className="absolute right-0 bottom-0 opacity-10 pointer-events-none hidden lg:block">
        <TractorSilhouette className="w-96 h-96" />
      </div>
    </div>
  );
}

function TractorSilhouette({ className = "w-24 h-24" }) {
  return (
    <svg className={className} viewBox="0 0 512 512" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M448 352h-32V225.9c0-25.7-8.9-50.8-25.3-70.8L336 96h-64l32 48H48c-26.5 0-48 21.5-48 48v144c0 8.8 7.2 16 16 16h16c8.8 0 16-7.2 16-16v-32h384v32c0 8.8 7.2 16 16 16h16c8.8 0 16-7.2 16-16v-32h32c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-96 0H160V192h192v160zM384 96c0-17.7-14.3-32-32-32s-32 14.3-32 32 14.3 32 32 32 32-14.3 32-32zM160 352c0 53-43 96-96 96s-96-43-96-96 43-96 96-96 96 43 96 96zm-16 0c0-44.2-35.8-80-80-80s-80 35.8-80 80 35.8 80 80 80 80-35.8 80-80zm368 0c0 53-43 96-96 96s-96-43-96-96 43-96 96-96 96 43 96 96zm-16 0c0-44.2-35.8-80-80-80s-80 35.8-80 80 35.8 80 80 80 80-35.8 80-80z" />
    </svg>
  );
}
