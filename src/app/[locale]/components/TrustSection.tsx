import React from 'react';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export function TrustSection() {
  const t = useTranslations('TrustSection');
  
  return (
    <section className="bg-tractor-yellow/10 py-16 px-4 text-center">
      <div className="container mx-auto max-w-4xl">
        <h2 className="text-3xl md:text-4xl font-bold text-tractor mb-4">
          {t('title')}
        </h2>
        
        <p className="text-gray-700 mb-8 max-w-3xl mx-auto">
          {t('description')}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/brands"
            className="bg-tractor hover:bg-tractor/90 text-white font-medium py-2 px-6 rounded-md transition-colors"
          >
            {t('browseTractors')}
          </Link>
          
          <Link 
            href="/compare" 
            className="border border-tractor text-tractor hover:bg-tractor/5 font-medium py-2 px-6 rounded-md transition-colors"
          >
            {t('compareTractors')}
          </Link>
        </div>
      </div>
    </section>
  );
}
