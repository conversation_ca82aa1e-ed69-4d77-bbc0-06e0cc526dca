import { Link } from '@/i18n/navigation';
import Image from 'next/image';
import { memo } from 'react';

interface TractorBrandCardProps {
  name: string;
  slug: string;
  imageSrc: string;
  type: 'farm' | 'lawn';
}

// 使用 memo 来避免不必要的重新渲染
export const TractorBrandCard = memo(function TractorBrandCard({ name, slug, imageSrc }: TractorBrandCardProps) {
  return (
    <Link 
      href={`/brands/${slug}`}
      className="group flex items-center justify-between py-3 hover:bg-tractor/5 transition-colors"
      prefetch={true}
    >
      <div className="flex items-center">
        <div className="w-10 h-10 mr-3 flex justify-center items-center bg-tractor/10 rounded overflow-hidden">
          <Image 
            src={imageSrc} 
            alt={`${name} logo`}
            width={40}
            height={40}
            className="object-contain w-8 h-8"
            loading="lazy"
            sizes="40px"
            unoptimized
          />
        </div>
        <span className="font-medium text-gray-800 group-hover:text-tractor transition-colors">{name}</span>
      </div>
      <div className="text-gray-400 group-hover:text-tractor transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </Link>
  );
});
