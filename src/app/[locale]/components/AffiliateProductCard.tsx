'use client';

import { memo } from 'react';
import { AffiliateProduct, PageContext } from '@/lib/types';
import { EnhancedAffiliateProductCard } from './EnhancedAffiliateProductCard';

interface AffiliateProductCardProps {
  product: AffiliateProduct;
  locale: string;
  pageContext?: PageContext;
}

// 为了向后兼容，保留原有的 AffiliateProductCard 组件
// 但内部使用增强版本
export const AffiliateProductCard = memo(function AffiliateProductCard({ 
  product, 
  locale,
  pageContext
}: AffiliateProductCardProps) {
  // 直接使用增强版本的组件
  return (
    <EnhancedAffiliateProductCard
      product={product}
      locale={locale}
      pageContext={pageContext}
      variant="default"
      showAnimation={true}
    />
  );
});