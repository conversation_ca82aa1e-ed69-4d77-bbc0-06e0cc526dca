"use client";

import { FilterOption } from '@/lib/types';

interface RadioFilterProps {
  id: string;
  name: string;
  options: FilterOption[];
  value: string;
  onChange: (value: string) => void;
}

export function RadioFilter({ id, name, options, value, onChange }: RadioFilterProps) {
  const handleChange = (optionValue: string) => {
    // 如果已选中，则清除选择
    if (value === optionValue) {
      onChange("");
    } else {
      onChange(optionValue);
    }
  };
  
  return (
    <div className="space-y-2">
      <h4 className="font-medium text-gray-900">{name}</h4>
      
      <div className="space-y-1">
        {options.map(option => (
          <div key={option.id} className="flex items-center">
            <input
              id={`${id}-${option.id}`}
              type="radio"
              checked={value === option.value.toString()}
              onChange={() => handleChange(option.value.toString())}
              className="h-4 w-4 text-tractor border-gray-300 focus:ring-tractor"
            />
            <label htmlFor={`${id}-${option.id}`} className="ml-2 text-sm text-gray-700">
              {option.label}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
}
