"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FilterGroup, FilterOption } from '@/lib/types';
import { CheckboxFilter } from './CheckboxFilter';
import { RangeFilter } from './RangeFilter';
import { RadioFilter } from './RadioFilter';
import { FilterTag } from './FilterTag';

interface FilterSystemProps {
  filters: FilterGroup[];
  onFilterChange?: (filters: Record<string, string | string[] | { min: number; max: number }>) => void;
  className?: string;
}

export function FilterSystem({ filters, onFilterChange, className = "" }: FilterSystemProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeFilters, setActiveFilters] = useState<Record<string, string | string[] | { min: number; max: number }>>({});
  const [activeTags, setActiveTags] = useState<{id: string, name: string, value: string}[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // 初始化筛选状态
  useEffect(() => {
    const initialFilters: Record<string, string | string[] | { min: number; max: number }> = {};
    
    // 从URL查询参数中获取筛选值
    filters.forEach(group => {
      if (group.type === 'checkbox' || group.type === 'radio') {
        const paramValue = searchParams?.get(group.id);
        if (paramValue) {
          initialFilters[group.id] = group.type === 'checkbox' 
            ? paramValue.split(',') 
            : paramValue;
        }
      } else if (group.type === 'range') {
        const minParam = searchParams?.get(`${group.id}Min`);
        const maxParam = searchParams?.get(`${group.id}Max`);
        
        if (minParam || maxParam) {
          initialFilters[group.id] = {
            min: minParam ? Number(minParam) : group.rangeMin,
            max: maxParam ? Number(maxParam) : group.rangeMax
          };
        }
      }
    });
    
    setActiveFilters(initialFilters);
    updateFilterTags(initialFilters);
  }, [filters, searchParams]);

  // 更新筛选标签
  const updateFilterTags = (filterValues: Record<string, string | string[] | { min: number; max: number }>) => {
    const tags: {id: string, name: string, value: string}[] = [];
    
    Object.entries(filterValues).forEach(([filterId, value]) => {
      const filterGroup = filters.find(f => f.id === filterId);
      
      if (!filterGroup) return;
      
      if (filterGroup.type === 'checkbox' && Array.isArray(value)) {
        value.forEach(val => {
          const option = filterGroup.options?.find(o => o.value === val);
          if (option) {
            tags.push({
              id: `${filterId}-${val}`,
              name: filterGroup.name,
              value: option.label
            });
          }
        });
      } else if (filterGroup.type === 'radio' && typeof value === 'string') {
        const option = filterGroup.options?.find(o => o.value === value);
        if (option) {
          tags.push({
            id: `${filterId}-${value}`,
            name: filterGroup.name,
            value: option.label
          });
        }
      } else if (filterGroup.type === 'range' && typeof value === 'object') {
        if (value.min !== filterGroup.rangeMin || value.max !== filterGroup.rangeMax) {
          tags.push({
            id: `${filterId}-range`,
            name: filterGroup.name,
            value: `${value.min} - ${value.max}`
          });
        }
      }
    });
    
    setActiveTags(tags);
  };

  // 处理筛选变化
  const handleFilterChange = (filterId: string, value: string | string[] | { min: number; max: number } | undefined) => {
    const newFilters = { ...activeFilters };
    
    // 如果值为空或默认值，则删除该筛选
    if (value === undefined || value === null || 
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'object' && 'min' in value && 'max' in value && 
         value.min === filters.find(f => f.id === filterId)?.rangeMin && 
         value.max === filters.find(f => f.id === filterId)?.rangeMax)) {
      delete newFilters[filterId];
    } else {
      newFilters[filterId] = value;
    }
    
    setActiveFilters(newFilters);
    updateFilterTags(newFilters);
    
    if (onFilterChange) {
      onFilterChange(newFilters);
    } else {
      // 构建查询参数
      const params = new URLSearchParams();
      
      // 保留当前的搜索查询
      const currentQuery = searchParams?.get('q');
      if (currentQuery) {
        params.set('q', currentQuery);
      }
      
      // 添加筛选参数
      Object.entries(newFilters).forEach(([key, value]) => {
        const filter = filters.find(f => f.id === key);
        
        if (filter?.type === 'checkbox' && Array.isArray(value)) {
          params.set(key, value.join(','));
        } else if (filter?.type === 'radio' && typeof value === 'string') {
          params.set(key, value);
        } else if (filter?.type === 'range' && typeof value === 'object') {
          if (value.min !== filter.rangeMin) {
            params.set(`${key}Min`, value.min.toString());
          }
          if (value.max !== filter.rangeMax) {
            params.set(`${key}Max`, value.max.toString());
          }
        }
      });
      
      // 导航到新的URL
      const currentPath = window.location.pathname;
      router.push(`${currentPath}?${params.toString()}`);
    }
  };

  // 移除筛选标签
  const handleRemoveTag = (tagId: string) => {
    const tag = activeTags.find(t => t.id === tagId);
    if (!tag) return;
    
    const [filterId] = tagId.split('-');
    const filter = filters.find(f => f.id === filterId);
    
    if (!filter) return;
    
    if (filter.type === 'checkbox') {
      const currentValues = Array.isArray(activeFilters[filterId]) 
        ? [...(activeFilters[filterId] as string[])] 
        : [];
      const valueToRemove = tagId.replace(`${filterId}-`, '');
      const newValues = currentValues.filter(v => v !== valueToRemove);
      
      handleFilterChange(filterId, newValues.length > 0 ? newValues : undefined);
    } else if (filter.type === 'radio') {
      handleFilterChange(filterId, undefined);
    } else if (filter.type === 'range') {
      handleFilterChange(filterId, {
        min: filter.rangeMin || 0,
        max: filter.rangeMax || 100
      });
    }
  };

  // 清除所有筛选
  const handleClearAll = () => {
    setActiveFilters({});
    setActiveTags([]);
    
    if (onFilterChange) {
      onFilterChange({});
    } else {
      // 只保留搜索查询参数
      const params = new URLSearchParams();
      const currentQuery = searchParams?.get('q');
      if (currentQuery) {
        params.set('q', currentQuery);
      }
      
      const currentPath = window.location.pathname;
      router.push(`${currentPath}${currentQuery ? `?${params.toString()}` : ''}`);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      {/* 筛选标题 */}
      <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">筛选</h3>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="md:hidden text-gray-500 hover:text-gray-700"
        >
          {isOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
        </button>
      </div>
      
      {/* 活动的筛选标签 */}
      {activeTags.length > 0 && (
        <div className="px-4 py-2 border-b border-gray-200 flex flex-wrap gap-2">
          {activeTags.map(tag => (
            <FilterTag
              key={tag.id}
              id={tag.id}
              name={tag.name}
              value={tag.value}
              onRemove={handleRemoveTag}
            />
          ))}
          
          {activeTags.length > 1 && (
            <button
              onClick={handleClearAll}
              className="text-xs text-gray-500 hover:text-tractor font-medium py-1 px-2 hover:bg-gray-100 rounded-full ml-auto"
            >
              清除全部
            </button>
          )}
        </div>
      )}
      
      {/* 筛选选项 */}
      <div className={`px-4 py-2 space-y-4 ${isOpen ? 'block' : 'hidden md:block'}`}>
        {filters.map(filter => (
          <div key={filter.id} className="border-b border-gray-200 pb-4 last:border-0 last:pb-0">
            {filter.type === 'checkbox' && (
              <CheckboxFilter
                id={filter.id}
                name={filter.name}
                options={filter.options || []}
                value={activeFilters[filter.id] || []}
                onChange={(value) => handleFilterChange(filter.id, value)}
              />
            )}
            
            {filter.type === 'radio' && (
              <RadioFilter
                id={filter.id}
                name={filter.name}
                options={filter.options || []}
                value={activeFilters[filter.id] || ''}
                onChange={(value) => handleFilterChange(filter.id, value)}
              />
            )}
            
            {filter.type === 'range' && (
              <RangeFilter
                id={filter.id}
                name={filter.name}
                min={filter.rangeMin || 0}
                max={filter.rangeMax || 100}
                step={filter.rangeStep || 1}
                value={
                  typeof activeFilters[filter.id] === 'object' && 'min' in activeFilters[filter.id] 
                    ? activeFilters[filter.id] as { min: number; max: number }
                    : { min: filter.rangeMin || 0, max: filter.rangeMax || 100 }
                }
                onChange={(value) => handleFilterChange(filter.id, value)}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// 图标组件
function ChevronDownIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
  );
}

function ChevronUpIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
    </svg>
  );
}
