"use client";

import { useState, useEffect } from 'react';

interface RangeFilterProps {
  id: string;
  name: string;
  min: number;
  max: number;
  step: number;
  value: { min: number; max: number };
  onChange: (value: { min: number; max: number }) => void;
}

export function RangeFilter({ id, name, min, max, step, value, onChange }: RangeFilterProps) {
  const [localMin, setLocalMin] = useState(value.min);
  const [localMax, setLocalMax] = useState(value.max);
  const [isEditing, setIsEditing] = useState(false);
  
  // 同步外部值
  useEffect(() => {
    setLocalMin(value.min);
    setLocalMax(value.max);
  }, [value]);
  
  // 处理滑块变化
  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'min' | 'max') => {
    const newValue = parseInt(e.target.value, 10);
    
    if (type === 'min') {
      // 确保最小值不大于最大值
      setLocalMin(Math.min(newValue, localMax));
    } else {
      // 确保最大值不小于最小值
      setLocalMax(Math.max(newValue, localMin));
    }
  };
  
  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, type: 'min' | 'max') => {
    const newValue = e.target.value === '' ? '' : parseInt(e.target.value, 10);
    
    if (type === 'min') {
      setLocalMin(newValue === '' ? min : newValue);
    } else {
      setLocalMax(newValue === '' ? max : newValue);
    }
  };
  
  // 应用更改
  const applyChanges = () => {
    // 确保数值在有效范围内
    const validMin = Math.max(min, Math.min(localMin, localMax));
    const validMax = Math.min(max, Math.max(localMax, localMin));
    
    setLocalMin(validMin);
    setLocalMax(validMax);
    
    onChange({ min: validMin, max: validMax });
    setIsEditing(false);
  };
  
  // 处理滑块释放
  const handleSliderRelease = () => {
    applyChanges();
  };
  
  // 处理输入框失焦
  const handleInputBlur = () => {
    applyChanges();
  };
  
  // 处理输入框回车
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applyChanges();
    }
  };
  
  // 计算滑块的百分比位置，用于显示双滑块
  const minPercent = ((localMin - min) / (max - min)) * 100;
  const maxPercent = ((localMax - min) / (max - min)) * 100;
  
  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">{name}</h4>
      
      {/* 范围输入框 */}
      <div className="flex items-center space-x-2">
        <input
          type="number"
          min={min}
          max={localMax}
          value={localMin}
          step={step}
          onChange={(e) => handleInputChange(e, 'min')}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          onFocus={() => setIsEditing(true)}
          className="w-20 py-1 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:border-tractor focus:ring-1 focus:ring-tractor"
        />
        <span className="text-gray-500">-</span>
        <input
          type="number"
          min={localMin}
          max={max}
          value={localMax}
          step={step}
          onChange={(e) => handleInputChange(e, 'max')}
          onBlur={handleInputBlur}
          onKeyDown={handleInputKeyDown}
          onFocus={() => setIsEditing(true)}
          className="w-20 py-1 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:border-tractor focus:ring-1 focus:ring-tractor"
        />
      </div>
      
      {/* 双滑块 */}
      <div className="relative h-5">
        <input
          type="range"
          min={min}
          max={max}
          value={localMin}
          step={step}
          onChange={(e) => handleSliderChange(e, 'min')}
          onMouseUp={handleSliderRelease}
          onTouchEnd={handleSliderRelease}
          className="absolute top-0 left-0 w-full appearance-none bg-transparent pointer-events-none"
        />
        <input
          type="range"
          min={min}
          max={max}
          value={localMax}
          step={step}
          onChange={(e) => handleSliderChange(e, 'max')}
          onMouseUp={handleSliderRelease}
          onTouchEnd={handleSliderRelease}
          className="absolute top-0 left-0 w-full appearance-none bg-transparent pointer-events-none"
        />
        
        {/* 自定义滑块轨道 */}
        <div className="absolute top-2 left-0 h-1 w-full bg-gray-200 rounded">
          <div
            className="absolute h-full bg-tractor rounded"
            style={{
              left: `${minPercent}%`,
              width: `${maxPercent - minPercent}%`
            }}
          />
        </div>
        
        {/* 自定义滑块手柄 */}
        <div
          className="absolute top-0.5 w-4 h-4 bg-white border-2 border-tractor rounded-full cursor-pointer"
          style={{ left: `calc(${minPercent}% - 0.5rem)` }}
        />
        <div
          className="absolute top-0.5 w-4 h-4 bg-white border-2 border-tractor rounded-full cursor-pointer"
          style={{ left: `calc(${maxPercent}% - 0.5rem)` }}
        />
      </div>
      
      {/* 范围标签 */}
      <div className="flex justify-between text-xs text-gray-500">
        <span>{min}</span>
        <span>{max}</span>
      </div>
    </div>
  );
}
