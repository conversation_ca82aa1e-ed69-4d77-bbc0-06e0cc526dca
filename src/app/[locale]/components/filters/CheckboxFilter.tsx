"use client";

import { useState } from 'react';
import { FilterOption } from '@/lib/types';

interface CheckboxFilterProps {
  id: string;
  name: string;
  options: FilterOption[];
  value: string[];
  onChange: (value: string[]) => void;
}

export function CheckboxFilter({ id, name, options, value, onChange }: CheckboxFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 默认显示的选项数量
  const defaultVisibleCount = 5;
  
  // 显示的选项
  const visibleOptions = isExpanded ? options : options.slice(0, defaultVisibleCount);
  
  const handleCheckboxChange = (optionValue: string, checked: boolean) => {
    let newValue = [...value];
    
    if (checked) {
      // 添加选项
      newValue.push(optionValue);
    } else {
      // 移除选项
      newValue = newValue.filter(v => v !== optionValue);
    }
    
    onChange(newValue);
  };
  
  return (
    <div className="space-y-2">
      <h4 className="font-medium text-gray-900">{name}</h4>
      
      <div className="space-y-1">
        {visibleOptions.map(option => (
          <div key={option.id} className="flex items-center">
            <input
              id={`${id}-${option.id}`}
              type="checkbox"
              checked={value.includes(option.value.toString())}
              onChange={(e) => handleCheckboxChange(option.value.toString(), e.target.checked)}
              className="h-4 w-4 text-tractor border-gray-300 rounded focus:ring-tractor"
            />
            <label htmlFor={`${id}-${option.id}`} className="ml-2 text-sm text-gray-700">
              {option.label}
            </label>
          </div>
        ))}
      </div>
      
      {options.length > defaultVisibleCount && (
        <button
          type="button"
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-sm font-medium text-tractor hover:text-tractor/80 flex items-center"
        >
          {isExpanded ? '显示更少' : `显示全部 (${options.length})`}
          <span className="ml-1">
            {isExpanded ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </span>
        </button>
      )}
    </div>
  );
}

function ChevronDownIcon({ className }: { className?: string }) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className={className} 
      viewBox="0 0 20 20" 
      fill="currentColor"
    >
      <path 
        fillRule="evenodd" 
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
        clipRule="evenodd" 
      />
    </svg>
  );
}

function ChevronUpIcon({ className }: { className?: string }) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className={className} 
      viewBox="0 0 20 20" 
      fill="currentColor"
    >
      <path 
        fillRule="evenodd" 
        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" 
        clipRule="evenodd" 
      />
    </svg>
  );
}
