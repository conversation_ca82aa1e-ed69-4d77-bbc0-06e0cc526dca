"use client";

interface FilterTagProps {
  id: string;
  name: string;
  value: string;
  onRemove: (id: string) => void;
}

export function FilterTag({ id, name, value, onRemove }: FilterTagProps) {
  return (
    <div className="inline-flex items-center py-1 px-2 rounded-full bg-tractor/10 text-xs">
      <span className="font-medium text-tractor/70 mr-1">{name}:</span>
      <span className="text-gray-700">{value}</span>
      <button
        onClick={() => onRemove(id)}
        className="ml-1 text-gray-500 hover:text-gray-700"
        aria-label={`移除${name}筛选`}
      >
        <CrossIcon className="h-3 w-3" />
      </button>
    </div>
  );
}

function CrossIcon({ className }: { className?: string }) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className={className} 
      viewBox="0 0 20 20" 
      fill="currentColor"
    >
      <path 
        fillRule="evenodd" 
        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
        clipRule="evenodd" 
      />
    </svg>
  );
}
