import Link from 'next/link';
import Image from 'next/image';

interface TractorModel {
  id: string;
  name: string;
  brand: string;
  image: string;
  year: number;
  power?: string;
  category: 'farm' | 'lawn';
}

interface FeaturedTractorsProps {
  title: string;
  tractors: TractorModel[];
}

export function FeaturedTractors({ title, tractors }: FeaturedTractorsProps) {
  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8">{title}</h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {tractors.map(tractor => (
            <TractorCard key={tractor.id} tractor={tractor} />
          ))}
        </div>
      </div>
    </section>
  );
}

function TractorCard({ tractor }: { tractor: TractorModel }) {
  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
      <div className="relative h-48 bg-gray-100">
        <Image 
          src={tractor.image} 
          alt={`${tractor.brand} ${tractor.name}`}
          fill
          className="object-contain p-4"
        />
      </div>
      
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-semibold text-lg text-gray-900">
            {tractor.brand} {tractor.name}
          </h3>
          <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
            {tractor.year}
          </span>
        </div>
        
        {tractor.power && (
          <p className="text-gray-600 text-sm mb-3">
            Power: {tractor.power}
          </p>
        )}
        
        <Link 
          href={`/${tractor.category}-tractors/${tractor.brand.toLowerCase()}/${tractor.id}`}
          className="text-green-700 hover:text-green-800 font-medium text-sm flex items-center"
        >
          View details
          <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
            <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </Link>
      </div>
    </div>
  );
}
