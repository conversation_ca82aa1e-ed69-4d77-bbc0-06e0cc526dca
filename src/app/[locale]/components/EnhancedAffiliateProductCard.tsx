'use client';

import Image from 'next/image';
import { memo, useState, useRef, useEffect } from 'react';
import { AffiliateProduct, TrackClickRequest, PageContext } from '@/lib/types';
import { useTranslations } from 'next-intl';
import { Star, Tag, Flame, Zap, ExternalLink, Heart, Globe } from 'lucide-react';
import {
  getOptimalAffiliateUrl,
  getLinkDestinationName,
  getCurrentAffiliateConfig
} from '@/lib/amazonConfig';

interface EnhancedAffiliateProductCardProps {
  product: AffiliateProduct;
  locale: string;
  pageContext?: PageContext;
  variant?: 'default' | 'compact' | 'featured';
  showAnimation?: boolean;
}

export const EnhancedAffiliateProductCard = memo(function EnhancedAffiliateProductCard({ 
  product, 
  locale,
  pageContext,
  variant = 'default',
  showAnimation = true
}: EnhancedAffiliateProductCardProps) {
  const t = useTranslations('AffiliateAds');
  const [isTracking, setIsTracking] = useState(false);
  const [isVisible, setIsVisible] = useState(true); // 默认可见，避免水合错误
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  
  // 获取当前配置
  const config = getCurrentAffiliateConfig();

  // 根据配置决定显示的文本
  const title = config.showLocalizedText
    ? (product.translations?.[locale]?.title || product.title)
    : product.title;
  const description = config.showLocalizedText
    ? (product.translations?.[locale]?.description || product.description)
    : product.description;

  // 获取最优的联盟链接和目标站点名称
  const affiliateUrl = getOptimalAffiliateUrl(product, locale, config);
  const linkDestination = getLinkDestinationName(product, locale, config);

  // 交集观察器用于延迟加载动画
  useEffect(() => {
    if (!showAnimation || !cardRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    observer.observe(cardRef.current);

    return () => observer.disconnect();
  }, [showAnimation]);

  // 处理点击跟踪
  const handleClick = async () => {
    if (isTracking) return;
    
    setIsTracking(true);
    
    try {
      const trackingData: TrackClickRequest = {
        productId: product.id,
        sourcePage: pageContext?.pageType || 'home',
        sourceUrl: window.location.href,
        referrer: document.referrer || undefined,
        pageContext: pageContext ? {
          brandSlug: pageContext.brandSlug,
          tractorModel: pageContext.tractorModel,
          newsSlug: pageContext.newsSlug
        } : undefined
      };

      const response = await fetch('/api/affiliate/track/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(trackingData)
      });

      if (response.ok) {
        const result = await response.json();
        window.open(result.redirectUrl, '_blank', 'noopener,noreferrer');
      } else {
        window.open(affiliateUrl, '_blank', 'noopener,noreferrer');
      }
    } catch (error) {
      console.error('点击跟踪错误:', error);
      window.open(affiliateUrl, '_blank', 'noopener,noreferrer');
    } finally {
      setIsTracking(false);
    }
  };

  // 移动端触摸处理
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    setTouchStart({ x: touch.clientX, y: touch.clientY });
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touch = e.changedTouches[0];
    const deltaX = Math.abs(touch.clientX - touchStart.x);
    const deltaY = Math.abs(touch.clientY - touchStart.y);
    
    // 如果移动距离很小，认为是点击
    if (deltaX < 10 && deltaY < 10) {
      handleClick();
    }
    
    setTouchStart(null);
  };

  // 渲染星级评分
  const renderStarRating = (rating: number, reviewCount: number) => {
    if (!rating || rating === 0) return null;

    return (
      <div className="flex items-center space-x-1 mb-2">
        <div className="flex items-center">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-3 w-3 transition-colors duration-200 ${
                i < Math.round(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        <span className="text-xs text-gray-500 transition-colors duration-200">
          {rating.toFixed(1)}/5 {reviewCount > 0 && `(${reviewCount})`}
        </span>
      </div>
    );
  };

  // 渲染产品标签
  const renderProductTags = () => {
    if (!product.tags || product.tags.length === 0) return null;
    
    const displayTags = product.tags.slice(0, variant === 'compact' ? 1 : 2);
    
    return (
      <div className="flex items-center mb-2">
        <Tag className="h-3 w-3 text-gray-400 mr-1" />
        <div className="flex flex-wrap gap-1">
          {displayTags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 transition-colors duration-200 hover:bg-gray-200"
            >
              {tag}
            </span>
          ))}
          {product.tags.length > displayTags.length && (
            <span className="text-xs text-gray-500">
              +{product.tags.length - displayTags.length}
            </span>
          )}
        </div>
      </div>
    );
  };

  // 获取卡片样式类
  const getCardClasses = () => {
    const baseClasses = "bg-white rounded-lg border border-tractor/10 overflow-hidden transition-all duration-300 group cursor-pointer";
    const animationClasses = showAnimation ? 
      `transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}` : '';
    
    const hoverClasses = "hover:shadow-xl hover:border-tractor/30 hover:-translate-y-1";
    const activeClasses = "active:scale-95 active:shadow-lg";
    
    switch (variant) {
      case 'compact':
        return `${baseClasses} ${animationClasses} ${hoverClasses} ${activeClasses} max-w-xs`;
      case 'featured':
        return `${baseClasses} ${animationClasses} ${hoverClasses} ${activeClasses} ring-2 ring-tractor/20`;
      default:
        return `${baseClasses} ${animationClasses} ${hoverClasses} ${activeClasses}`;
    }
  };

  return (
    <div 
      ref={cardRef}
      className={getCardClasses()}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      style={{
        transitionDelay: showAnimation ? '0ms' : '0ms'
      }}
    >
      {/* 图片容器 */}
      <div className={`relative bg-gray-50 overflow-hidden ${
        variant === 'compact' ? 'aspect-square' : 'aspect-square'
      }`}>
        {/* 加载占位符 */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-tractor rounded-full animate-spin"></div>
          </div>
        )}
        
        <Image
          src={product.imageUrl}
          alt={title}
          fill
          className={`object-contain p-4 transition-all duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          } ${
            isHovered ? 'scale-110' : 'scale-100'
          }`}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          unoptimized
          onLoad={() => setImageLoaded(true)}
        />
        
        {/* 顶部标签 */}
        {(product.isHotPick || product.isOnSale) && (
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.isHotPick && (
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500 text-white shadow-sm transition-all duration-300 ${
                isHovered ? 'scale-110 shadow-md' : 'scale-100'
              }`}>
                <Flame className="h-3 w-3 mr-1" />
                {t('hotPick')}
              </span>
            )}
            {product.isOnSale && (
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500 text-white shadow-sm transition-all duration-300 ${
                isHovered ? 'scale-110 shadow-md' : 'scale-100'
              }`}>
                <Zap className="h-3 w-3 mr-1" />
                {t('onSale')}
              </span>
            )}
          </div>
        )}
        
        {/* 分类标签 */}
        {product.category && (
          <div className="absolute top-2 right-2">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700 shadow-sm transition-all duration-300 ${
              isHovered ? 'bg-white shadow-md' : 'bg-white/90'
            }`}>
              {t(`category.${product.category}`)}
            </span>
          </div>
        )}

        {/* 悬停时的外部链接图标 */}
        <div className={`absolute bottom-2 right-2 transition-all duration-300 ${
          isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
        }`}>
          <div className="bg-tractor text-white p-2 rounded-full shadow-lg">
            <ExternalLink className="h-3 w-3" />
          </div>
        </div>
      </div>
      
      {/* 内容区域 */}
      <div className="p-4">
        <h3 className={`font-medium text-gray-900 mb-2 line-clamp-2 transition-colors duration-200 group-hover:text-tractor ${
          variant === 'compact' ? 'text-sm' : 'text-sm'
        }`}>
          {title}
        </h3>
        
        {/* 评分显示 */}
        {renderStarRating(product.rating || 0, product.reviewCount || 0)}
        
        {variant !== 'compact' && (
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">
            {description}
          </p>
        )}
        
        {/* 产品标签 */}
        {renderProductTags()}
        
        {/* 按钮 */}
        <button
          onClick={handleClick}
          disabled={isTracking}
          className={`w-full bg-tractor text-white font-medium rounded-md hover:bg-tractor/90 focus:outline-none focus:ring-2 focus:ring-tractor focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 ${
            variant === 'compact' ? 'text-xs py-2 px-3' : 'text-sm py-2.5 px-4'
          }`}
        >
          {isTracking ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {t('loading')}
            </div>
          ) : (
            <div className="flex items-center justify-center">
              {t('viewDetails')}
              <ExternalLink className="h-3 w-3 ml-1 transition-transform duration-200 group-hover:translate-x-0.5" />
            </div>
          )}
        </button>

        {/* 链接目标说明 */}
        {config.showLinkDestination && (
          <div className="flex items-center justify-center mt-1 text-xs text-gray-500">
            <Globe className="h-3 w-3 mr-1" />
            <span>{t('linkTo')} {linkDestination}</span>
          </div>
        )}
      </div>

      {/* 加载状态覆盖层 */}
      {isTracking && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-tractor mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">{t('loading')}</p>
          </div>
        </div>
      )}
    </div>
  );
});