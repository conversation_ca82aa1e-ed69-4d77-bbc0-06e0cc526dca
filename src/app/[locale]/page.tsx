import { Header } from "./components/Header";
import { HeroSection } from "./components/HeroSection";
import { TractorBrandsSection } from "./components/TractorBrandsSection";
import { TrustSection } from "./components/TrustSection";
import { TractorNewsSection } from "./components/TractorNewsSection";
import { AffiliateAdSection } from "./components/AffiliateAdSection";
import { Footer } from "./components/Footer";
import { Link } from "@/i18n/navigation";
import {getLocale, getTranslations} from 'next-intl/server';
import { Suspense } from 'react';
import { getHomePageData } from '@/services/homeService';
import { ProductMatcher } from '@/lib/productMatcher';
import JsonLd from '@/components/JsonLd';

// 设置页面重新生成的时间间隔 - 每小时更新一次
export const revalidate = 3600;

// 预取关键路径中的页面数据
export async function generateMetadata() {
  const locale = await getLocale();
  const t = await getTranslations({ locale, namespace: 'HomePage' });

  return {
    title: `${t('metaTitle')}`,
    description: `${t('metaDescription')}`,
    keywords: `${t('metaKeywords')}`,
    openGraph: {
      title: t('metaTitle'),
      description: t('metaDescription'),
      type: 'website',
      images: [
        {
          url: 'https://picsum.photos/id/1045/1200/630',
          width: 1200,
          height: 630,
          alt: t('metaTitle')
        }
      ],
      locale: locale
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metaTitle'),
      description: t('metaDescription'),
      images: ['https://picsum.photos/id/1045/1200/630']
    }
    // 移除硬编码的 alternates，使用 layout 中的全局设置
  };
}

export default async function Home() {
  // 从路由参数获取当前语言环境
  const locale = await getLocale();

  // 使用优化后的单一数据获取函数，减少数据库连接和查询
  const [homeData, t, affiliateProducts] = await Promise.all([
    getHomePageData(locale),
    getTranslations({ locale, namespace: 'HomePage' }),
    // 获取首页的动态广告产品数据
    ProductMatcher.getProductsForPage({
      pageType: 'home',
      locale
    }, 4)
  ]);

  // 从统一获取的数据中解构出各部分数据
  const { farmBrands: farmBrandsData, lawnBrands: lawnBrandsData, news: tractorNews } = homeData;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main>
        <HeroSection />

        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800">{t('popularBrands')}</h2>
              <Link href="/brands" className="text-tractor hover:text-tractor/80 flex items-center" prefetch={true}>
                {t('viewAllBrands')}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 rounded-lg"></div>}>
                <TractorBrandsSection
                  title="farmTractors"
                  subtitle="farmDescription"
                  brands={farmBrandsData}
                  type="farm"
                />
              </Suspense>

              <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 rounded-lg"></div>}>
                <TractorBrandsSection
                  title="lawnTractors"
                  subtitle="lawnDescription"
                  brands={lawnBrandsData}
                  type="lawn"
                />
              </Suspense>
            </div>
          </div>
        </section>

        <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 my-12 rounded-lg"></div>}>
          <AffiliateAdSection 
            products={affiliateProducts} 
            locale={locale}
            pageContext={{
              pageType: 'home',
              locale
            }}
          />
        </Suspense>

        <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 my-12 rounded-lg"></div>}>
          <TractorNewsSection news={tractorNews} />
        </Suspense>

        <TrustSection />
      </main>

      <Footer />

      {/* 添加首页结构化数据 */}
      <JsonLd
        data={{
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          'name': 'TractorData',
          'url': 'https://tractordata.site',
          'description': t('metaDescription'),
          'potentialAction': {
            '@type': 'SearchAction',
            'target': 'https://tractordata.site/search?q={search_term_string}',
            'query-input': 'required name=search_term_string'
          },
          'inLanguage': locale
        }}
      />

      {/* 添加组织结构化数据 */}
      <JsonLd
        data={{
          '@context': 'https://schema.org',
          '@type': 'Organization',
          'name': 'TractorData',
          'url': 'https://tractordata.site',
          'logo': 'https://tractordata.site/logo.svg',
          'sameAs': [
            'https://twitter.com/tractordata',
            'https://www.facebook.com/tractordata'
          ]
        }}
      />
    </div>
  );
}
