import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest, NextResponse } from 'next/server';

// 创建包含路径捕获功能的中间件
const intlMiddleware = createMiddleware({
  // 传递所有路由配置
  locales: routing.locales,
  defaultLocale: routing.defaultLocale,
  localePrefix: routing.localePrefix
});



/**
 * 验证 API 请求是否来自当前站点
 * @param request Next.js 请求对象
 * @returns null 如果请求有效; 否则返回带有错误状态的 NextResponse
 */
function validateApiOrigin(request: NextRequest): NextResponse | null {
  // 检查请求来源
  const referer = request.headers.get('referer');
  const origin = request.headers.get('origin');

  // 获取当前站点的主机名
  const url = new URL(request.url);
  const currentHost = url.host; // 例如 'localhost:3000' 或网站域名

  const isValidReferer = !referer || referer.includes(currentHost);
  const isValidOrigin = !origin || origin.includes(currentHost);

  // 如果既没有有效的Referer也没有有效的Origin，拒绝请求
  if (!isValidReferer && !isValidOrigin) {
    return NextResponse.json(
      { error: '未授权访问' },
      { status: 403 }
    );
  }

  // 请求有效，返回null继续处理
  return null;
}

// 主中间件函数
export async function middleware(request: NextRequest) {
  // 1. API 路由保护
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // 验证 API 请求来源
    const validationResult = validateApiOrigin(request);
    if (validationResult) return validationResult; // 如果无效，返回错误响应

    // API 请求有效，返回 next() 继续正常处理
    return NextResponse.next();
  }

  // 2. 管理后台路由 - 跳过国际化处理
  if (request.nextUrl.pathname.startsWith('/admin')) {
    return NextResponse.next();
  }

  // 3. 国际化中间件和路径捕获
  const response = intlMiddleware(request);

  // 添加当前路径到响应头中（为了 hreflang 生成）
  response.headers.set('x-current-path', request.nextUrl.pathname);

  return response;
}

// 使用更精细的 matcher 配置，按照 Next.js 最佳实践
export const config = {
  matcher: [
    // 1. 国际化路由匹配（排除静态资源和内部路由）
    '/((?!api|trpc|_next|_vercel|.*\\..*).*)',

    // 2. API 路由保护（只匹配 /api 开头的路由）
    '/api/:path*'
  ]
};
