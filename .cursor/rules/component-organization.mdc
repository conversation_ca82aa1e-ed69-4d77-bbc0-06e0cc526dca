---
description:
globs:
alwaysApply: true
---
# component-organization

组件组织遵循以下原则：

- 页面组件位于 `src/app/[locale]/*/page.tsx`
- 共享组件位于 `src/components` 目录
- 页面特定组件位于对应页面的 `components` 子目录
- 大型页面文件应拆分为多个小组件以提高可维护性

示例：品牌详情页组件结构
- [src/app/[locale]/brands/[slug]/page.tsx](mdc:src/app/[locale]/brands/[slug]/page.tsx) - 主页面文件
- [src/app/[locale]/brands/[slug]/components/](mdc:src/app/[locale]/brands/[slug]/components/) - 页面特定组件目录
  - BrandHeader.tsx - 品牌头部组件
  - BrandModels.tsx - 品牌型号组件
  - BrandHistory.tsx - 品牌历史组件
  - RelatedBrands.tsx - 相关品牌组件
  - HistoryTimeline.tsx - 历史时间线组件
  - index.ts - 导出所有组件