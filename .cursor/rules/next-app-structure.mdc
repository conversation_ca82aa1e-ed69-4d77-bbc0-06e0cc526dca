---
description:
globs:
alwaysApply: true
---
# next-app-structure

本项目是基于Next.js的国际化网站，主要结构如下：

- `src/app/[locale]` - 主要应用代码目录，使用动态路由支持多语言
- `src/i18n` - 国际化配置文件目录
- `messages` - 存放多语言翻译文件
- `public` - 静态资源，包括图标和图片

主要配置文件：
- [src/i18n/routing.ts](mdc:src/i18n/routing.ts) - 路由配置
- [src/i18n/navigation.ts](mdc:src/i18n/navigation.ts) - 导航工具
- [src/i18n/request.ts](mdc:src/i18n/request.ts) - 请求配置
- [src/middleware.ts](mdc:src/middleware.ts) - 中间件配置，处理语言检测和切换