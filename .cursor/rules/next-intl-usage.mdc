---
description:
globs:
alwaysApply: true
---
# next-intl-usage

使用next-intl（v4.0+）的最佳实践：

1. 服务器组件中的两种使用方式：
   - 异步组件：使用 `getTranslations` 等异步API
   - 非异步组件：直接使用 `useTranslations` 等hooks

2. 非异步服务器组件中的用法：
   ```typescript
   // 不需要 'use client'
   import { useTranslations } from 'next-intl';
   
   export function MyComponent() {
     const t = useTranslations('namespace');
     return <div>{t('key')}</div>;
   }
   ```

3. 异步组件中的用法：
   ```typescript
   // 异步服务器组件
   import { getTranslations } from 'next-intl/server';
   
   export async function MyAsyncComponent({ params }) {
     // 确保params已被解析
     const resolvedParams = await Promise.resolve(params);
     const t = await getTranslations({ 
       locale: resolvedParams.locale, 
       namespace: 'namespace' 
     });
     
     return <div>{t('key')}</div>;
   }
   ```

4. 客户端组件中的用法：
   ```typescript
   'use client';
   
   import { useTranslations } from 'next-intl';
   
   export function MyClientComponent() {
     const t = useTranslations('namespace');
     return <div>{t('key')}</div>;
   }
   ```

注意：在处理动态路由参数时，必须先使用 `await Promise.resolve(params)` 确保参数已被解析。