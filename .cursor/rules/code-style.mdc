---
description:
globs:
alwaysApply: true
---
# code-style

项目代码风格指南：

1. 最小化使用 `use client` 指令，优先使用服务器组件
2. 确保代码SEO友好
3. 所有页面需适配移动端
4. 组件拆分原则：
   - 功能独立的部分拆分为组件
   - 页面特定组件放在页面目录下
   - 共享组件放在全局components目录
5. 图片和图标使用：
   - 图片使用网络公共图片 https://picsum.photos
   - 图标自行绘制SVG，放在 public/icons 目录下
6. UI组件库：优先使用shadcn组件库
   - 安装组件的命令为 `npx shadcn@latest add xx`
7. 代码格式：
   - 使用TypeScript接口明确类型定义
   - 使用有意义的变量和函数名称
   - 适当添加注释说明复杂逻辑