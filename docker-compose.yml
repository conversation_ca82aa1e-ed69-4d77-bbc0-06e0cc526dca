services:
  tractor-data-web:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: tractor-data-web
    labels:
      - "com.docker.compose.project=tractor-data-web"
      - "cleanup.enable=true"
    ports:
      - "23001:3000"
    environment:
      - TZ=Asia/Shanghai
      - NODE_ENV=production
      - PORT=3000
      - NEXT_TELEMETRY_DISABLED=1
      # Node.js内存优化配置 - 平衡配置
      - NODE_OPTIONS=--max-old-space-size=512
      # 线程池优化
      - UV_THREADPOOL_SIZE=4
    volumes:
      # 只挂载日志和配置文件，避免挂载整个项目
      - ./logs:/app/logs
      - ./.env.production:/app/.env.production:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "health-check.js"]
      interval: 30s       # 降低检查频率，减少资源消耗
      timeout: 10s        # 增加超时时间
      retries: 2          # 减少重试次数
      start_period: 60s   # 增加启动等待时间
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
    deploy:
      resources:
        limits:
          memory: 1G      # 恢复到合理的内存限制
          cpus: '1.0'     # 合理的CPU限制
        reservations:
          memory: 512M    # 保证最小内存
          cpus: '0.5'     # 保证最小CPU
