# ===========================================
# 生产环境配置
# ===========================================

# 应用基础配置
NODE_ENV=production
PORT=3000
NEXT_TELEMETRY_DISABLED=1

# MongoDB 配置（生产环境）
MONGODB_URI=mongodb://n28.i.erlitech.com:27017
MONGODB_DB_NAME=tractor_data
MONGODB_USERNAME=root
MONGODB_PASSWORD=1q2w3e4R
MONGODB_AUTH_SOURCE=admin
MONGODB_MAX_POOL_SIZE=5
MONGODB_MIN_POOL_SIZE=1
MONGODB_CONNECT_TIMEOUT=10000
MONGODB_SERVER_SELECTION_TIMEOUT=5000

# 构建配置（生产环境 - 完整构建）
BUILD_MODE=full
BUILD_STATIC=false
ANALYZE=false

# 日志配置（生产环境 - 适中日志）
LOG_LEVEL=info
LOG_FILE_PATH=./logs
LOG_CONSOLE=false
LOG_FILE=true

# 缓存配置（生产环境 - 长缓存时间）
CACHE_REVALIDATE_TIME=3600
CACHE_ENABLED=true

# 站点配置
SITE_URL=https://tractordata.site
SITE_NAME=TractorData
DEFAULT_LOCALE=en
SUPPORTED_LOCALES=en,fr,zh,es,de,pt

# 安全配置（生产环境 - 严格配置）
API_RATE_LIMIT=100
CORS_ORIGIN=https://tractordata.site
SECURITY_HEADERS_ENABLED=true
IP_SALT=prod-salt-key-2024-secure

# 性能配置（生产环境）
IMAGES_UNOPTIMIZED=true
COMPRESS_ENABLED=true

# 开发工具配置（生产环境 - 禁用开发工具）
TURBOPACK_ENABLED=false
SKIP_TYPE_CHECK=false
SKIP_ESLINT=false

# PM2 配置（生产环境 - 最大实例）
PM2_INSTANCES=max
PM2_MAX_MEMORY=1G
PM2_APP_NAME=tractor-data-web

# 备份配置
BACKUP_DIR=./backups
AUTO_BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
