# 联盟广告系统设置指南

## 快速开始

### 一键设置（推荐）

```bash
npm run setup:affiliate
```

这个命令会自动：
1. 检查数据库连接
2. 将静态数据迁移到数据库
3. 确保数据结构完整
4. 提供后续步骤指导

### 手动设置

如果需要分步执行，可以使用以下命令：

```bash
# 1. 检查数据库连接
npm run check-db

# 2. 迁移静态数据到数据库
npm run migrate:static

# 3. 运行数据结构迁移
npm run migrate:affiliate

# 4. （可选）添加示例数据
npm run seed:affiliate
```

## 系统概览

### 数据迁移

您的静态联盟产品配置（`src/lib/affiliateData.ts`）将被迁移到 MongoDB 数据库中：

- **集合名称**: `affiliateProducts`
- **现有数据**: 4个预配置的产品
- **数据保留**: 现有的点击统计数据会被保留

### 管理界面

迁移完成后，您可以通过以下方式管理产品：

- **管理面板**: http://localhost:3000/admin/affiliate
- **功能**: 添加、编辑、删除产品
- **批量操作**: 启用/禁用、导入/导出
- **实时预览**: 编辑时查看效果

### 产品展示

产品会自动在以下页面显示：

- **首页**: 显示热门推荐产品
- **品牌页面**: 显示品牌相关产品
- **拖拉机详情页**: 显示相关配件和工具
- **新闻页面**: 显示教育性产品

## 验证设置

### 1. 检查数据库

```bash
# 连接到 MongoDB 并检查数据
mongo your-database-name
db.affiliateProducts.find().pretty()
```

### 2. 测试管理界面

1. 访问 http://localhost:3000/admin/affiliate
2. 确认可以看到迁移的产品
3. 尝试编辑一个产品
4. 测试批量操作功能

### 3. 测试前端显示

1. 访问网站首页
2. 检查是否显示联盟产品
3. 点击产品链接测试跟踪功能
4. 查看管理面板中的点击统计

## 故障排除

### 数据库连接问题

```bash
# 检查环境变量
cat .env.production | grep MONGODB

# 测试连接
npm run check-db
```

### 迁移失败

```bash
# 重新运行迁移
npm run migrate:static

# 检查错误日志
tail -f logs/migration.log
```

### 产品不显示

1. 检查产品是否启用：`enabled: true`
2. 检查目标页面设置：`targetPages`
3. 检查产品匹配逻辑：`brandMatch`, `modelMatch`

## 高级配置

### 添加新产品

通过管理界面添加，或者直接在数据库中插入：

```javascript
{
  id: 'unique-product-id',
  title: 'Product Title',
  description: 'Product Description',
  imageUrl: 'https://example.com/image.jpg',
  affiliateUrl: 'https://amazon.com/...',
  category: 'tractor', // tractor, equipment, parts, accessories
  enabled: true,
  rating: 4.5,
  reviewCount: 100,
  tags: ['tag1', 'tag2'],
  priority: 10,
  isHotPick: true,
  isOnSale: false,
  targetPages: ['home', 'brand'],
  brandMatch: ['john-deere'],
  modelMatch: [],
  clickCount: 0,
  impressionCount: 0,
  clickRate: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  translations: {
    'zh': { title: '中文标题', description: '中文描述' }
  }
}
```

### 自定义产品匹配

编辑 `src/lib/productMatcher.ts` 来自定义产品匹配逻辑：

- 修改品牌匹配规则
- 添加新的页面类型
- 调整产品评分算法

### 性能优化

```bash
# 创建数据库索引（自动执行）
db.affiliateProducts.createIndex({ "enabled": 1, "priority": -1 })
db.affiliateProducts.createIndex({ "targetPages": 1, "enabled": 1 })
```

## 支持

如果遇到问题：

1. 检查日志文件
2. 验证数据库连接
3. 确认环境变量设置
4. 重新运行迁移脚本

## 下一步

设置完成后，建议：

1. 添加更多产品到数据库
2. 配置产品匹配规则
3. 监控点击和转化数据
4. 优化产品展示策略