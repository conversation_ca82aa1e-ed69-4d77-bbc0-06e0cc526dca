#!/usr/bin/env node

/**
 * 联盟系统设置脚本
 * 一键设置整个联盟广告系统
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Setting up Affiliate System...\n');

async function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

async function setupAffiliateSystem() {
  try {
    // 1. 检查数据库连接
    await runCommand('npm run check-db', 'Checking database connection');
    
    // 2. 迁移静态数据到数据库
    await runCommand('npm run migrate:static', 'Migrating static affiliate data to database');
    
    // 3. 运行数据结构迁移（确保所有字段都存在）
    await runCommand('npm run migrate:affiliate', 'Running affiliate products migration');
    
    console.log('🎉 Affiliate System Setup Complete!\n');
    console.log('📝 Next Steps:');
    console.log('1. Visit http://localhost:3000/admin/affiliate to manage products');
    console.log('2. Check your website to see affiliate products displayed');
    console.log('3. Monitor clicks and performance in the admin panel');
    console.log('4. Add more products through the admin interface\n');
    
    console.log('💡 Useful Commands:');
    console.log('- npm run dev                 # Start development server');
    console.log('- npm run migrate:static      # Re-run static data migration');
    console.log('- npm run seed:affiliate      # Add sample products');
    console.log('- npm run check-db            # Check database connection\n');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure MongoDB is running and accessible');
    console.log('2. Check your .env.production file for correct database settings');
    console.log('3. Ensure you have the required permissions');
    console.log('4. Try running individual commands manually\n');
    process.exit(1);
  }
}

// 运行设置
if (require.main === module) {
  setupAffiliateSystem()
    .then(() => {
      console.log('✅ Setup script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Setup script failed:', error);
      process.exit(1);
    });
}

module.exports = { setupAffiliateSystem };