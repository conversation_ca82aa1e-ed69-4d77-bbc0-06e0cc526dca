#!/usr/bin/env node

/**
 * 联盟产品数据迁移脚本
 * 用于更新现有产品数据，添加新的字段和默认值
 */

const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.production' });

const MONGODB_URI = process.env.MONGODB_URI;
const DATABASE_NAME = process.env.MONGODB_DB || 'tractor-data';

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI environment variable is not set');
  process.exit(1);
}

async function migrateAffiliateProducts() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const collection = db.collection('affiliateProducts');
    
    console.log('📊 Checking existing products...');
    const totalProducts = await collection.countDocuments();
    console.log(`Found ${totalProducts} products to migrate`);
    
    if (totalProducts === 0) {
      console.log('✅ No products found, migration not needed');
      return;
    }
    
    // 获取所有产品
    const products = await collection.find({}).toArray();
    let migratedCount = 0;
    let skippedCount = 0;
    
    console.log('🔄 Starting migration...');
    
    for (const product of products) {
      const updates = {};
      let needsUpdate = false;
      
      // 检查并添加缺失的字段
      if (typeof product.rating !== 'number') {
        updates.rating = 0;
        needsUpdate = true;
      }
      
      if (typeof product.reviewCount !== 'number') {
        updates.reviewCount = 0;
        needsUpdate = true;
      }
      
      if (!Array.isArray(product.tags)) {
        updates.tags = [];
        needsUpdate = true;
      }
      
      if (typeof product.priority !== 'number') {
        updates.priority = 0;
        needsUpdate = true;
      }
      
      if (typeof product.isHotPick !== 'boolean') {
        updates.isHotPick = false;
        needsUpdate = true;
      }
      
      if (typeof product.isOnSale !== 'boolean') {
        updates.isOnSale = false;
        needsUpdate = true;
      }
      
      if (!Array.isArray(product.targetPages)) {
        updates.targetPages = ['home'];
        needsUpdate = true;
      }
      
      if (!Array.isArray(product.brandMatch)) {
        updates.brandMatch = [];
        needsUpdate = true;
      }
      
      if (!Array.isArray(product.modelMatch)) {
        updates.modelMatch = [];
        needsUpdate = true;
      }
      
      if (typeof product.clickCount !== 'number') {
        updates.clickCount = 0;
        needsUpdate = true;
      }
      
      if (typeof product.impressionCount !== 'number') {
        updates.impressionCount = 0;
        needsUpdate = true;
      }
      
      if (typeof product.clickRate !== 'number') {
        updates.clickRate = 0;
        needsUpdate = true;
      }
      
      if (!product.createdAt) {
        updates.createdAt = new Date();
        needsUpdate = true;
      }
      
      if (!product.updatedAt) {
        updates.updatedAt = new Date();
        needsUpdate = true;
      }
      
      if (typeof product.enabled !== 'boolean') {
        updates.enabled = true;
        needsUpdate = true;
      }
      
      // 验证分类字段
      const validCategories = ['tractor', 'equipment', 'parts', 'accessories'];
      if (!validCategories.includes(product.category)) {
        updates.category = 'tractor';
        needsUpdate = true;
      }
      
      // 如果需要更新
      if (needsUpdate) {
        updates.updatedAt = new Date();
        
        await collection.updateOne(
          { _id: product._id },
          { $set: updates }
        );
        
        migratedCount++;
        console.log(`✅ Migrated product: ${product.title}`);
      } else {
        skippedCount++;
      }
    }
    
    console.log('\n📈 Migration Summary:');
    console.log(`Total products: ${totalProducts}`);
    console.log(`Migrated: ${migratedCount}`);
    console.log(`Skipped (already up-to-date): ${skippedCount}`);
    
    // 创建索引以提高查询性能
    console.log('\n🔍 Creating indexes...');
    
    const indexes = [
      { key: { enabled: 1 }, name: 'enabled_1' },
      { key: { category: 1 }, name: 'category_1' },
      { key: { priority: -1 }, name: 'priority_-1' },
      { key: { isHotPick: 1 }, name: 'isHotPick_1' },
      { key: { isOnSale: 1 }, name: 'isOnSale_1' },
      { key: { targetPages: 1 }, name: 'targetPages_1' },
      { key: { brandMatch: 1 }, name: 'brandMatch_1' },
      { key: { modelMatch: 1 }, name: 'modelMatch_1' },
      { key: { clickCount: -1 }, name: 'clickCount_-1' },
      { key: { clickRate: -1 }, name: 'clickRate_-1' },
      { key: { createdAt: -1 }, name: 'createdAt_-1' },
      { key: { updatedAt: -1 }, name: 'updatedAt_-1' },
      { key: { tags: 1 }, name: 'tags_1' },
      // 复合索引
      { key: { enabled: 1, priority: -1 }, name: 'enabled_1_priority_-1' },
      { key: { category: 1, enabled: 1 }, name: 'category_1_enabled_1' },
      { key: { targetPages: 1, enabled: 1, priority: -1 }, name: 'targetPages_1_enabled_1_priority_-1' }
    ];
    
    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, { name: index.name, background: true });
        console.log(`✅ Created index: ${index.name}`);
      } catch (error) {
        if (error.code === 85) {
          console.log(`⚠️  Index already exists: ${index.name}`);
        } else {
          console.error(`❌ Failed to create index ${index.name}:`, error.message);
        }
      }
    }
    
    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await client.close();
  }
}

// 运行迁移
if (require.main === module) {
  migrateAffiliateProducts()
    .then(() => {
      console.log('✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateAffiliateProducts };