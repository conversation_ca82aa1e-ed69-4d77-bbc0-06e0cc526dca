#!/bin/bash

# Docker镜像清理脚本
# 用于自动清理旧的镜像文件，释放磁盘空间

set -e

echo "🧹 开始Docker清理..."

# 获取当前运行的容器
RUNNING_CONTAINERS=$(docker ps -q)
echo "当前运行的容器: $RUNNING_CONTAINERS"

# 清理dangling镜像（未标记的镜像）
echo "清理dangling镜像..."
DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
if [ ! -z "$DANGLING_IMAGES" ]; then
    docker rmi $DANGLING_IMAGES
    echo "✅ 已清理dangling镜像"
else
    echo "ℹ️  没有dangling镜像需要清理"
fi

# 清理未使用的镜像（保留最近的2个版本）
echo "清理旧的tractor-data-web镜像..."
OLD_IMAGES=$(docker images tractor-data-web --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | tail -n +2 | tail -n +3 | awk '{print $2}')
if [ ! -z "$OLD_IMAGES" ]; then
    echo $OLD_IMAGES | xargs docker rmi -f
    echo "✅ 已清理旧的应用镜像"
else
    echo "ℹ️  没有旧的应用镜像需要清理"
fi

# 清理未使用的网络
echo "清理未使用的网络..."
docker network prune -f
echo "✅ 已清理未使用的网络"

# 清理未使用的卷（谨慎操作）
echo "清理未使用的卷..."
docker volume prune -f
echo "✅ 已清理未使用的卷"

# 清理构建缓存（保留最近24小时的）
echo "清理构建缓存..."
docker builder prune -f --filter until=24h
echo "✅ 已清理构建缓存"

# 显示清理后的磁盘使用情况
echo "📊 清理后的Docker磁盘使用情况:"
docker system df

echo "🎉 Docker清理完成!"
