#!/usr/bin/env node

/**
 * 联盟产品示例数据生成脚本
 * 用于创建测试产品数据
 */

const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.production' });

const MONGODB_URI = process.env.MONGODB_URI;
const DATABASE_NAME = process.env.MONGODB_DB || 'tractor-data';

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI environment variable is not set');
  process.exit(1);
}

// 示例产品数据
const sampleProducts = [
  {
    title: "John Deere Tractor Maintenance Kit",
    description: "Complete maintenance kit for John Deere tractors including filters, oil, and essential parts for optimal performance.",
    imageUrl: "https://picsum.photos/400/400?random=1",
    affiliateUrl: "https://amazon.com/dp/example1",
    category: "parts",
    enabled: true,
    rating: 4.5,
    reviewCount: 127,
    tags: ["maintenance", "john-deere", "filters", "oil"],
    priority: 85,
    isHotPick: true,
    isOnSale: false,
    targetPages: ["home", "brand", "tractor"],
    brandMatch: ["john-deere"],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "约翰迪尔拖拉机维护套件",
        description: "约翰迪尔拖拉机完整维护套件，包括滤芯、机油和必要零件，确保最佳性能。"
      },
      "fr": {
        title: "Kit d'entretien pour tracteur John Deere",
        description: "Kit d'entretien complet pour tracteurs John Deere comprenant filtres, huile et pièces essentielles pour des performances optimales."
      }
    }
  },
  {
    title: "Heavy Duty Tractor Seat with Suspension",
    description: "Comfortable heavy-duty tractor seat with advanced suspension system, perfect for long working hours.",
    imageUrl: "https://picsum.photos/400/400?random=2",
    affiliateUrl: "https://amazon.com/dp/example2",
    category: "accessories",
    enabled: true,
    rating: 4.2,
    reviewCount: 89,
    tags: ["seat", "comfort", "suspension", "heavy-duty"],
    priority: 70,
    isHotPick: false,
    isOnSale: true,
    targetPages: ["home", "tractor"],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "重型拖拉机悬挂座椅",
        description: "舒适的重型拖拉机座椅，配备先进悬挂系统，适合长时间工作。"
      },
      "fr": {
        title: "Siège de tracteur robuste avec suspension",
        description: "Siège de tracteur robuste et confortable avec système de suspension avancé, parfait pour de longues heures de travail."
      }
    }
  },
  {
    title: "Kubota Engine Oil Filter Set",
    description: "Genuine Kubota engine oil filter set for optimal engine protection and performance.",
    imageUrl: "https://picsum.photos/400/400?random=3",
    affiliateUrl: "https://amazon.com/dp/example3",
    category: "parts",
    enabled: true,
    rating: 4.8,
    reviewCount: 203,
    tags: ["kubota", "oil-filter", "engine", "genuine"],
    priority: 90,
    isHotPick: true,
    isOnSale: true,
    targetPages: ["home", "brand"],
    brandMatch: ["kubota"],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "久保田发动机机油滤芯套装",
        description: "正品久保田发动机机油滤芯套装，提供最佳发动机保护和性能。"
      },
      "fr": {
        title: "Jeu de filtres à huile moteur Kubota",
        description: "Jeu de filtres à huile moteur Kubota authentique pour une protection et des performances optimales du moteur."
      }
    }
  },
  {
    title: "Professional Tractor Tool Set",
    description: "Complete professional tool set designed specifically for tractor maintenance and repair work.",
    imageUrl: "https://picsum.photos/400/400?random=4",
    affiliateUrl: "https://amazon.com/dp/example4",
    category: "equipment",
    enabled: true,
    rating: 4.3,
    reviewCount: 156,
    tags: ["tools", "maintenance", "repair", "professional"],
    priority: 60,
    isHotPick: false,
    isOnSale: false,
    targetPages: ["home", "news"],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "专业拖拉机工具套装",
        description: "专为拖拉机维护和修理工作设计的完整专业工具套装。"
      },
      "fr": {
        title: "Jeu d'outils professionnel pour tracteur",
        description: "Jeu d'outils professionnel complet conçu spécifiquement pour l'entretien et la réparation de tracteurs."
      }
    }
  },
  {
    title: "Case IH Hydraulic Fluid - 5 Gallon",
    description: "Premium hydraulic fluid specifically formulated for Case IH tractors and equipment.",
    imageUrl: "https://picsum.photos/400/400?random=5",
    affiliateUrl: "https://amazon.com/dp/example5",
    category: "parts",
    enabled: true,
    rating: 4.6,
    reviewCount: 74,
    tags: ["case-ih", "hydraulic-fluid", "premium", "5-gallon"],
    priority: 75,
    isHotPick: false,
    isOnSale: false,
    targetPages: ["brand", "tractor"],
    brandMatch: ["case-ih"],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "凯斯液压油 - 5加仑",
        description: "专为凯斯拖拉机和设备配制的优质液压油。"
      },
      "fr": {
        title: "Fluide hydraulique Case IH - 5 gallons",
        description: "Fluide hydraulique premium spécialement formulé pour les tracteurs et équipements Case IH."
      }
    }
  },
  {
    title: "Universal Tractor LED Light Bar",
    description: "High-intensity LED light bar suitable for all tractor brands, perfect for night operations.",
    imageUrl: "https://picsum.photos/400/400?random=6",
    affiliateUrl: "https://amazon.com/dp/example6",
    category: "accessories",
    enabled: true,
    rating: 4.4,
    reviewCount: 312,
    tags: ["led", "light-bar", "universal", "night-work"],
    priority: 65,
    isHotPick: false,
    isOnSale: true,
    targetPages: ["home", "tractor", "news"],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    translations: {
      "zh": {
        title: "通用拖拉机LED灯条",
        description: "适用于所有拖拉机品牌的高强度LED灯条，非常适合夜间作业。"
      },
      "fr": {
        title: "Barre lumineuse LED universelle pour tracteur",
        description: "Barre lumineuse LED haute intensité adaptée à toutes les marques de tracteurs, parfaite pour les opérations nocturnes."
      }
    }
  }
];

async function seedAffiliateProducts() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const collection = db.collection('affiliateProducts');
    
    console.log('📊 Checking existing products...');
    const existingCount = await collection.countDocuments();
    console.log(`Found ${existingCount} existing products`);
    
    // 检查是否已经有示例数据
    const existingSample = await collection.findOne({ 
      title: "John Deere Tractor Maintenance Kit" 
    });
    
    if (existingSample) {
      console.log('⚠️  Sample data already exists, skipping seed operation');
      console.log('Use --force flag to override existing data');
      return;
    }
    
    console.log('🌱 Seeding sample products...');
    
    const result = await collection.insertMany(sampleProducts);
    
    console.log(`✅ Successfully inserted ${result.insertedCount} sample products`);
    
    // 显示插入的产品信息
    console.log('\n📦 Inserted products:');
    sampleProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.title} (${product.category})`);
      console.log(`   Rating: ${product.rating}/5 (${product.reviewCount} reviews)`);
      console.log(`   Tags: ${product.tags.join(', ')}`);
      console.log(`   Hot Pick: ${product.isHotPick ? '🔥' : '❌'}, On Sale: ${product.isOnSale ? '💰' : '❌'}`);
      console.log('');
    });
    
    console.log('🎉 Seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await client.close();
  }
}

// 运行种子数据生成
if (require.main === module) {
  seedAffiliateProducts()
    .then(() => {
      console.log('✅ Seeding script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding script failed:', error);
      process.exit(1);
    });
}

module.exports = { seedAffiliateProducts };