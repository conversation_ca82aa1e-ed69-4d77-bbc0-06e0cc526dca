#!/bin/bash

# 零停机部署脚本
# 先构建新镜像，再无缝切换，确保服务不中断

set -e

echo "🚀 开始零停机部署..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 构建新镜像
echo "🔨 构建新镜像..."
if [ "$1" = "--clean" ]; then
    echo "🧹 清理构建（无缓存）..."
    docker compose build --no-cache
else
    docker compose build
fi
echo "✅ 镜像构建完成"

# 零停机部署：先启动新容器，健康检查通过后自动替换
echo "🔄 执行零停机部署..."

# 清理冲突的容器
echo "🧹 清理旧容器..."
docker compose down --remove-orphans 2>/dev/null || true

# 强制删除可能存在的同名容器
docker rm -f tractor-data-web 2>/dev/null || true

# 启动新容器（强制重新创建）
echo "▶️ 启动新容器..."
docker compose up -d --force-recreate

# 等待容器启动
echo "⏳ 等待容器启动..."
sleep 15

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..24}; do  # 2分钟检查
    if curl -f "http://localhost:23001/api/health" > /dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    fi

    if [ $i -eq 24 ]; then
        echo "❌ 健康检查失败"
        docker compose logs --tail=20
        exit 1
    fi

    echo "⏳ 等待健康检查通过... ($i/24)"
    sleep 5
done

# 执行清理
echo "🧹 执行清理..."
if [ -f "scripts/docker-cleanup.sh" ]; then
    chmod +x scripts/docker-cleanup.sh
    ./scripts/docker-cleanup.sh
else
    echo "⚠️  清理脚本不存在，执行基本清理..."
    docker system prune -f --filter until=24h
fi

# 部署完成
echo ""
echo "🎉 零停机部署完成！"
echo "🌐 访问地址: http://localhost:23001"
echo ""
echo "📊 容器状态:"
docker compose ps
echo ""
echo "📋 常用命令:"
echo "  make logs    # 查看日志"
echo "  make status  # 查看状态"
echo "  make health  # 健康检查"
echo "  make down    # 停止服务"
