#!/usr/bin/env node

/**
 * 静态联盟广告数据迁移脚本
 * 将 src/lib/affiliateData.ts 中的静态数据导入到 MongoDB 数据库
 */

const { MongoClient } = require('mongodb');
const path = require('path');
require('dotenv').config({ path: '.env.production' });

// 构建完整的 MongoDB URI
const MONGODB_HOST = process.env.MONGODB_URI || 'mongodb://n28.i.erlitech.com:27017';
const MONGODB_USERNAME = process.env.MONGODB_USERNAME;
const MONGODB_PASSWORD = process.env.MONGODB_PASSWORD;
const MONGODB_AUTH_SOURCE = process.env.MONGODB_AUTH_SOURCE || 'admin';
const DATABASE_NAME = process.env.MONGODB_DB_NAME || 'tractor_data';

// 构建带认证的 MongoDB URI
let MONGODB_URI;
if (MONGODB_USERNAME && MONGODB_PASSWORD) {
  MONGODB_URI = `mongodb://${MONGODB_USERNAME}:${MONGODB_PASSWORD}@${MONGODB_HOST.replace('mongodb://', '')}/${DATABASE_NAME}?authSource=${MONGODB_AUTH_SOURCE}`;
} else {
  MONGODB_URI = MONGODB_HOST;
}

if (!MONGODB_URI) {
  console.error('❌ MONGODB_URI environment variable is not set');
  process.exit(1);
}

// 静态数据（从 affiliateData.ts 复制）
const staticAffiliateProducts = [
  {
    id: 'tractor-toy-1',
    title: 'John Deere 1:16 Big Farm Tractor Model',
    description: 'Detailed scale model with authentic features, perfect for collectors and enthusiasts',
    imageUrl: 'https://m.media-amazon.com/images/I/713m9G8OeSL._SL300_.jpg',
    affiliateUrl: 'https://amzn.to/44Y4cZ3',
    category: 'tractor',
    enabled: true,
    rating: 4.5,
    reviewCount: 128,
    tags: ['john-deere', 'model', 'collectible', 'farm-tractor'],
    priority: 10,
    isHotPick: true,
    isOnSale: false,
    targetPages: ['home', 'brand', 'tractor'],
    brandMatch: ['john-deere'],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    translations: {
      'zh': {
        title: '约翰迪尔1:16农场拖拉机模型',
        description: '精致工艺制作，完美还原真实拖拉机细节，适合收藏和展示'
      },
      'en': {
        title: 'John Deere 1:16 Big Farm Tractor Model',
        description: 'Detailed scale model with authentic features, perfect for collectors and enthusiasts'
      },
      'fr': {
        title: 'Modèle de Tracteur John Deere 1:16',
        description: 'Modèle à l\'échelle détaillé avec des caractéristiques authentiques'
      },
      'es': {
        title: 'Modelo de Tractor John Deere 1:16',
        description: 'Modelo a escala detallado con características auténticas'
      },
      'de': {
        title: 'John Deere 1:16 Traktor Modell',
        description: 'Detailliertes Maßstabsmodell mit authentischen Merkmalen'
      },
      'pt': {
        title: 'Modelo de Trator John Deere 1:16',
        description: 'Modelo em escala detalhado com características autênticas'
      }
    }
  },
  {
    id: 'tractor-parts-1',
    title: 'Universal Tractor Hydraulic Filter Kit',
    description: 'High-quality filter kit for extended tractor life, compatible with multiple models',
    imageUrl: 'https://m.media-amazon.com/images/I/71bG0o1gDRL._SL300_.jpg',
    affiliateUrl: 'https://amzn.to/45b1zSr',
    category: 'parts',
    enabled: true,
    rating: 4.2,
    reviewCount: 89,
    tags: ['hydraulic', 'filter', 'maintenance', 'universal'],
    priority: 8,
    isHotPick: false,
    isOnSale: true,
    targetPages: ['home', 'tractor', 'brand'],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    translations: {
      'zh': {
        title: '通用拖拉机液压滤清器套装',
        description: '高品质滤清器，延长拖拉机使用寿命，适用于多种型号'
      },
      'en': {
        title: 'Universal Tractor Hydraulic Filter Kit',
        description: 'High-quality filter kit for extended tractor life, compatible with multiple models'
      },
      'fr': {
        title: 'Kit de Filtre Hydraulique Universel',
        description: 'Kit de filtre de haute qualité pour prolonger la vie du tracteur'
      },
      'es': {
        title: 'Kit de Filtro Hidráulico Universal',
        description: 'Kit de filtro de alta calidad para extender la vida del tractor'
      },
      'de': {
        title: 'Universal Hydraulikfilter-Set',
        description: 'Hochwertiges Filterset für längere Traktorlebensdauer'
      },
      'pt': {
        title: 'Kit de Filtro Hidráulico Universal',
        description: 'Kit de filtro de alta qualidade para estender a vida do trator'
      }
    }
  },
  {
    id: 'farm-equipment-1',
    title: 'Heavy Duty Farm Work Gloves',
    description: 'Essential protective gear for farm work, durable and slip-resistant',
    imageUrl: 'https://m.media-amazon.com/images/I/81gICNXWFgL._SL300_.jpg',
    affiliateUrl: 'https://amzn.to/44OER3B',
    category: 'accessories',
    enabled: true,
    rating: 4.7,
    reviewCount: 256,
    tags: ['gloves', 'safety', 'work-gear', 'farm'],
    priority: 7,
    isHotPick: false,
    isOnSale: false,
    targetPages: ['home', 'news'],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    translations: {
      'zh': {
        title: '重型农场工作手套',
        description: '农场作业专用防护装备，耐磨防滑，保护双手安全'
      },
      'en': {
        title: 'Heavy Duty Farm Work Gloves',
        description: 'Essential protective gear for farm work, durable and slip-resistant'
      },
      'fr': {
        title: 'Gants de Travail Agricole Robustes',
        description: 'Équipement de protection essentiel pour le travail agricole'
      },
      'es': {
        title: 'Guantes de Trabajo Agrícola Resistentes',
        description: 'Equipo de protección esencial para el trabajo agrícola'
      },
      'de': {
        title: 'Robuste Landwirtschafts-Arbeitshandschuhe',
        description: 'Unverzichtbare Schutzausrüstung für die Landwirtschaft'
      },
      'pt': {
        title: 'Luvas de Trabalho Agrícola Resistentes',
        description: 'Equipamento de proteção essencial para trabalho agrícola'
      }
    }
  },
  {
    id: 'tractor-book-1',
    title: 'Classic Tractors: Complete Encyclopedia',
    description: 'Comprehensive guide to classic tractor models from all major brands',
    imageUrl: 'https://m.media-amazon.com/images/P/0760370648.01._SL300_.jpg',
    affiliateUrl: 'https://amzn.to/3H65V57',
    category: 'accessories',
    enabled: true,
    rating: 4.3,
    reviewCount: 67,
    tags: ['book', 'encyclopedia', 'classic', 'reference'],
    priority: 6,
    isHotPick: false,
    isOnSale: false,
    targetPages: ['home', 'news', 'brand'],
    brandMatch: [],
    modelMatch: [],
    clickCount: 0,
    impressionCount: 0,
    clickRate: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    translations: {
      'zh': {
        title: '经典拖拉机：完整百科全书',
        description: '详细介绍各大品牌经典拖拉机车型，爱好者必备收藏'
      },
      'en': {
        title: 'Classic Tractors: Complete Encyclopedia',
        description: 'Comprehensive guide to classic tractor models from all major brands'
      },
      'fr': {
        title: 'Tracteurs Classiques: Encyclopédie Complète',
        description: 'Guide complet des modèles de tracteurs classiques de toutes les grandes marques'
      },
      'es': {
        title: 'Tractores Clásicos: Enciclopedia Completa',
        description: 'Guía completa de modelos de tractores clásicos de todas las marcas principales'
      },
      'de': {
        title: 'Klassische Traktoren: Vollständige Enzyklopädie',
        description: 'Umfassender Leitfaden für klassische Traktormodelle aller großen Marken'
      },
      'pt': {
        title: 'Tratores Clássicos: Enciclopédia Completa',
        description: 'Guia abrangente de modelos de tratores clássicos de todas as principais marcas'
      }
    }
  }
];

async function migrateStaticAffiliateData() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DATABASE_NAME);
    const collection = db.collection('affiliate_products');
    
    console.log('📊 Checking existing products...');
    const existingCount = await collection.countDocuments();
    console.log(`Found ${existingCount} existing products in database`);
    
    // 检查是否已经有静态数据
    const existingStaticProducts = await collection.find({
      id: { $in: staticAffiliateProducts.map(p => p.id) }
    }).toArray();
    
    console.log(`Found ${existingStaticProducts.length} existing static products`);
    
    let insertedCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    
    console.log('🔄 Starting migration...');
    
    for (const product of staticAffiliateProducts) {
      try {
        // 检查产品是否已存在
        const existingProduct = await collection.findOne({ id: product.id });
        
        if (existingProduct) {
          // 更新现有产品，但保留统计数据
          const updateData = {
            ...product,
            // 保留现有的统计数据
            clickCount: existingProduct.clickCount || 0,
            impressionCount: existingProduct.impressionCount || 0,
            clickRate: existingProduct.clickRate || 0,
            // 更新时间戳
            updatedAt: new Date()
          };
          
          await collection.updateOne(
            { id: product.id },
            { $set: updateData }
          );
          
          updatedCount++;
          console.log(`✅ Updated product: ${product.title}`);
        } else {
          // 插入新产品
          await collection.insertOne(product);
          insertedCount++;
          console.log(`✅ Inserted product: ${product.title}`);
        }
      } catch (error) {
        console.error(`❌ Failed to process product ${product.id}:`, error.message);
        skippedCount++;
      }
    }
    
    console.log('\n📈 Migration Summary:');
    console.log(`Total static products: ${staticAffiliateProducts.length}`);
    console.log(`Inserted: ${insertedCount}`);
    console.log(`Updated: ${updatedCount}`);
    console.log(`Skipped (errors): ${skippedCount}`);
    
    // 创建索引以提高查询性能
    console.log('\n🔍 Creating indexes...');
    
    const indexes = [
      { key: { id: 1 }, name: 'id_1', unique: true },
      { key: { enabled: 1 }, name: 'enabled_1' },
      { key: { category: 1 }, name: 'category_1' },
      { key: { priority: -1 }, name: 'priority_-1' },
      { key: { isHotPick: 1 }, name: 'isHotPick_1' },
      { key: { isOnSale: 1 }, name: 'isOnSale_1' },
      { key: { targetPages: 1 }, name: 'targetPages_1' },
      { key: { brandMatch: 1 }, name: 'brandMatch_1' },
      { key: { tags: 1 }, name: 'tags_1' },
      { key: { clickCount: -1 }, name: 'clickCount_-1' },
      { key: { createdAt: -1 }, name: 'createdAt_-1' },
      { key: { updatedAt: -1 }, name: 'updatedAt_-1' },
      // 复合索引
      { key: { enabled: 1, priority: -1 }, name: 'enabled_1_priority_-1' },
      { key: { category: 1, enabled: 1 }, name: 'category_1_enabled_1' },
      { key: { targetPages: 1, enabled: 1, priority: -1 }, name: 'targetPages_1_enabled_1_priority_-1' }
    ];
    
    for (const index of indexes) {
      try {
        await collection.createIndex(index.key, { 
          name: index.name, 
          background: true,
          unique: index.unique || false
        });
        console.log(`✅ Created index: ${index.name}`);
      } catch (error) {
        if (error.code === 85) {
          console.log(`⚠️  Index already exists: ${index.name}`);
        } else {
          console.error(`❌ Failed to create index ${index.name}:`, error.message);
        }
      }
    }
    
    // 验证迁移结果
    console.log('\n🔍 Verifying migration...');
    const finalCount = await collection.countDocuments();
    const enabledCount = await collection.countDocuments({ enabled: true });
    const hotPickCount = await collection.countDocuments({ isHotPick: true });
    const onSaleCount = await collection.countDocuments({ isOnSale: true });
    
    console.log(`Total products in database: ${finalCount}`);
    console.log(`Enabled products: ${enabledCount}`);
    console.log(`Hot pick products: ${hotPickCount}`);
    console.log(`On sale products: ${onSaleCount}`);
    
    // 显示产品分类统计
    const categoryStats = await collection.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]).toArray();
    
    console.log('\n📊 Products by category:');
    categoryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} products`);
    });
    
    console.log('\n🎉 Static data migration completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('1. Update your code to use database instead of static data');
    console.log('2. Test the affiliate product display on your website');
    console.log('3. Use the admin panel to manage products: /admin/affiliate');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await client.close();
  }
}

// 运行迁移
if (require.main === module) {
  migrateStaticAffiliateData()
    .then(() => {
      console.log('✅ Static data migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Static data migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateStaticAffiliateData };