#!/usr/bin/env node

/**
 * SEO监控脚本
 * 定期检查网站SEO状态并生成报告
 */

const fs = require('fs').promises;
const path = require('path');
const https = require('https');
const { URL } = require('url');

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const config = {
  baseUrl: process.env.SITE_URL || 'https://tractordata.site',
  outputPath: path.join(process.cwd(), 'seo-monitoring'),
  checkInterval: 24 * 60 * 60 * 1000, // 24小时
  endpoints: [
    '/',
    '/brands',
    '/tractors',
    '/news',
    '/sitemap.xml',
    '/robots.txt'
  ]
};

/**
 * 日志函数
 */
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleString();
  const prefix = {
    info: '📝',
    success: '✅',
    warn: '⚠️',
    error: '❌'
  }[type] || '📝';

  console.log(`${prefix} [${timestamp}] ${message}`);
}

/**
 * HTTP请求函数
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'SEO-Monitor/1.0 (TractorData)',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          responseTime: Date.now() - startTime
        });
      });
    });

    const startTime = Date.now();
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

/**
 * 检查页面SEO元素
 */
function analyzePage(html, url) {
  const analysis = {
    url,
    timestamp: new Date().toISOString(),
    title: null,
    description: null,
    keywords: null,
    h1Count: 0,
    h2Count: 0,
    imageCount: 0,
    imagesWithoutAlt: 0,
    internalLinks: 0,
    externalLinks: 0,
    hasCanonical: false,
    hasOpenGraph: false,
    hasTwitterCard: false,
    hasStructuredData: false,
    hasHreflang: false,
    issues: []
  };

  try {
    // 提取title
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    if (titleMatch) {
      analysis.title = titleMatch[1].trim();
      if (analysis.title.length < 30) {
        analysis.issues.push('Title too short (< 30 characters)');
      }
      if (analysis.title.length > 60) {
        analysis.issues.push('Title too long (> 60 characters)');
      }
    } else {
      analysis.issues.push('Missing title tag');
    }

    // 提取meta description
    const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']*)["\'][^>]*>/i);
    if (descMatch) {
      analysis.description = descMatch[1].trim();
      if (analysis.description.length < 120) {
        analysis.issues.push('Meta description too short (< 120 characters)');
      }
      if (analysis.description.length > 160) {
        analysis.issues.push('Meta description too long (> 160 characters)');
      }
    } else {
      analysis.issues.push('Missing meta description');
    }

    // 提取keywords
    const keywordsMatch = html.match(/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"']*)["\'][^>]*>/i);
    if (keywordsMatch) {
      analysis.keywords = keywordsMatch[1].trim();
    }

    // 计算标题标签
    analysis.h1Count = (html.match(/<h1[^>]*>/gi) || []).length;
    analysis.h2Count = (html.match(/<h2[^>]*>/gi) || []).length;

    if (analysis.h1Count === 0) {
      analysis.issues.push('Missing H1 tag');
    }
    if (analysis.h1Count > 1) {
      analysis.issues.push('Multiple H1 tags found');
    }

    // 检查图片
    const images = html.match(/<img[^>]*>/gi) || [];
    analysis.imageCount = images.length;
    analysis.imagesWithoutAlt = images.filter(img => !img.includes('alt=')).length;

    if (analysis.imagesWithoutAlt > 0) {
      analysis.issues.push(`${analysis.imagesWithoutAlt} images without alt text`);
    }

    // 检查链接
    const links = html.match(/<a[^>]*href=["\']([^"']*)["\'][^>]*>/gi) || [];
    links.forEach(link => {
      const hrefMatch = link.match(/href=["\']([^"']*)["\']/i);
      if (hrefMatch) {
        const href = hrefMatch[1];
        if (href.startsWith('http') && !href.includes(config.baseUrl)) {
          analysis.externalLinks++;
        } else if (href.startsWith('/') || href.includes(config.baseUrl)) {
          analysis.internalLinks++;
        }
      }
    });

    // 检查canonical
    analysis.hasCanonical = html.includes('rel="canonical"');

    // 检查Open Graph
    analysis.hasOpenGraph = html.includes('property="og:') || html.includes('property=\'og:');

    // 检查Twitter Card
    analysis.hasTwitterCard = html.includes('name="twitter:') || html.includes('name=\'twitter:');

    // 检查结构化数据
    analysis.hasStructuredData = html.includes('application/ld+json') || html.includes('schema.org');

    // 检查hreflang
    analysis.hasHreflang = html.includes('hreflang=');

    // 添加建议
    if (!analysis.hasCanonical) {
      analysis.issues.push('Missing canonical URL');
    }
    if (!analysis.hasOpenGraph) {
      analysis.issues.push('Missing Open Graph tags');
    }
    if (!analysis.hasStructuredData) {
      analysis.issues.push('Missing structured data');
    }

  } catch (error) {
    analysis.issues.push(`Analysis error: ${error.message}`);
  }

  return analysis;
}

/**
 * 检查单个端点
 */
async function checkEndpoint(endpoint) {
  const url = `${config.baseUrl}${endpoint}`;
  
  try {
    log(`检查: ${url}`);
    const response = await makeRequest(url);
    
    const result = {
      url,
      endpoint,
      statusCode: response.statusCode,
      responseTime: response.responseTime,
      contentLength: response.body.length,
      timestamp: new Date().toISOString(),
      headers: {
        contentType: response.headers['content-type'],
        cacheControl: response.headers['cache-control'],
        lastModified: response.headers['last-modified'],
        etag: response.headers['etag']
      },
      seo: null,
      issues: []
    };

    // 检查状态码
    if (response.statusCode !== 200) {
      result.issues.push(`HTTP ${response.statusCode} status code`);
    }

    // 检查响应时间
    if (response.responseTime > 3000) {
      result.issues.push(`Slow response time: ${response.responseTime}ms`);
    }

    // 分析HTML页面
    if (response.headers['content-type']?.includes('text/html')) {
      result.seo = analyzePage(response.body, url);
    }

    return result;
    
  } catch (error) {
    log(`检查失败 ${url}: ${error.message}`, 'error');
    return {
      url,
      endpoint,
      error: error.message,
      timestamp: new Date().toISOString(),
      issues: [`Request failed: ${error.message}`]
    };
  }
}

/**
 * 运行完整监控
 */
async function runMonitoring() {
  log('开始SEO监控...');
  
  const report = {
    timestamp: new Date().toISOString(),
    baseUrl: config.baseUrl,
    endpoints: [],
    summary: {
      totalEndpoints: config.endpoints.length,
      successfulChecks: 0,
      failedChecks: 0,
      totalIssues: 0,
      averageResponseTime: 0
    }
  };

  // 检查所有端点
  for (const endpoint of config.endpoints) {
    const result = await checkEndpoint(endpoint);
    report.endpoints.push(result);
    
    if (!result.error) {
      report.summary.successfulChecks++;
    } else {
      report.summary.failedChecks++;
    }
    
    const issueCount = (result.issues?.length || 0) + (result.seo?.issues?.length || 0);
    report.summary.totalIssues += issueCount;
  }

  // 计算平均响应时间
  const responseTimes = report.endpoints
    .filter(e => e.responseTime)
    .map(e => e.responseTime);
  
  if (responseTimes.length > 0) {
    report.summary.averageResponseTime = Math.round(
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
    );
  }

  // 保存报告
  await fs.mkdir(config.outputPath, { recursive: true });
  const reportPath = path.join(config.outputPath, `seo-monitor-${Date.now()}.json`);
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

  // 输出摘要
  log('SEO监控完成', 'success');
  console.log('\n📊 监控摘要:');
  console.log(`✅ 成功检查: ${report.summary.successfulChecks}/${report.summary.totalEndpoints}`);
  console.log(`❌ 失败检查: ${report.summary.failedChecks}`);
  console.log(`⚠️  总问题数: ${report.summary.totalIssues}`);
  console.log(`⏱️  平均响应时间: ${report.summary.averageResponseTime}ms`);
  console.log(`📄 报告保存到: ${reportPath}`);

  return report;
}

// 运行监控
if (require.main === module) {
  runMonitoring().catch(console.error);
}

module.exports = { runMonitoring };
