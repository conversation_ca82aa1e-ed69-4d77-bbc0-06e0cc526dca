#!/usr/bin/env node

/**
 * 改进的站点地图生成器
 * 使用环境配置系统，支持错误处理和重试机制
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const { create } = require('xmlbuilder2');

// 加载环境配置 - 按优先级加载
const nodeEnv = process.env.NODE_ENV || 'development';

// 按优先级尝试加载配置文件
const envFiles = [
    `.env.${nodeEnv}`,     // .env.production 或 .env.development
    '.env.local',          // 本地配置
    '.env'                 // 默认配置
];

let envLoaded = false;
for (const envFile of envFiles) {
    if (fsSync.existsSync(envFile)) {
        require('dotenv').config({ path: envFile });
        console.log(`📄 加载环境配置: ${envFile}`);
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    require('dotenv').config(); // 尝试默认加载
    console.log('⚠️  使用默认环境配置');
}

// 使用MongoDB直接连接
const { MongoClient } = require('mongodb');

// 配置
const config = {
    baseUrl: process.env.SITE_URL || 'http://localhost:3000',
    languages: (process.env.SUPPORTED_LOCALES || 'en,fr,zh,es,de,pt').split(','),
    outputPath: path.join(process.cwd(), 'public'),
    sitemapPath: path.join(process.cwd(), 'public', 'sitemaps'),
    maxRetries: 3,
    retryDelay: 2000,
};

// 静态路由
const staticRoutes = [
    { path: '', priority: 1.0 },
    { path: '/brands', priority: 0.9 },
    { path: '/tractors', priority: 0.9 },
    { path: '/compare', priority: 0.8 },
    { path: '/news', priority: 0.7 },
    { path: '/shows', priority: 0.7 },
    { path: '/about', priority: 0.7 },
    { path: '/contact', priority: 0.7 }
];

/**
 * 格式化日期为ISO格式
 */
function formatDate(date) {
    if (!date) return new Date().toISOString().split('T')[0];
    const d = new Date(date);
    return isNaN(d.getTime()) ? new Date().toISOString().split('T')[0] : d.toISOString().split('T')[0];
}

/**
 * 获取页面优先级
 */
function getPagePriority(path, type = 'static') {
    const priorityMap = {
        '': 1.0,                    // 首页
        '/brands': 0.9,             // 品牌列表
        '/tractors': 0.9,           // 拖拉机列表
        '/compare': 0.8,            // 比较工具
        '/news': 0.7,               // 新闻列表
        '/shows': 0.7,              // 展会
        '/about': 0.6,              // 关于我们
        '/contact': 0.6             // 联系我们
    };

    if (type === 'brand') return 0.8;
    if (type === 'model') return 0.6;
    if (type === 'news') return 0.5;

    return priorityMap[path] || 0.5;
}

/**
 * 获取更新频率
 */
function getChangeFreq(type = 'static') {
    const freqMap = {
        'static': 'monthly',
        'brand': 'weekly',
        'model': 'weekly',
        'news': 'daily'
    };

    return freqMap[type] || 'weekly';
}

/**
 * 生成多语言链接
 */
function generateAlternateRefs(pathWithoutLocale) {
    const alternateRefs = config.languages.map(lang => ({
        href: `${config.baseUrl}${lang === 'en' ? pathWithoutLocale : `/${lang}${pathWithoutLocale}`}`,
        hreflang: lang
    }));

    alternateRefs.push({
        href: `${config.baseUrl}${pathWithoutLocale}`,
        hreflang: 'x-default'
    });

    return alternateRefs;
}

/**
 * 创建URL对象
 */
function createUrlObject(pathWithoutLocale, lang, options = {}) {
    const path = lang === 'en' ? pathWithoutLocale : `/${lang}${pathWithoutLocale}`;
    const type = options.type || 'static';

    return {
        loc: path,
        changefreq: options.changefreq || getChangeFreq(type),
        priority: options.priority || getPagePriority(pathWithoutLocale, type),
        lastmod: formatDate(options.lastmod || new Date()),
        alternateRefs: generateAlternateRefs(pathWithoutLocale)
    };
}

/**
 * 生成站点地图XML
 */
function generateSitemapXML(urls) {
    const root = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('urlset', {
            xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9',
            'xmlns:xhtml': 'http://www.w3.org/1999/xhtml'
        });

    urls.forEach(urlData => {
        const urlElement = root.ele('url');
        urlElement.ele('loc').txt(config.baseUrl + urlData.loc);

        urlData.alternateRefs.forEach(ref => {
            urlElement.ele('xhtml:link', {
                rel: 'alternate',
                hreflang: ref.hreflang,
                href: ref.href
            });
        });

        if (urlData.lastmod) {
            urlElement.ele('lastmod').txt(urlData.lastmod);
        }
        if (urlData.changefreq) {
            urlElement.ele('changefreq').txt(urlData.changefreq);
        }
        if (urlData.priority !== undefined) {
            urlElement.ele('priority').txt(urlData.priority.toFixed(1));
        }
    });

    return root.end({ prettyPrint: true });
}

/**
 * 生成站点地图索引
 */
function generateSitemapIndex(sitemaps) {
    const root = create({ version: '1.0', encoding: 'UTF-8' })
        .ele('sitemapindex', {
            xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9'
        });

    sitemaps.forEach(sitemap => {
        const sitemapEle = root.ele('sitemap');
        sitemapEle.ele('loc').txt(sitemap.url);
        if (sitemap.lastmod) {
            sitemapEle.ele('lastmod').txt(sitemap.lastmod);
        }
    });

    return root.end({ prettyPrint: true });
}

/**
 * 重试机制
 */
async function withRetry(fn, retries = config.maxRetries) {
    for (let i = 0; i < retries; i++) {
        try {
            return await fn();
        } catch (error) {
            console.warn(`尝试 ${i + 1}/${retries} 失败:`, error.message);
            if (i === retries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, config.retryDelay));
        }
    }
}

/**
 * 连接数据库
 */
async function connectToDatabase() {
    const uri = process.env.MONGODB_URI;
    const dbName = process.env.MONGODB_DB_NAME;
    const username = process.env.MONGODB_USERNAME;
    const password = process.env.MONGODB_PASSWORD;
    const authSource = process.env.MONGODB_AUTH_SOURCE;

    // 检查必需的环境变量
    if (!uri) {
        throw new Error('MONGODB_URI 环境变量未设置');
    }
    if (!dbName) {
        throw new Error('MONGODB_DB_NAME 环境变量未设置');
    }
    if (!username) {
        throw new Error('MONGODB_USERNAME 环境变量未设置');
    }
    if (!password) {
        throw new Error('MONGODB_PASSWORD 环境变量未设置');
    }

    console.log(`🔗 连接数据库: ${uri.replace(password, '***')}`);

    try {
        const url = new URL(uri);
        url.username = username;
        url.password = password;
        url.pathname = `/${dbName}`;
        if (authSource) {
            url.searchParams.set('authSource', authSource);
        }

        const client = await MongoClient.connect(url.toString(), {
            maxPoolSize: 5,
            serverSelectionTimeoutMS: 10000,
            connectTimeoutMS: 10000,
        });

        console.log('✅ 数据库连接成功');
        return { client, db: client.db(dbName) };
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        throw error;
    }
}

/**
 * 获取数据库数据
 */
async function fetchData() {
    return await withRetry(async () => {
        const { client, db } = await connectToDatabase();
        
        console.log('正在获取数据库数据...');
        
        const [brands, models, news] = await Promise.all([
            db.collection('brands').find({ language: 'en' }).toArray(),
            db.collection('models').find({}).toArray(),
            db.collection('news').find({ language: 'en' }).toArray()
        ]);

        console.log(`获取到 ${brands.length} 个品牌, ${models.length} 个型号, ${news.length} 个新闻`);

        // 关闭数据库连接
        await client.close();

        return { brands, models, news };
    });
}

/**
 * 生成所有站点地图
 */
async function generateSitemaps() {
    console.log('开始生成站点地图...');
    
    try {
        // 获取数据
        const { brands, models, news } = await fetchData();
        
        const sitemapFiles = [];
        const currentDate = formatDate(new Date());
        
        // 确保输出目录存在
        await fs.mkdir(config.outputPath, { recursive: true });
        await fs.mkdir(config.sitemapPath, { recursive: true });
        
        // 生成各种类型的站点地图
        for (const lang of config.languages) {
            // 静态页面
            const staticUrls = staticRoutes.map(route =>
                createUrlObject(route.path, lang, {
                    priority: route.priority,
                    type: 'static'
                })
            );

            const staticFilename = `sitemap-static-${lang}.xml`;
            const staticContent = generateSitemapXML(staticUrls);
            await fs.writeFile(path.join(config.sitemapPath, staticFilename), staticContent);

            sitemapFiles.push({
                url: `${config.baseUrl}/sitemaps/${staticFilename}`,
                lastmod: currentDate
            });

            console.log(`生成 ${staticFilename} (${staticUrls.length} 个URL)`);

            // 品牌页面
            const brandUrls = brands.map(brand =>
                createUrlObject(`/brands/${brand.brand_key}`, lang, {
                    lastmod: brand.updatedAt,
                    type: 'brand'
                })
            );

            const brandFilename = `sitemap-brands-${lang}.xml`;
            const brandContent = generateSitemapXML(brandUrls);
            await fs.writeFile(path.join(config.sitemapPath, brandFilename), brandContent);

            sitemapFiles.push({
                url: `${config.baseUrl}/sitemaps/${brandFilename}`,
                lastmod: currentDate
            });

            console.log(`生成 ${brandFilename} (${brandUrls.length} 个URL)`);
            
            // 型号页面（分批处理大量数据）
            const batchSize = 10000;
            const modelBatches = Math.ceil(models.length / batchSize);
            
            for (let batch = 0; batch < modelBatches; batch++) {
                const start = batch * batchSize;
                const end = Math.min(start + batchSize, models.length);
                const batchModels = models.slice(start, end);
                
                const modelUrls = batchModels.map(model =>
                    createUrlObject(`/tractors/${model.brand_key}/${model.model_key}`, lang, {
                        lastmod: model.updatedAt,
                        type: 'model'
                    })
                );
                
                const modelFilename = modelBatches > 1 
                    ? `sitemap-models-${lang}-${batch + 1}.xml`
                    : `sitemap-models-${lang}.xml`;
                    
                const modelContent = generateSitemapXML(modelUrls);
                await fs.writeFile(path.join(config.sitemapPath, modelFilename), modelContent);

                sitemapFiles.push({
                    url: `${config.baseUrl}/sitemaps/${modelFilename}`,
                    lastmod: currentDate
                });
                
                console.log(`生成 ${modelFilename} (${modelUrls.length} 个URL)`);
            }
            
            // 新闻页面
            const newsUrls = news.map(article =>
                createUrlObject(`/news/${article.slug}`, lang, {
                    lastmod: article.updatedAt || article.date,
                    type: 'news'
                })
            );
            
            const newsFilename = `sitemap-news-${lang}.xml`;
            const newsContent = generateSitemapXML(newsUrls);
            await fs.writeFile(path.join(config.sitemapPath, newsFilename), newsContent);

            sitemapFiles.push({
                url: `${config.baseUrl}/sitemaps/${newsFilename}`,
                lastmod: currentDate
            });
            
            console.log(`生成 ${newsFilename} (${newsUrls.length} 个URL)`);
        }
        
        // 生成索引文件
        const indexContent = generateSitemapIndex(sitemapFiles);
        await fs.writeFile(path.join(config.outputPath, 'sitemap.xml'), indexContent);
        
        console.log(`生成站点地图索引文件 (${sitemapFiles.length} 个站点地图)`);
        
        // 更新 robots.txt
        await updateRobotsTxt();
        
        console.log('站点地图生成完成！');
        
    } catch (error) {
        console.error('生成站点地图时发生错误:', error);
        process.exit(1);
    }
}

/**
 * 更新 robots.txt
 */
async function updateRobotsTxt() {
    const robotsPath = path.join(config.outputPath, 'robots.txt');
    
    let robotsContent = `User-agent: *
Allow: /

# 禁止访问的路径
Disallow: /api/
Disallow: /_next/
Disallow: /404
Disallow: /500

# 站点地图
Sitemap: ${config.baseUrl}/sitemap.xml
`;

    await fs.writeFile(robotsPath, robotsContent);
    console.log('更新 robots.txt 完成');
}

// 运行生成器
if (require.main === module) {
    generateSitemaps().catch(console.error);
}

module.exports = { generateSitemaps };
