#!/usr/bin/env node

const { MongoClient } = require('mongodb');

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.MONGODB_DB_NAME || 'tractor_data';
const USERNAME = process.env.MONGODB_USERNAME || 'root';
const PASSWORD = process.env.MONGODB_PASSWORD || '';
const AUTH_SOURCE = process.env.MONGODB_AUTH_SOURCE || 'admin';

async function checkDatabase() {
  const client = new MongoClient(MONGODB_URI, {
    auth: {
      username: USERNAME,
      password: PASSWORD,
    },
    authSource: AUTH_SOURCE,
  });

  try {
    await client.connect();
    console.log('✅ 成功连接到数据库');
    
    const db = client.db(DB_NAME);
    
    // 检查各个集合的数据量
    console.log('\n📊 数据库统计信息:');
    console.log('=' * 50);
    
    // 品牌数据
    const brandStats = await Promise.all([
      db.collection('brands').countDocuments(),
      db.collection('brands').countDocuments({ language: 'en' }),
      db.collection('brands').countDocuments({ language: 'zh' }),
      db.collection('brands').distinct('language'),
      db.collection('brands').distinct('brand_key')
    ]);
    
    console.log(`\n🏭 品牌 (brands) 集合:`);
    console.log(`   总记录数: ${brandStats[0]}`);
    console.log(`   英文记录: ${brandStats[1]}`);
    console.log(`   中文记录: ${brandStats[2]}`);
    console.log(`   支持语言: ${brandStats[3].join(', ')}`);
    console.log(`   唯一品牌: ${brandStats[4].length}`);
    
    // 新闻数据
    const newsStats = await Promise.all([
      db.collection('news').countDocuments(),
      db.collection('news').countDocuments({ language: 'en' }),
      db.collection('news').countDocuments({ language: 'zh' }),
      db.collection('news').distinct('language')
    ]);
    
    console.log(`\n📰 新闻 (news) 集合:`);
    console.log(`   总记录数: ${newsStats[0]}`);
    console.log(`   英文记录: ${newsStats[1]}`);
    console.log(`   中文记录: ${newsStats[2]}`);
    console.log(`   支持语言: ${newsStats[3].join(', ')}`);
    
    // 型号数据
    const modelStats = await Promise.all([
      db.collection('models').countDocuments(),
      db.collection('models').distinct('brand_key'),
      db.collection('models').distinct('model_key')
    ]);
    
    console.log(`\n🚜 型号 (models) 集合:`);
    console.log(`   总记录数: ${modelStats[0]}`);
    console.log(`   涉及品牌: ${modelStats[1].length}`);
    console.log(`   唯一型号: ${modelStats[2].length}`);
    
    // 检查一些示例数据
    console.log(`\n📋 示例数据:`);
    
    const sampleBrands = await db.collection('brands')
      .find({ language: 'en' })
      .limit(5)
      .project({ brand_name: 1, brand_key: 1, tractor_type: 1 })
      .toArray();
    
    console.log(`   品牌示例:`);
    sampleBrands.forEach(brand => {
      console.log(`     - ${brand.brand_name} (${brand.brand_key}) [${brand.tractor_type}]`);
    });
    
    const sampleNews = await db.collection('news')
      .find({ language: 'en' })
      .limit(3)
      .project({ title: 1, slug: 1, date: 1 })
      .toArray();
    
    console.log(`   新闻示例:`);
    sampleNews.forEach(news => {
      console.log(`     - ${news.title} (${news.slug})`);
    });
    
    const sampleModels = await db.collection('models')
      .find({})
      .limit(5)
      .project({ model_name: 1, brand_key: 1, power: 1 })
      .toArray();
    
    console.log(`   型号示例:`);
    sampleModels.forEach(model => {
      console.log(`     - ${model.model_name} (${model.brand_key}) [${model.power}]`);
    });
    
    // 计算理论静态页面数量
    console.log(`\n🔢 理论静态页面计算:`);
    console.log('=' * 50);
    
    const languages = brandStats[3];
    const uniqueBrands = brandStats[4].length;
    const englishNews = newsStats[1];
    const totalModels = modelStats[0];
    
    console.log(`支持的语言数量: ${languages.length}`);
    console.log(`唯一品牌数量: ${uniqueBrands}`);
    console.log(`英文新闻数量: ${englishNews}`);
    console.log(`总型号数量: ${totalModels}`);
    
    // 计算各种模式下的页面数量
    const testModeBrands = Math.min(uniqueBrands, 10);
    const testModeNews = Math.min(englishNews, 5);
    const testModeModels = Math.min(totalModels, 10);
    
    const fullModeBrands = Math.min(uniqueBrands, 100);
    const fullModeNews = Math.min(englishNews, 50);
    const fullModeModels = Math.min(totalModels, 600);
    
    console.log(`\n📄 测试模式页面数量:`);
    console.log(`   品牌页面: ${testModeBrands} × ${languages.length} = ${testModeBrands * languages.length}`);
    console.log(`   新闻页面: ${testModeNews} × ${languages.length} = ${testModeNews * languages.length}`);
    console.log(`   型号页面: ${testModeModels} × ${languages.length} = ${testModeModels * languages.length}`);
    console.log(`   静态页面: ~20`);
    console.log(`   总计: ~${(testModeBrands + testModeNews + testModeModels) * languages.length + 20}`);
    
    console.log(`\n📄 完整模式页面数量:`);
    console.log(`   品牌页面: ${fullModeBrands} × ${languages.length} = ${fullModeBrands * languages.length}`);
    console.log(`   新闻页面: ${fullModeNews} × ${languages.length} = ${fullModeNews * languages.length}`);
    console.log(`   型号页面: ${fullModeModels} × ${languages.length} = ${fullModeModels * languages.length}`);
    console.log(`   静态页面: ~20`);
    console.log(`   总计: ~${(fullModeBrands + fullModeNews + fullModeModels) * languages.length + 20}`);
    
  } catch (error) {
    console.error('❌ 数据库连接或查询失败:', error);
  } finally {
    await client.close();
    console.log('\n✅ 数据库连接已关闭');
  }
}

// 运行检查
checkDatabase().catch(console.error);
