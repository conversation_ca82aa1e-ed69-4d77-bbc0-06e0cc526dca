#!/usr/bin/env node

/**
 * 简化的SEO优化器
 * 只生成核心的SEO文件
 */

const fs = require('fs').promises;
const path = require('path');

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

// SEO优化配置
const config = {
  outputPath: path.join(process.cwd(), 'public'),
  siteUrl: process.env.SITE_URL || 'https://tractordata.site',
  siteName: process.env.SITE_NAME || 'TractorData',
  languages: (process.env.SUPPORTED_LOCALES || 'en,fr,zh,es,de,pt').split(','),
};

/**
 * 日志输出函数
 */
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleString();
  const prefix = {
    info: '📝',
    success: '✅',
    warn: '⚠️',
    error: '❌'
  }[type] || '📝';

  console.log(`${prefix} [${timestamp}] ${message}`);
}

/**
 * 生成 robots.txt
 */
async function generateRobotsTxt() {
  log('生成 robots.txt...');

  const robotsContent = `# 允许主要搜索引擎爬虫
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Baiduspider
Allow: /

User-agent: YandexBot
Allow: /

# ==============================================
# 禁止AI训练爬虫
# ==============================================

# OpenAI相关爬虫
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

# Anthropic相关爬虫（日志中发现高频访问）
User-agent: ClaudeBot
Disallow: /

User-agent: Claude-Web
Disallow: /

User-agent: anthropic-ai
Disallow: /

# Meta AI爬虫（日志中发现多IP并发访问）
User-agent: meta-externalagent
Disallow: /

User-agent: FacebookBot
Disallow: /

# Google AI训练爬虫
User-agent: Google-Extended
Disallow: /

# 禁止Amazon爬虫
User-agent: Amazonbot
Disallow: /

# 其他AI训练爬虫
User-agent: CCBot
Disallow: /

User-agent: Applebot-Extended
Disallow: /

User-agent: PerplexityBot
Disallow: /

User-agent: MistralAI-User
Disallow: /

# ==============================================
# 其他常见问题爬虫
# ==============================================

# 邮箱收集爬虫
User-agent: EmailCollector
Disallow: /

User-agent: EmailSiphon
Disallow: /

# 内容复制爬虫
User-agent: WebCopier
Disallow: /

User-agent: HTTrack
Disallow: /

# ==============================================
# 默认规则
# ==============================================

# 其他未知爬虫默认禁止
User-agent: *
Disallow: /

# ==============================================
# 站点地图
# ==============================================

# 请替换为您的实际站点地图URL
Sitemap: ${config.siteUrl}/sitemap.xml

# ==============================================
# 爬取延迟设置（适用于允许的爬虫）
# ==============================================

# 为允许的搜索引擎设置合理的爬取延迟
Crawl-delay: 1
`;

  const robotsPath = path.join(config.outputPath, 'robots.txt');
  await fs.writeFile(robotsPath, robotsContent);
  log('robots.txt 生成完成', 'success');
}

/**
 * 生成 PWA manifest.json
 */
async function generateManifest() {
  log('生成 manifest.json...');

  const manifest = {
    name: config.siteName,
    short_name: config.siteName,
    description: "全球最全面的拖拉机数据库，提供详细的拖拉机规格、图片和比较工具。",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#22c55e",
    orientation: "portrait-primary",
    icons: [
      {
        src: "/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "maskable"
      },
      {
        src: "/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any"
      }
    ],
    categories: ["business", "productivity"],
    lang: "en",
    dir: "ltr"
  };

  const manifestPath = path.join(config.outputPath, 'manifest.json');
  await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
  log('manifest.json 生成完成', 'success');
}

/**
 * 验证生成的文件
 */
async function validateGeneratedFiles() {
  log('验证生成的文件...');

  const requiredFiles = [
    'robots.txt',
    'manifest.json'
  ];

  let allValid = true;

  for (const file of requiredFiles) {
    const filePath = path.join(config.outputPath, file);
    try {
      await fs.access(filePath);
      log(`✓ ${file} 存在`);
    } catch (error) {
      log(`✗ ${file} 缺失`, 'error');
      allValid = false;
    }
  }

  if (allValid) {
    log('所有SEO文件验证通过', 'success');
  } else {
    log('部分SEO文件验证失败', 'error');
  }

  return allValid;
}

/**
 * 生成SEO报告
 */
async function generateSEOReport() {
  log('生成SEO报告...');

  const report = {
    timestamp: new Date().toISOString(),
    site: config.siteName,
    url: config.siteUrl,
    languages: config.languages,
    files: {
      robotsTxt: false,
      manifest: false,
      sitemap: false
    },
    recommendations: []
  };

  // 检查文件是否存在
  try {
    await fs.access(path.join(config.outputPath, 'robots.txt'));
    report.files.robotsTxt = true;
  } catch (error) {
    report.recommendations.push('Generate robots.txt file');
  }

  try {
    await fs.access(path.join(config.outputPath, 'manifest.json'));
    report.files.manifest = true;
  } catch (error) {
    report.recommendations.push('Generate manifest.json file');
  }

  try {
    await fs.access(path.join(config.outputPath, 'sitemap.xml'));
    report.files.sitemap = true;
  } catch (error) {
    report.recommendations.push('Generate sitemap.xml file');
  }

  // 保存报告
  const reportPath = path.join(config.outputPath, 'seo-report.json');
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

  log(`SEO报告已保存到: ${reportPath}`, 'success');
  return report;
}

/**
 * 主函数
 */
async function main() {
  log('开始SEO优化流程...');

  try {
    // 确保输出目录存在
    await fs.mkdir(config.outputPath, { recursive: true });

    // 执行核心SEO优化任务
    await generateRobotsTxt();
    await generateManifest();

    // 验证生成的文件
    const isValid = await validateGeneratedFiles();

    // 生成SEO报告
    const report = await generateSEOReport();

    if (isValid) {
      log('SEO优化完成！所有文件已生成并验证通过', 'success');

      // 输出摘要
      console.log('\n📊 SEO优化摘要:');
      console.log(`✅ robots.txt: ${report.files.robotsTxt ? '已生成' : '未生成'}`);
      console.log(`✅ manifest.json: ${report.files.manifest ? '已生成' : '未生成'}`);
      console.log(`✅ sitemap.xml: ${report.files.sitemap ? '已检测' : '未检测'}`);

      if (report.recommendations.length > 0) {
        console.log('\n💡 建议:');
        report.recommendations.forEach(rec => console.log(`  - ${rec}`));
      }
    } else {
      log('SEO优化完成，但部分文件验证失败', 'warn');
    }

  } catch (error) {
    log(`SEO优化过程中发生错误: ${error.message}`, 'error');
    process.exit(1);
  }
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
SEO优化工具

用法:
  node scripts/seo-optimizer-simple.js

功能:
  - 生成 robots.txt
  - 生成 PWA manifest.json
  - 验证生成的文件

选项:
  -h, --help     显示帮助信息

环境变量:
  SITE_URL       网站基础URL (默认: http://localhost:3000)
  SITE_NAME      网站名称 (默认: TractorData)
  SUPPORTED_LOCALES  支持的语言 (默认: en,fr,zh,es,de,pt)
`);
  process.exit(0);
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
