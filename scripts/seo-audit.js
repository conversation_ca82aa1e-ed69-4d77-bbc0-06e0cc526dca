#!/usr/bin/env node

/**
 * SEO审计工具
 * 自动检查网站的SEO配置和性能
 */

const fs = require('fs').promises;
const path = require('path');
const { create } = require('xmlbuilder2');

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const config = {
  baseUrl: process.env.SITE_URL || 'https://tractordata.site',
  outputPath: path.join(process.cwd(), 'seo-audit-report.json'),
  publicPath: path.join(process.cwd(), 'public'),
  srcPath: path.join(process.cwd(), 'src'),
};

/**
 * SEO审计结果接口
 */
class SEOAuditResult {
  constructor() {
    this.timestamp = new Date().toISOString();
    this.score = 0;
    this.maxScore = 0;
    this.checks = [];
    this.warnings = [];
    this.errors = [];
    this.recommendations = [];
  }

  addCheck(name, passed, weight = 1, message = '') {
    this.checks.push({
      name,
      passed,
      weight,
      message,
      timestamp: new Date().toISOString()
    });
    
    this.maxScore += weight;
    if (passed) {
      this.score += weight;
    }
  }

  addWarning(message) {
    this.warnings.push({
      message,
      timestamp: new Date().toISOString()
    });
  }

  addError(message) {
    this.errors.push({
      message,
      timestamp: new Date().toISOString()
    });
  }

  addRecommendation(message, priority = 'medium') {
    this.recommendations.push({
      message,
      priority,
      timestamp: new Date().toISOString()
    });
  }

  getScorePercentage() {
    return this.maxScore > 0 ? Math.round((this.score / this.maxScore) * 100) : 0;
  }

  getGrade() {
    const percentage = this.getScorePercentage();
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  }
}

/**
 * 检查robots.txt文件
 */
async function checkRobotsTxt(audit) {
  try {
    const robotsPath = path.join(config.publicPath, 'robots.txt');
    const robotsContent = await fs.readFile(robotsPath, 'utf-8');
    
    audit.addCheck('robots.txt exists', true, 2);
    
    // 检查是否包含sitemap
    if (robotsContent.includes('Sitemap:')) {
      audit.addCheck('robots.txt contains sitemap', true, 2);
    } else {
      audit.addCheck('robots.txt contains sitemap', false, 2, 'Add Sitemap directive to robots.txt');
    }
    
    // 检查是否允许主要搜索引擎
    const allowsGooglebot = robotsContent.includes('User-agent: Googlebot');
    audit.addCheck('robots.txt allows Googlebot', allowsGooglebot, 3);
    
    // 检查是否禁止AI爬虫
    const blocksAI = robotsContent.includes('GPTBot') || robotsContent.includes('ClaudeBot');
    audit.addCheck('robots.txt blocks AI crawlers', blocksAI, 1);
    
  } catch (error) {
    audit.addCheck('robots.txt exists', false, 2, 'robots.txt file not found');
    audit.addError(`robots.txt check failed: ${error.message}`);
  }
}

/**
 * 检查sitemap.xml文件
 */
async function checkSitemap(audit) {
  try {
    const sitemapPath = path.join(config.publicPath, 'sitemap.xml');
    const sitemapContent = await fs.readFile(sitemapPath, 'utf-8');
    
    audit.addCheck('sitemap.xml exists', true, 3);
    
    // 检查XML格式
    try {
      const doc = create(sitemapContent);
      audit.addCheck('sitemap.xml is valid XML', true, 2);
    } catch (xmlError) {
      audit.addCheck('sitemap.xml is valid XML', false, 2, 'Invalid XML format');
    }
    
    // 检查是否包含多语言链接
    const hasHreflang = sitemapContent.includes('xhtml:link') && sitemapContent.includes('hreflang');
    audit.addCheck('sitemap contains hreflang', hasHreflang, 2);
    
    // 检查lastmod日期
    const hasLastmod = sitemapContent.includes('<lastmod>');
    audit.addCheck('sitemap contains lastmod', hasLastmod, 1);
    
  } catch (error) {
    audit.addCheck('sitemap.xml exists', false, 3, 'sitemap.xml file not found');
    audit.addError(`sitemap.xml check failed: ${error.message}`);
  }
}

/**
 * 检查manifest.json文件
 */
async function checkManifest(audit) {
  try {
    const manifestPath = path.join(config.publicPath, 'manifest.json');
    const manifestContent = await fs.readFile(manifestPath, 'utf-8');
    const manifest = JSON.parse(manifestContent);
    
    audit.addCheck('manifest.json exists', true, 2);
    
    // 检查必需字段
    const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color'];
    requiredFields.forEach(field => {
      const hasField = manifest[field] !== undefined;
      audit.addCheck(`manifest has ${field}`, hasField, 1);
    });
    
    // 检查图标
    const hasIcons = manifest.icons && manifest.icons.length > 0;
    audit.addCheck('manifest has icons', hasIcons, 2);
    
    if (hasIcons) {
      const has192 = manifest.icons.some(icon => icon.sizes && icon.sizes.includes('192x192'));
      const has512 = manifest.icons.some(icon => icon.sizes && icon.sizes.includes('512x512'));
      audit.addCheck('manifest has 192x192 icon', has192, 1);
      audit.addCheck('manifest has 512x512 icon', has512, 1);
    }
    
  } catch (error) {
    audit.addCheck('manifest.json exists', false, 2, 'manifest.json file not found or invalid');
    audit.addError(`manifest.json check failed: ${error.message}`);
  }
}

/**
 * 检查页面组件的SEO实现
 */
async function checkPageComponents(audit) {
  try {
    // 检查layout.tsx
    const layoutPath = path.join(config.srcPath, 'app', '[locale]', 'layout.tsx');
    const layoutContent = await fs.readFile(layoutPath, 'utf-8');
    
    // 检查是否有generateMetadata
    const hasGenerateMetadata = layoutContent.includes('generateMetadata');
    audit.addCheck('layout has generateMetadata', hasGenerateMetadata, 3);
    
    // 检查是否有hreflang实现
    const hasHreflang = layoutContent.includes('alternates') && layoutContent.includes('languages');
    audit.addCheck('layout implements hreflang', hasHreflang, 3);
    
    // 检查是否有结构化数据
    const hasJsonLd = layoutContent.includes('JsonLd') || layoutContent.includes('application/ld+json');
    audit.addCheck('layout includes structured data', hasJsonLd, 2);
    
    // 检查是否有Web Vitals监控
    const hasWebVitals = layoutContent.includes('WebVitals') || layoutContent.includes('web-vitals');
    audit.addCheck('layout includes Web Vitals monitoring', hasWebVitals, 2);
    
  } catch (error) {
    audit.addError(`Page components check failed: ${error.message}`);
  }
}

/**
 * 检查SEO组件
 */
async function checkSEOComponents(audit) {
  const componentsPath = path.join(config.srcPath, 'components');
  
  try {
    const files = await fs.readdir(componentsPath);
    
    // 检查关键SEO组件
    const seoComponents = [
      'JsonLd.tsx',
      'BreadcrumbSchema.tsx',
      'ProductSchema.tsx',
      'OrganizationSchema.tsx',
      'ArticleSchema.tsx',
      'WebVitals.tsx'
    ];
    
    for (const component of seoComponents) {
      const exists = files.includes(component);
      audit.addCheck(`${component} exists`, exists, 1);
    }
    
  } catch (error) {
    audit.addError(`SEO components check failed: ${error.message}`);
  }
}

/**
 * 检查多语言配置
 */
async function checkMultilingualConfig(audit) {
  try {
    // 检查i18n配置
    const routingPath = path.join(config.srcPath, 'i18n', 'routing.ts');
    const routingContent = await fs.readFile(routingPath, 'utf-8');
    
    audit.addCheck('i18n routing config exists', true, 2);
    
    // 检查支持的语言数量
    const localesMatch = routingContent.match(/locales:\s*\[(.*?)\]/s);
    if (localesMatch) {
      const locales = localesMatch[1].split(',').length;
      audit.addCheck('supports multiple languages', locales > 1, 2, `Supports ${locales} languages`);
    }
    
    // 检查消息文件
    const messagesPath = path.join(process.cwd(), 'messages');
    const messageFiles = await fs.readdir(messagesPath);
    const hasMultipleMessages = messageFiles.length > 1;
    audit.addCheck('has multiple message files', hasMultipleMessages, 1);
    
  } catch (error) {
    audit.addError(`Multilingual config check failed: ${error.message}`);
  }
}

/**
 * 生成SEO建议
 */
function generateRecommendations(audit) {
  const scorePercentage = audit.getScorePercentage();
  
  if (scorePercentage < 70) {
    audit.addRecommendation('Consider implementing missing SEO components to improve search visibility', 'high');
  }
  
  if (audit.errors.length > 0) {
    audit.addRecommendation('Fix critical SEO errors to prevent indexing issues', 'high');
  }
  
  if (audit.warnings.length > 3) {
    audit.addRecommendation('Address SEO warnings to optimize search performance', 'medium');
  }
  
  // 基于具体检查项的建议
  const failedChecks = audit.checks.filter(check => !check.passed);
  
  if (failedChecks.some(check => check.name.includes('sitemap'))) {
    audit.addRecommendation('Ensure sitemap.xml is properly configured and accessible', 'high');
  }
  
  if (failedChecks.some(check => check.name.includes('hreflang'))) {
    audit.addRecommendation('Implement proper hreflang tags for international SEO', 'medium');
  }
  
  if (failedChecks.some(check => check.name.includes('structured data'))) {
    audit.addRecommendation('Add structured data to improve rich snippets in search results', 'medium');
  }
}

/**
 * 主审计函数
 */
async function runSEOAudit() {
  console.log('🔍 Starting SEO Audit...\n');
  
  const audit = new SEOAuditResult();
  
  try {
    await checkRobotsTxt(audit);
    await checkSitemap(audit);
    await checkManifest(audit);
    await checkPageComponents(audit);
    await checkSEOComponents(audit);
    await checkMultilingualConfig(audit);
    
    generateRecommendations(audit);
    
    // 保存报告
    await fs.writeFile(config.outputPath, JSON.stringify(audit, null, 2));
    
    // 输出结果
    console.log('📊 SEO Audit Results');
    console.log('===================');
    console.log(`Score: ${audit.score}/${audit.maxScore} (${audit.getScorePercentage()}%)`);
    console.log(`Grade: ${audit.getGrade()}`);
    console.log(`Checks: ${audit.checks.filter(c => c.passed).length}/${audit.checks.length} passed`);
    console.log(`Warnings: ${audit.warnings.length}`);
    console.log(`Errors: ${audit.errors.length}`);
    console.log(`\nReport saved to: ${config.outputPath}`);
    
    if (audit.errors.length > 0) {
      console.log('\n❌ Critical Errors:');
      audit.errors.forEach(error => console.log(`  - ${error.message}`));
    }
    
    if (audit.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      audit.recommendations.forEach(rec => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
        console.log(`  ${priority} ${rec.message}`);
      });
    }
    
  } catch (error) {
    console.error('❌ SEO Audit failed:', error);
    process.exit(1);
  }
}

// 运行审计
if (require.main === module) {
  runSEOAudit().catch(console.error);
}

module.exports = { runSEOAudit, SEOAuditResult };
