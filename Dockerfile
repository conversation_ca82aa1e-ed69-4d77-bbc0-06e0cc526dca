# 多阶段构建 Dockerfile
# 阶段1: 依赖安装
FROM node:22-alpine AS deps
WORKDIR /app

# 复制包管理文件
COPY package*.json ./
COPY ecosystem.config.js ./

# 安装依赖（包括 PM2）
RUN npm ci --only=production && \
    npm install pm2 -g && \
    npm cache clean --force

# 阶段2: 构建阶段
FROM node:22-alpine AS builder
WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 构建项目
RUN npm run build

# 生成 SEO 文件
RUN npm run sitemap || true
RUN npm run seo:optimize || true

# 阶段3: 运行时镜像
FROM node:22-alpine AS runner
WORKDIR /app

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 安装 PM2（在切换用户之前）
RUN npm install pm2 -g

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/ecosystem.config.js ./ecosystem.config.js
COPY --from=builder /app/start-server.js ./start-server.js
COPY --from=builder /app/health-check.js ./health-check.js
COPY --from=builder /app/scripts ./scripts

# 安装运行时需要的 dotenv 依赖
RUN npm install dotenv

# 复制环境配置文件
COPY --from=builder /app/.env.production ./

# 创建日志目录并设置权限
RUN mkdir -p logs && \
    chmod 755 logs && \
    chown -R nextjs:nodejs /app && \
    chmod -R 755 /app

USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
  CMD node health-check.js || exit 1

# 设置环境变量并启动
ENV NODE_ENV=production
CMD ["pm2-runtime", "start", "ecosystem.config.js"]
