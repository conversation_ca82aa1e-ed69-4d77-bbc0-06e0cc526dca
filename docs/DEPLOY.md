# 零停机部署

## 🚀 快速部署

```bash
# 零停机部署
./scripts/zero-downtime-deploy.sh

# 或使用npm脚本
npm run deploy

# 清理缓存部署
./scripts/zero-downtime-deploy.sh --clean
```

## 🔄 工作原理

1. **构建新镜像** - 在后台构建，不影响当前服务
2. **启动新容器** - 使用新镜像启动容器
3. **健康检查** - 确保新容器正常工作
4. **自动切换** - Docker自动替换旧容器

## ⏱️ 时间说明

- **构建时间**: ~10分钟（首次或清理构建）
- **切换时间**: ~15秒（容器启动时间）
- **服务中断**: 0秒（零停机）

## 📋 常用命令

```bash
# 查看容器状态
docker compose ps

# 查看实时日志
docker compose logs -f

# 停止服务
docker compose down

# 重启服务
docker compose restart
```

## 🛠️ 故障排除

如果部署失败：

1. 检查Docker是否运行
2. 确保端口23001未被占用
3. 检查磁盘空间是否充足
4. 查看容器日志：`docker compose logs`
