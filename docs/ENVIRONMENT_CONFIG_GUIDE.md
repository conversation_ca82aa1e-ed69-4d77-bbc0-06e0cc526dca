# 🔧 环境配置系统使用指南

## 📋 概述

本项目采用了完整的环境配置系统，支持不同部署环境的配置管理，确保敏感信息的安全性和配置的灵活性。

## 📁 配置文件结构

```
├── .env.example          # 配置模板文件（包含所有可用配置项）
├── .env.development      # 开发环境配置
├── .env.production       # 生产环境配置
├── .env.local           # 本地环境配置（当前项目实际配置）
├── src/lib/env.ts       # 环境配置管理工具
└── ENVIRONMENT_CONFIG_GUIDE.md  # 本说明文档
```

## 🔐 安全性说明

### 受保护的文件
- `.env.local` - 包含实际敏感信息，**不会**提交到版本控制
- 任何 `.env.*.local` 文件 - 本地覆盖配置

### 可提交的文件
- `.env.example` - 配置模板，不包含敏感信息
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

## 🚀 快速开始

### 1. 首次设置

```bash
# 复制配置模板
cp .env.example .env.local

# 编辑配置文件，填入实际值
nano .env.local
```

### 2. 必需配置项

在 `.env.local` 中必须配置以下项目：

```bash
# MongoDB 数据库配置
MONGODB_URI=mongodb://your-host:27017
MONGODB_DB_NAME=your_database
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password
MONGODB_AUTH_SOURCE=admin
```

### 3. 验证配置

```bash
# 检查数据库连接
npm run check-db

# 启动开发服务器
npm run dev
```

## 📊 配置项详解

### 🗄️ 数据库配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `MONGODB_URI` | MongoDB 连接地址 | `mongodb://localhost:27017` |
| `MONGODB_DB_NAME` | 数据库名称 | `tractor_data` |
| `MONGODB_USERNAME` | 数据库用户名 | `root` |
| `MONGODB_PASSWORD` | 数据库密码 | `your_password` |
| `MONGODB_AUTH_SOURCE` | 认证数据库 | `admin` |
| `MONGODB_MAX_POOL_SIZE` | 最大连接池大小 | `10` |
| `MONGODB_MIN_POOL_SIZE` | 最小连接池大小 | `2` |

### 🏗️ 构建配置

| 配置项 | 说明 | 可选值 |
|--------|------|--------|
| `BUILD_MODE` | 构建模式 | `development`, `test`, `full` |
| `BUILD_STATIC` | 是否静态导出 | `true`, `false` |
| `ANALYZE` | 是否分析构建 | `true`, `false` |

### 🌐 站点配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `SITE_URL` | 网站基础URL | `http://localhost:3000` |
| `SITE_NAME` | 网站名称 | `TractorData` |
| `DEFAULT_LOCALE` | 默认语言 | `en` |
| `SUPPORTED_LOCALES` | 支持的语言 | `en,fr,zh,es,de,pt` |

### 🔧 PM2 配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `PM2_INSTANCES` | PM2 实例数量 | `max`, `4` |
| `PM2_MAX_MEMORY` | 最大内存限制 | `1G`, `512M` |
| `PM2_APP_NAME` | 应用名称 | `tractor-data-web` |

## 🌍 环境特定配置

### 开发环境 (.env.development)

```bash
NODE_ENV=development
BUILD_MODE=development
LOG_LEVEL=debug
PM2_INSTANCES=2
CACHE_REVALIDATE_TIME=60
```

**特点**：
- 快速构建
- 详细日志
- 短缓存时间
- 少量PM2实例

### 生产环境 (.env.production)

```bash
NODE_ENV=production
BUILD_MODE=full
LOG_LEVEL=info
PM2_INSTANCES=max
CACHE_REVALIDATE_TIME=3600
```

**特点**：
- 完整构建
- 适中日志
- 长缓存时间
- 最大PM2实例

### 本地环境 (.env.local)

基于当前项目的实际配置，包含真实的数据库连接信息。

## 🛠️ 使用方法

### 1. 开发模式

```bash
# 使用开发环境配置
NODE_ENV=development npm run dev

# 或者直接运行（会自动加载 .env.local）
npm run dev
```

### 2. 生产部署

```bash
# 使用生产环境配置
NODE_ENV=production npm run deploy

# 或者分步执行
npm run build
npm run pm2:start
```

### 3. 环境切换

```bash
# 临时使用不同环境
NODE_ENV=development npm run build
NODE_ENV=production npm run build
```

## 🔍 配置验证

### 自动验证

项目启动时会自动验证必需的配置项：

```typescript
// src/lib/env.ts 中的验证逻辑
export function validateEnvConfig(): void {
  const config = getEnvConfig();
  const errors: string[] = [];

  if (!config.MONGODB_URI) {
    errors.push('MONGODB_URI 是必需的');
  }
  // ... 其他验证
}
```

### 手动检查

```bash
# 检查数据库连接
npm run check-db

# 查看当前配置
node -e "console.log(require('./src/lib/env').env)"
```

## 🚨 故障排除

### 常见问题

#### 1. 数据库连接失败

**错误**：`环境配置验证失败: MONGODB_PASSWORD 是必需的`

**解决方案**：
```bash
# 检查 .env.local 文件是否存在
ls -la .env.local

# 确保包含数据库密码
grep MONGODB_PASSWORD .env.local
```

#### 2. PM2 启动失败

**错误**：`PM2 无法读取配置`

**解决方案**：
```bash
# 确保 dotenv 已安装
npm install dotenv

# 重新启动 PM2
npm run pm2:stop
npm run pm2:start
```

#### 3. 构建模式不生效

**错误**：构建时没有使用预期的模式

**解决方案**：
```bash
# 明确指定构建模式
BUILD_MODE=full npm run build

# 或在 .env.local 中设置
echo "BUILD_MODE=full" >> .env.local
```

### 调试技巧

```bash
# 查看所有环境变量
printenv | grep -E "(MONGODB|BUILD|PM2)"

# 测试配置加载
node -e "
require('dotenv').config();
console.log('MONGODB_URI:', process.env.MONGODB_URI);
console.log('BUILD_MODE:', process.env.BUILD_MODE);
"
```

## 📝 最佳实践

### 1. 配置管理

- ✅ 使用 `.env.local` 存储本地配置
- ✅ 定期更新 `.env.example` 模板
- ✅ 不要在代码中硬编码敏感信息
- ❌ 不要提交包含密码的配置文件

### 2. 安全性

- 🔐 定期更换数据库密码
- 🔐 使用强密码
- 🔐 限制数据库访问权限
- 🔐 定期审查配置文件

### 3. 部署

- 🚀 部署前验证配置
- 🚀 使用环境特定的配置文件
- 🚀 监控配置变更
- 🚀 备份重要配置

## 📞 支持

如果遇到配置问题：

1. 检查本文档的故障排除部分
2. 验证 `.env.local` 文件格式
3. 运行 `npm run check-db` 测试数据库连接
4. 查看应用日志：`pm2 logs tractor-data-web`

---

**配置系统已就绪！** 🎉 您的拖拉机数据网站现在具备了完整的环境配置管理能力。
