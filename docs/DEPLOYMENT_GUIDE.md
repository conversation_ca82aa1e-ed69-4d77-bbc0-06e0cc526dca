# 🚀 TractorData.site 部署指南

## 📋 目录
- [环境要求](#环境要求)
- [PM2进程说明](#pm2进程说明)
- [数据库统计](#数据库统计)
- [常用命令](#常用命令)
- [部署流程](#部署流程)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🔧 环境要求

### 系统要求
- **Node.js**: v18.17.0 或更高版本
- **npm**: v9.6.7 或更高版本
- **PM2**: 全局安装 (`npm install -g pm2`)
- **MongoDB**: 连接到远程数据库
- **内存**: 建议 4GB 以上
- **CPU**: 多核处理器（PM2会使用所有核心）

### 环境变量
确保以下环境变量已配置：
```bash
NODE_ENV=production
MONGODB_URI=mongodb://n28.i.erlitech.com:27017
BUILD_MODE=full  # 或 test
```

## 🔄 PM2进程说明

### 为什么有两种name？

您看到的两种进程名称是因为：

1. **`tractor-data-web`** (ID: 11-21) - **当前活跃的进程**
   - 这是通过 `ecosystem.config.js` 启动的正确进程
   - 运行在集群模式，11个实例
   - 内存使用正常 (~70MB/实例)
   - 状态健康

2. **`tractor-web`** (ID: 0-10) - **旧的进程**
   - 这是之前测试时启动的进程
   - 内存显示0b，说明已经不活跃
   - 已经清理删除

### PM2配置说明
```javascript
// ecosystem.config.js
{
  name: 'tractor-data-web',        // 进程名称
  instances: 'max',                // 使用所有CPU核心
  exec_mode: 'cluster',            // 集群模式
  max_memory_restart: '1G',        // 内存超过1GB自动重启
  autorestart: true                // 自动重启
}
```

## 📊 数据库统计

### 实际数据量
- **品牌总数**: 2011条记录
  - 英文: 334个品牌
  - 中文: 340个品牌
  - 支持语言: 6种 (de, en, es, fr, pt, zh)
  - 唯一品牌: 335个

- **新闻总数**: 94条记录
  - 仅英文版本
  - 支持语言: 1种 (en)

- **型号总数**: 16056条记录
  - 涉及品牌: 328个
  - 唯一型号: 16056个

### 静态页面生成数量

#### 测试模式 (BUILD_MODE=test)
- 品牌页面: 10 × 6语言 = 60页
- 新闻页面: 5 × 6语言 = 30页  
- 型号页面: 10 × 6语言 = 60页
- 静态页面: ~20页
- **总计: ~170页**

#### 完整模式 (BUILD_MODE=full)
- 品牌页面: 100 × 6语言 = 600页
- 新闻页面: 50 × 6语言 = 300页
- 型号页面: 600 × 6语言 = 3600页
- 静态页面: ~20页
- **总计: ~4520页**

## 🛠️ 常用命令

### 开发和构建
```bash
# 开发模式
npm run dev

# 构建项目
npm run build

# 检查数据库
npm run check-db
```

### PM2管理
```bash
# 启动服务
npm run pm2:start

# 查看状态
npm run pm2:status

# 重启服务
npm run pm2:restart

# 停止服务
npm run pm2:stop
```

### SEO和维护
```bash
# 生成SEO文件
npm run seo:optimize

# 生成站点地图
npm run sitemap

# 检查数据库连接
npm run check-db
```

### 一键部署
```bash
# 完整部署流程
npm run deploy
# 等同于: build + sitemap + seo:optimize + pm2:restart
```

## 🚀 部署流程

### 1. 首次部署
```bash
# 1. 克隆代码
git clone <repository-url>
cd tractor-data-web

# 2. 安装依赖
npm ci

# 3. 构建项目
npm run build

# 4. 生成SEO文件
npm run sitemap
npm run seo:optimize

# 5. 启动PM2
npm run pm2:start

# 6. 检查数据库连接
npm run check-db
```

### 2. 更新部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖（如有）
npm ci

# 3. 一键部署
npm run deploy

# 4. 检查服务状态
npm run pm2:status
```

### 3. 快速重启
```bash
# 仅重启服务（不重新构建）
npm run pm2:restart
```

## 📈 监控和维护

### 性能监控
- **健康检查**: `http://localhost:3000/api/health`
- **PM2监控**: `pm2 monit`
- **日志查看**: `pm2 logs tractor-data-web`

### 定期维护
```bash
# 检查数据库连接
npm run check-db

# 查看PM2状态
npm run pm2:status
```

### 日志管理
```bash
# 查看实时日志
pm2 logs tractor-data-web

# 清理日志
pm2 flush

# 重载日志配置
pm2 reload tractor-data-web
```

## 🔧 故障排除

### 常见问题

#### 1. PM2进程异常
```bash
# 查看进程状态
npm run pm2:status

# 重启所有实例
npm run pm2:restart

# 如果还有问题，停止后重新启动
npm run pm2:stop
npm run pm2:start
```

#### 2. 内存使用过高
```bash
# 检查内存使用
pm2 monit

# 重启高内存实例
pm2 restart <process-id>
```

#### 3. 构建失败
```bash
# 检查数据库连接
npm run check-db

# 清理缓存后重试
rm -rf .next
npm run build
```

#### 4. 健康检查失败
```bash
# 检查PM2状态
npm run pm2:status

# 查看详细日志
pm2 logs tractor-data-web --lines 50

# 重启服务
npm run pm2:restart
```

### 紧急恢复
```bash
# 重启PM2服务
npm run pm2:restart

# 查看详细日志
pm2 logs tractor-data-web --lines 100
```

## 📝 最佳实践

### 1. 部署前检查
- ✅ 数据库连接正常
- ✅ 代码已提交到版本控制
- ✅ 依赖已更新
- ✅ 环境变量已配置

### 2. 部署后验证
- ✅ PM2状态正常
- ✅ 健康检查通过
- ✅ 网站可正常访问
- ✅ 关键功能测试

### 3. 监控要点
- 📊 内存使用 (<1GB/实例)
- 📊 CPU使用 (<80%)
- 📊 响应时间 (<500ms)
- 📊 错误率 (<1%)

### 4. 维护计划
- 🔄 每日: 检查PM2状态
- 🔄 每周: 创建备份
- 🔄 每月: 更新依赖
- 🔄 每季度: 性能优化

---

## 🎯 快速参考

### 最常用命令
```bash
npm run deploy          # 一键部署
npm run pm2:status      # 查看状态
npm run check-db        # 检查数据库
npm run sitemap         # 生成站点地图
```

### 紧急情况
```bash
npm run pm2:restart     # 重启服务
npm run check-db        # 检查数据库
pm2 logs tractor-data-web  # 查看日志
```

---

**部署成功！** 🎉 您的拖拉机数据网站现在运行在高性能的PM2集群模式下，具备完整的监控和备份机制。
