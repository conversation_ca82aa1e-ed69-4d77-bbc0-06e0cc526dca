/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        tractor: {
          DEFAULT: 'var(--color-tractor)',
          yellow: 'var(--color-tractor-yellow)'
        },
      },
    },
  },
  safelist: [
    'text-tractor',
    'bg-tractor',
    'from-tractor',
    'to-tractor/90',
    'hover:bg-tractor/90',
  ],
  plugins: [],
} 