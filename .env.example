# ===========================================
# 拖拉机数据网站 - 环境配置模板
# ===========================================
# 复制此文件为 .env.local 并填入实际配置值
# 不要将包含敏感信息的 .env 文件提交到版本控制

# ===========================================
# 应用基础配置
# ===========================================
NODE_ENV=development
PORT=3000
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# MongoDB 数据库配置
# ===========================================
# MongoDB 连接 URI（不包含认证信息）
MONGODB_URI=mongodb://localhost:27017

# 数据库名称
MONGODB_DB_NAME=tractor_data

# 数据库认证信息
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password
MONGODB_AUTH_SOURCE=admin

# 连接池配置
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2

# 连接超时配置（毫秒）
MONGODB_CONNECT_TIMEOUT=10000
MONGODB_SERVER_SELECTION_TIMEOUT=5000

# ===========================================
# 构建和部署配置
# ===========================================
# 构建模式: test | full | development
BUILD_MODE=development

# 静态导出模式
BUILD_STATIC=false

# 构建优化
ANALYZE=false

# ===========================================
# 日志配置
# ===========================================
# 日志级别: error | warn | info | debug
LOG_LEVEL=info

# 日志文件路径
LOG_FILE_PATH=./logs

# 是否启用控制台日志
LOG_CONSOLE=true

# 是否启用文件日志
LOG_FILE=true

# ===========================================
# 缓存配置
# ===========================================
# 页面重新验证时间（秒）
CACHE_REVALIDATE_TIME=3600

# 是否启用缓存
CACHE_ENABLED=true

# ===========================================
# SEO 和站点配置
# ===========================================
# 网站基础 URL
SITE_URL=http://localhost:3000

# 网站名称
SITE_NAME=TractorData

# 默认语言
DEFAULT_LOCALE=en

# 支持的语言（逗号分隔）
SUPPORTED_LOCALES=en,fr,zh,es,de,pt

# ===========================================
# 安全配置
# ===========================================
# API 访问限制
API_RATE_LIMIT=100

# 跨域配置
CORS_ORIGIN=*

# 安全头配置
SECURITY_HEADERS_ENABLED=true

# ===========================================
# 性能配置
# ===========================================
# 图片优化
IMAGES_UNOPTIMIZED=true

# 压缩配置
COMPRESS_ENABLED=true

# ===========================================
# 开发配置
# ===========================================
# 是否启用 Turbopack
TURBOPACK_ENABLED=true

# 是否跳过类型检查
SKIP_TYPE_CHECK=true

# 是否跳过 ESLint 检查
SKIP_ESLINT=true

# ===========================================
# PM2 配置
# ===========================================
# PM2 实例数量 (max 表示使用所有 CPU 核心)
PM2_INSTANCES=max

# PM2 最大内存重启阈值
PM2_MAX_MEMORY=1G

# PM2 应用名称
PM2_APP_NAME=tractor-data-web

# ===========================================
# 第三方服务配置
# ===========================================
# 如果使用外部图片服务
# EXTERNAL_IMAGE_DOMAINS=images.unsplash.com,picsum.photos

# 如果使用 CDN
# CDN_URL=

# 如果使用分析服务
# ANALYTICS_ID=

# ===========================================
# 备份和恢复配置
# ===========================================
# 备份目录
BACKUP_DIR=./backups

# 是否启用自动备份
AUTO_BACKUP_ENABLED=false

# 备份保留天数
BACKUP_RETENTION_DAYS=7
