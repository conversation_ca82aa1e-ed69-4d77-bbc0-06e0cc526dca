# Makefile for tractor-data-web project

.PHONY: help build up down restart logs clean deploy health status

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  build        - 构建Docker镜像"
	@echo "  up           - 启动容器"
	@echo "  down         - 停止容器"
	@echo "  restart      - 重启容器"
	@echo "  logs         - 查看日志"
	@echo "  clean        - 清理Docker资源"
	@echo "  deploy       - 零停机部署（推荐）"
	@echo "  deploy-clean - 零停机部署（无缓存构建）"
	@echo "  health       - 检查应用健康状态"
	@echo "  status       - 查看容器状态"

# 构建镜像
build:
	@echo "🔨 构建Docker镜像..."
	docker compose build --no-cache

# 启动容器
up:
	@echo "▶️  启动容器..."
	docker compose up -d

# 停止容器
down:
	@echo "⏹️  停止容器..."
	docker compose down

# 重启容器
restart: down up

# 查看日志
logs:
	@echo "📋 查看容器日志..."
	docker compose logs -f --tail=50

# 清理Docker资源
clean:
	@echo "🧹 清理Docker资源..."
	@if [ -f "scripts/docker-cleanup.sh" ]; then \
		chmod +x scripts/docker-cleanup.sh && ./scripts/docker-cleanup.sh; \
	else \
		docker system prune -f --filter until=24h; \
	fi

# 零停机部署
deploy:
	@echo "🚀 开始零停机部署..."
	@if [ -f "scripts/zero-downtime-deploy.sh" ]; then \
		chmod +x scripts/zero-downtime-deploy.sh && ./scripts/zero-downtime-deploy.sh; \
	else \
		make down && make build && make up && make clean; \
	fi

# 快速部署（无缓存构建）
deploy-clean:
	@echo "🚀 开始零停机部署（清理构建）..."
	@if [ -f "scripts/zero-downtime-deploy.sh" ]; then \
		chmod +x scripts/zero-downtime-deploy.sh && ./scripts/zero-downtime-deploy.sh --clean; \
	else \
		make down && make build && make up && make clean; \
	fi

# 检查健康状态
health:
	@echo "🔍 检查应用健康状态..."
	@curl -s http://localhost:23001/api/health | jq . || echo "健康检查失败或jq未安装"

# 查看容器状态
status:
	@echo "📊 容器状态:"
	@docker compose ps
	@echo ""
	@echo "📊 资源使用:"
	@docker stats --no-stream tractor-data-web || echo "容器未运行"
