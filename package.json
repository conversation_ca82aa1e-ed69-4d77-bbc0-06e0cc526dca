{"name": "tractor-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "sitemap": "node scripts/generate-sitemap.js", "seo:optimize": "node scripts/seo-optimizer.js", "seo:audit": "node scripts/seo-audit.js", "seo:full": "npm run seo:optimize && npm run sitemap && npm run seo:audit", "deploy": "./scripts/zero-downtime-deploy.sh", "check-db": "node scripts/check-database.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.4", "@tailwindcss/postcss": "^4.1.10", "@types/negotiator": "^0.6.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.6.3", "lucide-react": "^0.487.0", "mongodb": "^6.15.0", "negotiator": "^1.0.0", "next": "15.2.5", "next-intl": "^4.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.5", "web-vitals": "^5.0.3", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.5", "typescript": "^5"}}