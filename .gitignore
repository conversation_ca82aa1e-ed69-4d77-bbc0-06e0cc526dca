# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (保护敏感信息)
.env
.env.local
.env.*.local
# 允许提交环境模板和示例文件
!.env.example
!.env.development
!.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Editor directories and files
.idea
.kiro
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

/logs/
*.log
